"""Test script for the "打开资产路径目录" functionality.

This script tests the new right-click menu option to open asset directories
in the file explorer.
"""

# Import built-in modules
import os
import tempfile
import unittest
from unittest.mock import Mock
from unittest.mock import patch

# Import third-party modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_grid_view import AssetGridView
from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_list_model import AssetListModel


class TestOpenAssetFolder(unittest.TestCase):
    """Test cases for the open asset folder functionality."""

    def setUp(self):
        """Set up test fixtures."""
        # Create QApplication if it doesn't exist
        if not QtWidgets.QApplication.instance():
            self.app = QtWidgets.QApplication([])
        else:
            self.app = QtWidgets.QApplication.instance()

        # Create test components
        self.model = AssetListModel()
        self.view = AssetGridView()
        self.view.setModel(self.model)

        # Create temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()
        self.test_file_path = os.path.join(self.temp_dir, "test_asset.fbx")

        # Create a test file
        with open(self.test_file_path, "w") as f:
            f.write("test content")

        # Create test asset data
        self.test_asset = {
            "id": "test_asset_001",
            "name": "Test Hair Asset",
            "asset_type": "card",
            "file_path": self.test_file_path,
            "thumbnail": None,
            "metadata": {
                "file_path": self.test_file_path,
                "reference": None,
            },
        }

    def tearDown(self):
        """Clean up test fixtures."""
        # Clean up temporary files
        if os.path.exists(self.test_file_path):
            os.remove(self.test_file_path)
        if os.path.exists(self.temp_dir):
            os.rmdir(self.temp_dir)

    def test_open_asset_folder_with_valid_path(self):
        """Test opening asset folder with valid file path."""
        # Set up test data
        self.model.setAssets([self.test_asset])

        # Mock os.startfile to avoid actually opening file explorer
        with patch(
            "cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_grid_view.os.startfile"
        ) as mock_startfile:
            # Call the method
            self.view._open_asset_folder(self.test_asset)

            # Verify os.startfile was called with correct directory
            expected_dir = os.path.dirname(self.test_file_path)
            mock_startfile.assert_called_once_with(expected_dir)

    def test_open_asset_folder_with_missing_file_path(self):
        """Test opening asset folder when file_path is missing."""
        # Create asset without file_path in metadata
        asset_without_path = {
            "id": "test_asset_002",
            "name": "Test Asset Without Path",
            "asset_type": "card",
            "metadata": {},
        }

        # Mock logger to capture warning
        with patch.object(self.view, "_logger") as mock_logger:
            # Call the method
            self.view._open_asset_folder(asset_without_path)

            # Verify warning was logged
            mock_logger.warning.assert_called_once()

    def test_open_asset_folder_with_nonexistent_directory(self):
        """Test opening asset folder when directory doesn't exist."""
        # Create asset with non-existent file path
        nonexistent_path = "/nonexistent/path/asset.fbx"
        asset_with_bad_path = {
            "id": "test_asset_003",
            "name": "Test Asset Bad Path",
            "asset_type": "card",
            "metadata": {
                "file_path": nonexistent_path,
            },
        }

        # Mock logger to capture warning
        with patch.object(self.view, "_logger") as mock_logger:
            # Call the method
            self.view._open_asset_folder(asset_with_bad_path)

            # Verify warning was logged
            mock_logger.warning.assert_called_once()

    def test_context_menu_contains_open_folder_action(self):
        """Test that context menu contains the open folder action."""
        # Set up test data
        self.model.setAssets([self.test_asset])

        # Create a mock context menu event
        pos = QtCore.QPoint(10, 10)
        global_pos = QtCore.QPoint(100, 100)

        # Mock QMenu to capture menu actions
        with patch(
            "cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_grid_view.QtWidgets.QMenu"
        ) as mock_menu_class:
            mock_menu = Mock()
            mock_menu_class.return_value = mock_menu

            # Mock indexAt to return valid index
            with patch.object(self.view, "indexAt") as mock_index_at:
                mock_index = Mock()
                mock_index.isValid.return_value = True
                mock_index.data.return_value = self.test_asset
                mock_index_at.return_value = mock_index

                # Mock model
                with patch.object(self.view, "model") as mock_model:
                    mock_model.return_value = self.model

                    # Create mock event
                    mock_event = Mock()
                    mock_event.pos.return_value = pos
                    mock_event.globalPos.return_value = global_pos

                    # Call contextMenuEvent
                    self.view.contextMenuEvent(mock_event)

                    # Verify menu was created and actions were added
                    mock_menu_class.assert_called_once_with(self.view)

                    # Check that addAction was called for our new action
                    action_calls = mock_menu.addAction.call_args_list
                    action_texts = [call[0][0] for call in action_calls if call[0]]

                    # Verify "打开资产路径目录" action was added
                    self.assertIn("打开资产路径目录", action_texts)


if __name__ == "__main__":
    unittest.main()
