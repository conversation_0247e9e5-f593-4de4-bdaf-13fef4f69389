# Import built-in modules
import logging

# Import third-party modules
import maya.cmds as cmds

# Import local modules
from cgame_avatar_factory.common import constants as const
from cgame_avatar_factory.face_sculpting_center.utils import dna_utils as dna
from cgame_avatar_factory.face_sculpting_center.utils import mesh_util as mesh


def collapse_model():
    """Clean up the scene and prepare for model collapse

    Perform cleanup operations required for model collapse, including deleting mesh history and unnecessary nodes
    """
    delete_mesh_history()


def delete_nodes():
    nodes_to_delete = [
        const.ROOT_PINCH_JOINT,
        const.FACE_CTRL_BASIC_GRP,
        const.FACE_CTRL_DETAILED_GRP,
        const.PINCE_CTRL_NAME,
        const.MIX_CTRL_NAME,
        const.FACE_CTRL_NAME,
        const.BAK_HEAD_MESH_NAME,
    ]

    for node in nodes_to_delete:
        mesh.delete_node(node)


def delete_mesh_history():
    """Delete history for all objects in the scene while preserving current shape

    This function first duplicates the current mesh shapes to preserve their current state,
    then deletes the original meshes and renames the duplicates to match the original names.
    This approach ensures that the current visual state is maintained while removing all history.
    """
    mesh_names = dna.TemplateDna().get_mesh_name()
    for mesh_name in mesh_names:
        if cmds.objExists(mesh_name):
            cmds.delete(mesh_name, constructionHistory=True)


def restore_mesh_to_base(mesh_name):
    """Restore the mesh to its base (original) state.

    Args:
        mesh_name (str): The name of the mesh to restore.
    """
    # 1. Reset all blendShape weights to 0
    blendshapes = cmds.ls(cmds.listHistory(mesh_name), type="blendShape") or []
    for bs in blendshapes:
        if not cmds.objExists(bs):
            logging.info(f"BlendShape node does not exist: {bs}")
            continue
        num_targets = cmds.blendShape(bs, q=True, weightCount=True)
        for i in range(num_targets):
            attr_full = f"{bs}.w[{i}]"
            if cmds.getAttr(attr_full, lock=True):
                cmds.setAttr(attr_full, lock=False)
            connections = cmds.listConnections(attr_full, s=True, d=False, plugs=True) or []
            for conn in connections:
                try:
                    cmds.disconnectAttr(conn, attr_full)
                except Exception as e:
                    logging.info(f"Failed to disconnect {conn} -> {attr_full}: {e}")
            try:
                cmds.setAttr(attr_full, 0)
            except Exception as e:
                logging.info(f"Failed to set {attr_full}: {e}")
        try:
            cmds.delete(bs)
        except Exception as e:
            logging.info(f"Failed to delete blendShape node {bs}: {e}")

    # 2. Disable all deformers (lattice, nonlinear, etc.)
    deformers = cmds.ls(cmds.listHistory(mesh_name), type=["lattice", "nonLinear", "wrap"]) or []
    for deformer in deformers:
        try:
            cmds.setAttr(f"{deformer}.envelope", 0)
        except Exception:
            pass

    # 3. (Optional) Go to bind pose if skinned
    skin_clusters = cmds.ls(cmds.listHistory(mesh_name), type="skinCluster") or []
    if skin_clusters:
        try:
            cmds.select(mesh_name)
            cmds.mel.eval("gotoBindPose;")
        except Exception:
            pass


def delete_mesh_history_with_restore():
    """Restore all meshes to their base state, then delete history."""
    mesh_names = dna.TemplateDna().get_mesh_name()
    for mesh_name in mesh_names:
        if cmds.objExists(mesh_name):
            restore_mesh_to_base(mesh_name)
            cmds.delete(mesh_name, constructionHistory=True)
