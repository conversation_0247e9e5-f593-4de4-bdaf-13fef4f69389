"""Describe our module distribution to Distutils."""

# Import future modules
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

# Import third-party modules
from setuptools import find_packages
from setuptools import setup

setup(
    name="cgame_avatar_factory",
    author="<PERSON><PERSON>",
    author_email="<PERSON><EMAIL>",
    url="https://git.woa.com/lightbox/internal/cgame_avatar_factory",
    package_dir={"": "."},
    packages=find_packages("."),
    description="avatar tools in Maya for cgame.",
    entry_points={},
    classifiers=[
        "Programming Language :: Python",
        "Programming Language :: Python :: 3",
    ],
    use_scm_version=True,
    setup_requires=["setuptools_scm"],
    package_data={
        "cgame_avatar_factory": [
            "face_sculpting_center/resources/*.json",
            "face_sculpting_center/resources/*.dna",
            "face_sculpting_center/resources/*.fbx",
            "face_sculpting_center/resources/facecustomization/*.json",
            "face_sculpting_center/resources/face_area/*.png",
            "common/resources/static/images/*.svg",
            "face_sculpting_center/resources/models/catchlight/*.fbx",
        ],
    },
)
