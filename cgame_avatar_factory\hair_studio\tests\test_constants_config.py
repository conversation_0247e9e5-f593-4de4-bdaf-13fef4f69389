"""Core Unit Tests for Constants Configuration.

Tests the enhanced constants configuration system including path definitions,
tab mappings, and configuration validation.
"""

# Import built-in modules
import os
import sys
import unittest

# Add the project root to Python path for testing
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


class TestConstantsConfig(unittest.TestCase):
    """Core tests for constants configuration."""

    def test_hair_asset_lib_tab_map_structure(self):
        """Test HAIR_ASSET_LIB_TAB_MAP structure and content."""
        # Import local modules
        from cgame_avatar_factory.hair_studio.constants import HAIR_ASSET_LIB_TAB_MAP

        # Test basic structure
        self.assertIsInstance(HAIR_ASSET_LIB_TAB_MAP, dict)
        self.assertGreater(len(HAIR_ASSET_LIB_TAB_MAP), 0)

        # Test required keys
        expected_keys = ["eyebrow", "hair", "beard"]
        for key in expected_keys:
            self.assertIn(key, HAIR_ASSET_LIB_TAB_MAP)

        # Test each tab configuration
        for tab_key, config in HAIR_ASSET_LIB_TAB_MAP.items():
            self.assertIsInstance(config, dict)
            self.assertIn("show_text", config)
            self.assertIn("lib_path", config)

            # Test show_text is not empty
            self.assertIsInstance(config["show_text"], str)
            self.assertGreater(len(config["show_text"]), 0)

            # Test lib_path is a list (after dynamic discovery)
            self.assertIsInstance(config["lib_path"], list)

    def test_display_texts(self):
        """Test display text mappings."""
        # Import local modules
        from cgame_avatar_factory.hair_studio.constants import HAIR_ASSET_LIB_TAB_MAP

        expected_texts = {
            "eyebrow": "眉毛",
            "hair": "头发",
            "beard": "胡子",
        }

        for key, expected_text in expected_texts.items():
            self.assertEqual(HAIR_ASSET_LIB_TAB_MAP[key]["show_text"], expected_text)

    def test_all_hair_asset_dir_structure(self):
        """Test ALL_HAIR_ASSET_DIR configuration."""
        # Import local modules
        from cgame_avatar_factory.hair_studio.constants import ALL_HAIR_ASSET_DIR
        from cgame_avatar_factory.hair_studio.constants import LOCAL_HAIR_CARD_LIB_PATH
        from cgame_avatar_factory.hair_studio.constants import PROJECT_HAIR_CARD_LIB_PATH
        from cgame_avatar_factory.hair_studio.constants import PUBLICK_HAIR_CARD_LIB_PATH

        # Test structure
        self.assertIsInstance(ALL_HAIR_ASSET_DIR, list)

        # Test contains expected paths
        self.assertIn(PUBLICK_HAIR_CARD_LIB_PATH, ALL_HAIR_ASSET_DIR)
        self.assertIn(PROJECT_HAIR_CARD_LIB_PATH, ALL_HAIR_ASSET_DIR)
        self.assertIn(LOCAL_HAIR_CARD_LIB_PATH, ALL_HAIR_ASSET_DIR)

        # Test path types
        for path in ALL_HAIR_ASSET_DIR:
            self.assertIsInstance(path, str)

    def test_default_sub_tab_key(self):
        """Test DEFAULT_SUB_TAB_KEY configuration."""
        # Import local modules
        from cgame_avatar_factory.hair_studio.constants import DEFAULT_SUB_TAB_KEY
        from cgame_avatar_factory.hair_studio.constants import HAIR_ASSET_LIB_TAB_MAP

        # Test default key exists
        self.assertIsInstance(DEFAULT_SUB_TAB_KEY, str)
        self.assertEqual(DEFAULT_SUB_TAB_KEY, "hair")

        # Test default key is valid
        self.assertIn(DEFAULT_SUB_TAB_KEY, HAIR_ASSET_LIB_TAB_MAP)

    def test_xgen_curve_config_structure(self):
        """Test XGEN and CURVE configuration structure."""
        # Import local modules
        from cgame_avatar_factory.hair_studio.constants import ALL_CURVE_ASSET_DIR
        from cgame_avatar_factory.hair_studio.constants import ALL_XGEN_ASSET_DIR
        from cgame_avatar_factory.hair_studio.constants import CURVE_ASSET_LIB_TAB_MAP
        from cgame_avatar_factory.hair_studio.constants import XGEN_ASSET_LIB_TAB_MAP

        # Test XGEN configuration
        self.assertIsInstance(ALL_XGEN_ASSET_DIR, list)
        self.assertIsInstance(XGEN_ASSET_LIB_TAB_MAP, dict)

        # Test CURVE configuration
        self.assertIsInstance(ALL_CURVE_ASSET_DIR, list)
        self.assertIsInstance(CURVE_ASSET_LIB_TAB_MAP, dict)

        # Test they are currently empty (as expected)
        # XGEN and CURVE are not currently configured but structure is ready
        for path in ALL_XGEN_ASSET_DIR:
            self.assertIsInstance(path, str)
        for path in ALL_CURVE_ASSET_DIR:
            self.assertIsInstance(path, str)

    def test_hair_type_constants(self):
        """Test hair type constants."""
        # Import local modules
        from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CARD
        from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CURVE
        from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_XGEN

        # Test constants exist and are strings
        self.assertEqual(HAIR_TYPE_CARD, "card")
        self.assertEqual(HAIR_TYPE_XGEN, "xgen")
        self.assertEqual(HAIR_TYPE_CURVE, "curve")

    def test_path_constants_not_empty_strings(self):
        """Test that configured paths are not empty strings."""
        # Import local modules
        from cgame_avatar_factory.hair_studio.constants import PUBLICK_HAIR_CARD_LIB_PATH

        # Test public path is configured
        self.assertIsInstance(PUBLICK_HAIR_CARD_LIB_PATH, str)
        self.assertGreater(len(PUBLICK_HAIR_CARD_LIB_PATH), 0)

        # local path can be empty
        # Note: PROJECT_HAIR_CARD_LIB_PATH can be empty string (disabled)

    def test_supported_extensions(self):
        """Test supported file extensions."""
        # Import local modules
        from cgame_avatar_factory.hair_studio.constants import SUPPORTED_MODEL_EXTENSIONS
        from cgame_avatar_factory.hair_studio.constants import SUPPORTED_THUMBNAIL_EXTENSIONS

        # Test model extensions
        self.assertIsInstance(SUPPORTED_MODEL_EXTENSIONS, set)
        expected_model_exts = {".fbx", ".obj", ".ma", ".mb"}
        for ext in expected_model_exts:
            self.assertIn(ext, SUPPORTED_MODEL_EXTENSIONS)

        # Test thumbnail extensions
        self.assertIsInstance(SUPPORTED_THUMBNAIL_EXTENSIONS, set)
        expected_thumb_exts = {".jpg", ".jpeg", ".png", ".bmp", ".tiff"}
        for ext in expected_thumb_exts:
            self.assertIn(ext, SUPPORTED_THUMBNAIL_EXTENSIONS)

    def test_config_consistency(self):
        """Test consistency between related configurations."""
        # Import local modules
        from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CARD
        from cgame_avatar_factory.hair_studio.constants import DEFAULT_SUB_TAB_KEY
        from cgame_avatar_factory.hair_studio.constants import HAIR_ASSET_LIB_TAB_MAP

        # Test default sub tab key exists in tab map
        self.assertIn(DEFAULT_SUB_TAB_KEY, HAIR_ASSET_LIB_TAB_MAP)

        # Test hair type constant is consistent
        self.assertEqual(HAIR_TYPE_CARD, "card")

        # Test all tab keys are valid identifiers
        for tab_key in HAIR_ASSET_LIB_TAB_MAP.keys():
            self.assertIsInstance(tab_key, str)
            self.assertGreater(len(tab_key), 0)
            # Should be valid Python identifier-like (no spaces, special chars)
            self.assertTrue(tab_key.replace("_", "").isalnum())


class TestConstantsIntegration(unittest.TestCase):
    """Integration tests for constants with other systems."""

    def test_constants_with_asset_lib_config(self):
        """Test constants integration with AssetLibConfig."""
        # Import local modules
        from cgame_avatar_factory.hair_studio.constants import DEFAULT_SUB_TAB_KEY
        from cgame_avatar_factory.hair_studio.constants import HAIR_ASSET_LIB_TAB_MAP
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import AssetLibConfig

        config = AssetLibConfig()

        # Test that config uses constants correctly
        self.assertEqual(config.get_default_tab_key(), DEFAULT_SUB_TAB_KEY)

        # Test that all constant tab keys are available in config
        for tab_key in HAIR_ASSET_LIB_TAB_MAP.keys():
            self.assertIn(tab_key, config.get_tab_keys())
            self.assertTrue(config.is_valid_tab_key(tab_key))

    def test_constants_with_asset_config(self):
        """Test constants integration with asset_config module."""
        # Import local modules
        from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CARD
        from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CURVE
        from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_XGEN
        from cgame_avatar_factory.hair_studio.data.asset_config import get_asset_paths

        asset_paths = get_asset_paths()

        # Test that asset_config recognizes hair type constants
        self.assertIn(HAIR_TYPE_CARD, asset_paths)
        self.assertIn(HAIR_TYPE_XGEN, asset_paths)
        self.assertIn(HAIR_TYPE_CURVE, asset_paths)

        # Test that paths are lists
        for asset_type, paths in asset_paths.items():
            self.assertIsInstance(paths, list)


def run_tests():
    """Run the test suite."""
    print("Testing Constants Configuration...")

    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestConstantsConfig))
    suite.addTests(loader.loadTestsFromTestCase(TestConstantsIntegration))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    if result.wasSuccessful():
        print(f"\n✅ All {result.testsRun} constants tests passed!")
    else:
        print(f"\n❌ {len(result.failures)} failures, {len(result.errors)} errors")

    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
