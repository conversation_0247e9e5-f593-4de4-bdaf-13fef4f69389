# Hair Studio 核心功能测试

## 概述

这个测试套件确保 Hair Studio 的核心功能在配置修改和系统优化后仍然正常工作。

## 测试模块

### 1. **Constants Configuration Tests** (`test_constants_config.py`)
测试常量配置系统的核心功能：

- ✅ `HAIR_ASSET_LIB_TAB_MAP` 结构和内容验证
- ✅ 显示文本映射正确性
- ✅ `ALL_HAIR_ASSET_DIR` 配置结构
- ✅ 默认子tab键配置
- ✅ XGEN/CURVE 配置结构就绪性
- ✅ 文件扩展名支持
- ✅ 配置一致性检查

### 2. **AssetLibConfig Core Tests** (`test_asset_lib_config_core.py`)
测试资产库配置系统的核心功能：

- ✅ 配置初始化
- ✅ Tab 配置管理
- ✅ 默认tab逻辑
- ✅ 子tab显示逻辑
- ✅ 库路径获取
- ✅ 所有资产路径聚合
- ✅ Tab索引映射
- ✅ 配置摘要生成
- ✅ 便利函数

### 3. **Data Manager Core Tests** (`test_data_manager_core.py`)
测试数据管理器的核心功能：

- ✅ 数据管理器初始化
- ✅ 基本资产检索
- ✅ 资产数据结构验证
- ✅ 组件检索
- ✅ 资产重新加载
- ✅ 按类型过滤资产
- ✅ 与asset_config集成
- ✅ 路径验证
- ✅ 子类型逻辑核心功能

### 4. **Sub-Tab Switching Tests** (`test_sub_tab_switching.py`)
测试子tab切换的核心逻辑：

- ✅ 按子类型过滤资产
- ✅ 子tab配置验证
- ✅ 子tab显示逻辑
- ✅ Tab索引映射
- ✅ 默认tab选择
- ✅ Tab切换状态管理
- ✅ 按子类型统计资产
- ✅ 子类型验证
- ✅ 配置和常量集成
- ✅ 数据管理器集成

### 5. **Sub-Type Logic Tests** (`test_sub_type_logic.py`)
测试子类型判断逻辑：

- ✅ 路径检测（hair/eyebrow/beard）
- ✅ 默认回退逻辑
- ✅ 大小写不敏感检测
- ✅ 混合路径分隔符支持
- ✅ 中文关键词检测
- ✅ 资产数据结构支持

## 运行测试

### 运行所有核心测试
```bash
cd cgame_avatar_factory/hair_studio/tests
python run_core_tests.py
```

### 运行特定测试模块
```bash
# 常量配置测试
python run_core_tests.py constants

# 资产库配置测试
python run_core_tests.py config

# 数据管理器测试
python run_core_tests.py data

# 子tab切换测试
python run_core_tests.py subtab

# 子类型逻辑测试
python run_core_tests.py subtype
```

### 单独运行测试模块
```bash
# 运行单个测试文件
python test_constants_config.py
python test_asset_lib_config_core.py
python test_data_manager_core.py
python test_sub_tab_switching.py
python test_sub_type_logic.py
```

## 测试覆盖范围

### 🎯 **核心功能覆盖**
- **配置系统**: 常量定义、动态发现、路径管理
- **数据管理**: 资产加载、过滤、结构验证
- **UI逻辑**: 子tab切换、状态管理、显示控制
- **类型判断**: 路径解析、子类型确定、验证逻辑

### 📊 **测试类型**
- **单元测试**: 独立功能模块测试
- **集成测试**: 模块间交互测试
- **结构测试**: 数据结构和配置验证
- **逻辑测试**: 业务逻辑和算法测试

### 🔍 **测试重点**
- **向后兼容性**: 确保现有功能不受影响
- **配置正确性**: 验证配置文件和常量设置
- **数据完整性**: 确保资产数据结构完整
- **逻辑准确性**: 验证业务逻辑正确执行

## 测试环境要求

### 📋 **依赖项**
- Python 3.x
- unittest (标准库)
- 项目根目录在 Python 路径中

### 🚫 **无需依赖**
- **Qt/PySide**: 测试专注于业务逻辑，不依赖UI框架
- **外部库**: 仅使用Python标准库
- **实际资产文件**: 使用模拟数据进行测试

## 测试结果解读

### ✅ **成功标准**
- 所有测试用例通过
- 无异常或错误
- 配置验证通过
- 数据结构完整

### ❌ **失败处理**
1. **查看详细错误信息**
2. **检查相关配置文件**
3. **验证代码修改**
4. **修复问题后重新测试**

### 📈 **性能指标**
- 测试执行时间 < 30秒
- 内存使用合理
- 无内存泄漏

## 维护指南

### 🔄 **定期运行**
- 代码修改后
- 配置更新后
- 发布前验证
- 定期回归测试

### 📝 **更新测试**
- 新功能添加时更新测试
- 配置变更时更新验证
- Bug修复时添加回归测试
- 重构时保持测试覆盖

### 🎯 **测试原则**
- **快速**: 测试应该快速执行
- **独立**: 测试之间不应相互依赖
- **可重复**: 测试结果应该一致
- **清晰**: 测试意图应该明确

## 故障排除

### 常见问题

#### 1. **导入错误**
```
ModuleNotFoundError: No module named 'cgame_avatar_factory'
```
**解决方案**: 确保项目根目录在Python路径中

#### 2. **配置错误**
```
KeyError: 'show_text'
```
**解决方案**: 检查 `HAIR_ASSET_LIB_TAB_MAP` 配置完整性

#### 3. **路径错误**
```
FileNotFoundError: [Errno 2] No such file or directory
```
**解决方案**: 测试使用模拟数据，不依赖实际文件

### 调试技巧

1. **使用详细输出**: `python -m unittest -v`
2. **单独运行失败测试**: 专注于特定问题
3. **检查日志输出**: 查看详细错误信息
4. **验证配置**: 确保常量和配置正确

## 总结

这个测试套件确保 Hair Studio 的核心功能在各种修改和优化后仍然稳定可靠。通过定期运行这些测试，可以及早发现问题并保证系统质量。
