# Import built-in modules
import os
from typing import List

# Import third-party modules
from dna import DataLayer_Geometry
from dna_viewer import DNA
import maya.cmds as cmds
from qtpy import QtCore

# Import local modules
import cgame_avatar_factory.common.constants as const
import cgame_avatar_factory.common.utils.axis_align_utils as axis_align_utils
import cgame_avatar_factory.face_sculpting_center.build_face.build_finally_mesh as build_mesh
import cgame_avatar_factory.face_sculpting_center.build_face.create_pinch_dna as create_pinch
import cgame_avatar_factory.face_sculpting_center.build_face.face_controllers as face_ctrls
import cgame_avatar_factory.face_sculpting_center.build_face.setup_riglogic as riglogic
import cgame_avatar_factory.face_sculpting_center.mesh_align as mesh_align
import cgame_avatar_factory.face_sculpting_center.parametric_parts.parametric_eyes as parametric_eyes
import cgame_avatar_factory.face_sculpting_center.utils.dna_utils as dna
import cgame_avatar_factory.face_sculpting_center.utils.joint_utils as joint
import cgame_avatar_factory.face_sculpting_center.utils.mesh_util as mesh


class BuildSignalManager(QtCore.QObject):
    progress_signal = QtCore.Signal(dict)
    catchlight_reset_signal = QtCore.Signal()

    def __init__(self):
        super(BuildSignalManager, self).__init__()


class AutoProgressTracker:
    """Automatic progress tracking based on method execution with weighted calculation."""

    def __init__(self, signal_manager):
        self.signal_manager = signal_manager
        self.steps = []
        self.current_step = 0
        self.total_steps = 0
        self.total_weight = 0
        self._should_auto_finish = False

    def register_steps(self, steps_config):
        """Register all steps with their descriptions and weights.

        Args:
            steps_config: List of tuples (method_name, description, weight)
                         weight determines how much progress this step contributes
        """
        self.steps = steps_config
        self.total_steps = len(steps_config)
        self.total_weight = sum(step[2] for step in steps_config)
        self.current_step = 0

    def start_step(self, method_name):
        """Automatically calculate and emit weighted progress when a step starts."""
        for i, (step_method, description, weight) in enumerate(self.steps):
            if step_method == method_name:
                # Calculate weighted progress based on completed steps
                progress_value = self._calculate_weighted_progress(i)
                self.signal_manager.progress_signal.emit(
                    {
                        "value": progress_value,
                        "text": description,
                    },
                )
                self.current_step = i

                # Auto-finish when reaching the last step
                if i == len(self.steps) - 1:
                    # Schedule finish() to be called after the method completes
                    self._should_auto_finish = True
                break

    def _calculate_weighted_progress(self, current_index):
        """Calculate progress based on weights of completed steps.

        Args:
            current_index: Index of current step

        Returns:
            int: Progress percentage (0-100)
        """
        if self.total_weight == 0:
            return 0

        # Sum weights of all completed steps
        completed_weight = sum(step[2] for step in self.steps[:current_index])

        # Calculate percentage based on weight ratio
        progress_percentage = (completed_weight / self.total_weight) * 100

        return int(progress_percentage)

    def finish(self):
        """Mark process as complete."""
        self.signal_manager.progress_signal.emit({"value": 100, "text": "完成..."})


def auto_progress(description=None, weight=1):
    """Decorator for automatic progress tracking.

    Args:
        description: Text description for this step (optional, uses registered description)
        weight: Relative weight of this step (for future use)
    """

    def decorator(func):
        def wrapper(self, *args, **kwargs):
            if hasattr(self, "auto_progress"):
                self.auto_progress.start_step(func.__name__)

            # Execute the original method
            result = func(self, *args, **kwargs)

            # Auto-finish if this was the last step
            if hasattr(self, "auto_progress") and self.auto_progress._should_auto_finish:
                self.auto_progress.finish()
                self.auto_progress._should_auto_finish = False

            return result

        wrapper.__name__ = func.__name__
        return wrapper

    return decorator


# Use function scope static variable to implement singleton pattern
def get_build_signal_manager():
    """Get build_face signal manager instance

    Returns:
        BuildSignalManager: signal manager instance
    """
    if not hasattr(get_build_signal_manager, "_instance"):
        get_build_signal_manager._instance = BuildSignalManager()
    return get_build_signal_manager._instance


class CustomizationRigBuilder:
    """Build and process mesh data from DNA files.

    This class handles DNA file loading, mesh creation, joint setup and skinning.

    Attributes:
        dna_paths (List[str]): List of paths to DNA files
        source_dna_path (str): Path to base DNA file
        template_dna_reader: DNA file reader instance
        hierarchy (dict): Joint hierarchy mapping
        joint_list (list): List of joint names
        local_position (dict): Joint local positions
        local_rotate (dict): Joint local rotations
        world_positions (dict): Joint world positions
    """

    def __init__(self, dna_paths: List[str], collapsed_mode: bool = False) -> None:
        """Initialize CustomizationRigBuilder with DNA file paths.

        Args:
            dna_paths: List of paths to DNA files to process
            collapsed_mode: Whether to use collapsed mode (uses paths[1:4] instead of all paths)
        """
        # Store parameters
        self.dna_paths = dna_paths
        self.collapsed_mode = collapsed_mode

        # Initialize core components
        self.signal_manager = get_build_signal_manager()
        self.auto_progress = AutoProgressTracker(self.signal_manager)

        # Initialize data structures
        self.template_dna_reader = None
        self.hierarchy = {}
        self.joint_list = []
        self.local_position = {}
        self.local_rotate = {}
        self.world_positions = {}
        self.source_dna_path = None
        self.base_dna = None
        self.imported_up_axis = None
        self.template_dna_axis = None

    def build(self):
        """Execute build process"""
        # Backup head mesh
        mesh.backup_head_mesh()

        # Register all build steps for automatic progress tracking
        self._register_build_steps()

        # Setup DNA paths based on mode
        self._setup_dna_paths(self.dna_paths)

        # Initialize data structures
        self._initialize_data_structures()

        # Setup DNA reader and coordinate system
        self._setup_dna_reader(self.dna_paths[0])

        # Execute main build process
        self._execute_build_process()

    def _register_build_steps(self):
        """Register all build_face steps for automatic weighted progress calculation.

        Weight allocation based on estimated complexity and time:
        - Light operations (setup, initialization): 1-2
        - Medium operations (data processing): 3-5
        - Heavy operations (mesh building, rig setup): 8-15
        """
        steps = [
            ("_setup_dna_paths", "初始化DNA构建...", 1),
            ("_initialize_data_structures", "初始化数据结构...", 1),
            ("_setup_dna_reader", "加载DNA文件...", 3),
            ("_execute_build_process", "构建面部骨骼...", 2),
            ("_process_mesh_data", "处理模型数据...", 8),
            ("_build_joint_system", "获取关节数据...", 5),
            ("_build_final_mesh_and_bones", "构建最终网格...", 12),
            ("_setup_rig_logic_and_controllers", "设置绑定逻辑...", 15),
            ("_finalize_build", "备份头部网格...", 3),
        ]
        self.auto_progress.register_steps(steps)

    @auto_progress()
    def _setup_dna_paths(self, dna_paths: List[str]) -> None:
        """Setup DNA paths based on collapsed mode."""
        if self.collapsed_mode:
            self.dna_paths = dna_paths[1:4]
        else:
            self.dna_paths = dna_paths

        self.source_dna_path = os.path.join(const.CONFIG_PATH, const.BASE_DNA_PATH)

    @auto_progress()
    def _initialize_data_structures(self) -> None:
        """Initialize all data structure attributes."""
        self.template_dna_reader = dna.TemplateDna.get_reader()
        self.hierarchy = {}
        self.joint_list = []
        self.local_position = {}
        self.local_rotate = {}
        self.world_positions = {}

    @auto_progress()
    def _setup_dna_reader(self, base_dna_path: str) -> None:
        """Setup DNA reader and determine coordinate system."""
        self.base_dna = DNA(base_dna_path)
        self.imported_up_axis = "y" if self.base_dna.coordinate_system == (0, 2, 4) else "z"
        coord_system = self.template_dna_reader.getCoordinateSystem()
        is_y_up_system = coord_system.xAxis == 0 and coord_system.yAxis == 2 and coord_system.zAxis == 4

        self.template_dna_axis = "y" if is_y_up_system else "z"

    @auto_progress()
    def _execute_build_process(self) -> None:
        """Execute the main build_face process."""
        self.build_face_sculpting_rig()
        self._finalize_build()

    @auto_progress()
    def _finalize_build(self) -> None:
        """Finalize the build_face process."""
        mesh.set_layer_display_type(const.HEAD_LAYER_NAME, 2)
        mesh.create_layout([const.FACE_CTRL_BASIC_GRP, const.FACE_CTRL_DETAILED_GRP])

        # Emit signal to notify UI components to refresh their state
        self.signal_manager.catchlight_reset_signal.emit()

        face_ctrls.hide_all_ctrl()

    def build_face_sculpting_rig(self):
        """Process DNA files and build_face mesh with joints."""
        # Step 1: Process mesh data
        source_dna_data, mesh_deltas_data, face_align_mesh_data = self._process_mesh_data()

        # Step 2: Build joint system
        self._build_joint_system()

        # Step 3: Build final mesh and setup bones
        self._build_final_mesh_and_bones(source_dna_data, face_align_mesh_data, mesh_deltas_data)

        # Step 4: Setup rig logic and controllers
        self._setup_rig_logic_and_controllers()

    @auto_progress()
    def _process_mesh_data(self):
        """Process and align mesh data from DNA files."""
        # Get source DNA mesh data
        source_dna_data = self.get_mesh_data_from_dna([self.source_dna_path], self.template_dna_axis)

        # Get all mesh data and handle collapsed mode
        _all_mesh_data = self.get_mesh_data_from_dna(self.dna_paths, self.imported_up_axis)
        if self.collapsed_mode:
            _all_mesh_data.insert(0, self.get_mesh_data_from_scenes())

        # Align mesh data
        mesh_deltas_data, face_align_mesh_data = mesh_align.start_align(_all_mesh_data)

        return source_dna_data, mesh_deltas_data, face_align_mesh_data

    @auto_progress()
    def _build_joint_system(self):
        """Build joint hierarchy and extract joint data."""
        self.get_joints_data()

    @auto_progress()
    def _build_final_mesh_and_bones(self, source_dna_data, face_align_mesh_data, mesh_deltas_data):
        """Build final mesh and setup bone positions."""
        build_mesh.BuildFinallyMesh(face_align_mesh_data, const.FACE_MIX_SUFFIX)
        build_mesh.BuildFinallyMesh(mesh_deltas_data, const.FACE_ALIGN_SUFFIX)

        joint.AdaptiveBonePosition(source_dna_data[0], face_align_mesh_data[0], self.world_positions)
        self.add_skin_cluster()

    @auto_progress()
    def _setup_rig_logic_and_controllers(self):
        """Setup rig logic system and create controllers."""
        # Setup rig logic
        output_path = create_pinch.create_pinch_dna()

        riglogic.SetupRiglogic(output_path, const.PINCE_CTRL_NAME, const.PINCE_NODE_NAME, connect=False)

        # Create controllers
        face_ctrls.create_controller()

        # Create eyeball controllers
        parametric_eyes.create_eyeball_drivers()

    def add_skin_cluster(self):
        """Import skin cluster data from source DNA."""
        dna.import_all_skin_clusters(self.source_dna_path)
        for mesh_name in dna.TemplateDna.get_mesh_name():
            cmds.rename(f"{mesh_name}_skinCluster", f"{mesh_name}_skinCluster_{const.PINCH_OUTPUT_DNA_NAME}")

    def get_joints_data(self):
        """Extract and build_face joint data from DNA.

        Returns:
            tuple: Joint hierarchy and world positions
        """
        reader = dna.TemplateDna.get_reader()
        joint_count = reader.getJointCount()

        for joint_index in range(joint_count):
            joint_name = reader.getJointName(joint_index)
            self.joint_list.append(joint_name)
            parent_idx = reader.getJointParentIndex(joint_index)
            self.hierarchy[joint_name] = parent_idx
            self.local_position[joint_name] = reader.getNeutralJointTranslation(joint_index)
            self.local_rotate[joint_name] = reader.getNeutralJointRotation(joint_index)

            self.build_joint(joint_name)

        axis_align_utils.align_object_to_scene_up_axis(const.ROOT_PINCH_JOINT, self.template_dna_axis)

        for joint_index in range(joint_count):
            joint_name = reader.getJointName(joint_index)
            self.world_positions[joint_name] = mesh.get_joint_world_position(joint_name)

        return self.hierarchy, self.world_positions

    def build_joint(self, joint_name):
        """Create a joint and set its transforms.

        Args:
            joint_name: Name of joint to create

        Returns:
            str: Created joint name
        """
        parent_idx = self.hierarchy[joint_name]
        if parent_idx == self.joint_list.index(joint_name):
            cmds.select(clear=True)
        else:
            cmds.select(self.joint_list[parent_idx])

        joint = cmds.joint(name=joint_name)

        mesh.set_joint_position(joint_name, self.local_position[joint_name], self.local_rotate[joint_name])

        if self.joint_list.index(joint_name) == 0:
            cmds.setAttr(f"{joint}.v", 0, lock=True, keyable=False, channelBox=False)
        return joint

    def get_mesh_data_from_scenes(self):
        """Get mesh data from Maya scene.

        Returns:
            dict: Dictionary mapping mesh names to vertex positions
        """
        mesh_name_list = dna.TemplateDna.get_mesh_name()
        mesh_data = {}

        for mesh_name in mesh_name_list:
            vertex_positions = mesh.get_mesh_data(mesh_name)
            mesh_data[mesh_name] = vertex_positions

        return mesh_data

    def get_mesh_data_from_dna(self, dna_path: List[str], import_axis: str):
        """Extract mesh vertex data from DNA files.

        Args:
            dna_path: List of DNA file paths
            import_axis: Axis to align imported data to. "y" or "z".

        Returns:
            list: List of mesh data dictionaries
        """
        all_mesh_data = []

        for path in dna_path:
            _mesh_data = {}
            reader = dna.load_dna_calib(path, DataLayer_Geometry)
            for mesh_index in range(reader.getMeshCount()):
                mesh_name = reader.getMeshName(mesh_index)
                vertex_index = reader.getVertexPositionCount(mesh_index)
                mesh_data = self.get_vertex_positions(reader, vertex_index, mesh_index, import_axis)
                _mesh_data[mesh_name] = mesh_data
            all_mesh_data.append(_mesh_data)
        return all_mesh_data

    def get_vertex_positions(self, reader, vertex_count: int, mesh_index: int, import_axis: str):
        """Get vertex positions for a mesh.

        Args:
            reader: DNA reader instance
            vertex_count: Number of vertices
            mesh_index: Index of mesh to process
            import_axis: Axis to align imported data to. "y" or "z".

        Returns:
            list: List of vertex positions
        """
        mesh_data = []
        for i in range(vertex_count):
            position = reader.getVertexPosition(mesh_index, i)
            position = axis_align_utils.align_point_to_scene_up_axis(position, import_axis)
            mesh_data.append(position)
        return mesh_data
