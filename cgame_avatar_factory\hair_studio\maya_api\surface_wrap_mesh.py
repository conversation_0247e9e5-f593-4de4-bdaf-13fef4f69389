"""Wrap mesh for surface like, eyebrow follow face.
    Examples:

added = createProximityWrap("head_lod0_mesh_reference", ["Eyebrows_M_FlatThick_LOD1", "Mustache_L_Wavy_LOD1", ])
remove_target_from_deformer(added, "Mustache_L_Wavy_LOD1")

add_target_to_deformer(added, "Beard_S_Uneven_LOD1")
"""
# Import built-in modules
import logging

# Import Maya commands using unified import system
from .utils import get_maya_cmds
from .utils import is_maya_available

# Get Maya commands (real or mock)
cmds = get_maya_cmds(__name__)
MAYA_AVAILABLE = is_maya_available()

# Lazy import of node_interface for Maya 2022 compatibility
node_interface = None


def _get_node_interface():
    """Lazy import of maya.internal.nodes.proximitywrap.node_interface for Maya 2022."""
    global node_interface
    if node_interface is None:
        if MAYA_AVAILABLE:
            try:
                # Import third-party modules
                import maya.internal.nodes.proximitywrap.node_interface as ni

                node_interface = ni
            except ImportError:
                # Create mock if import fails

                logger = logging.getLogger(__name__)

                class MockNodeInterface(object):
                    def __getattr__(self, item):
                        def wrapper(*args, **kwargs):
                            logger.warning(
                                "Maya node_interface called but not available: {}.{} {}".format(
                                    item,
                                    args,
                                    kwargs,
                                ),
                            )
                            return "mock_{}".format(item)

                        return wrapper

                node_interface = MockNodeInterface()
        else:
            # Create mock for non-Maya environment

            logger = logging.getLogger(__name__)

            class MockNodeInterface(object):
                def __getattr__(self, item):
                    def wrapper(*args, **kwargs):
                        logger.warning(
                            "Maya node_interface called outside of Maya: {}.{} {}".format(
                                item,
                                args,
                                kwargs,
                            ),
                        )
                        return "mock_{}".format(item)

                    return wrapper

            node_interface = MockNodeInterface()

    return node_interface


# Use the common get_maya_version function from utils
from .utils import get_maya_version


def _add_proximity_wrap_driver_compatible(deformer, driver_shape):
    """
    Add driver to proximityWrap deformer using version-compatible method.

    Args:
        deformer (str): ProximityWrap deformer node name
        driver_shape (str): Driver shape node name

    Returns:
        bool: True if successful, False otherwise
    """

    logger = logging.getLogger(__name__)

    if not MAYA_AVAILABLE:
        logger.warning("Maya not available, skipping driver connection")
        return False

    maya_version = get_maya_version()
    if maya_version < 2019:
        logger.warning("Maya version < 2019, skipping driver connection")
        return False

    # use node interface first.
    ni = _get_node_interface()
    if hasattr(ni, "NodeInterface"):
        proximity_interface = ni.NodeInterface(deformer)
        if hasattr(proximity_interface, "addDriver"):
            proximity_interface.addDriver(driver_shape)
            return True
        else:
            logger.error("addDriver method not available in node_interface")
    else:
        logger.error("NodeInterface not available in node_interface")

    # then try cmds proximityWrap interface
    if hasattr(cmds, "proximityWrap"):
        cmds.proximityWrap(deformer, edit=True, addDrivers=[driver_shape])
        return True
    else:
        logger.error("cmds.proximityWrap not available in Maya version = {}".format(maya_version))

    logger.error(f"Failed to add proximity wrap driver finally.")
    return False


def createProximityWrap(source, target_list, name="proximityWrap", attr_params=None):
    """
    Creates a proximity with the given source and target transforms.
    Compatible with Maya 2022 and 2023+.

    Args:
        source (pm.nodetypes.Transform): Transform with skinned mesh that will drive given target.
        target (pm.nodetypes.Transform): Transform with mesh shape that will be driven by given source.
    Returns:
        (pm.nodetypes.ProximityWrap): Proximity wrap node created.

    Note:
        - Maya 2022: Uses maya.internal.nodes.proximitywrap.node_interface.addDriver
        - Maya 2023+: Uses cmds.proximityWrap with addDrivers parameter
    """
    # implementing with maya.cmds since PyMel raises the following warning for every attribute set.
    # Warning: pymel.core.general : Could not create desired MFn. Defaulting to MFnDependencyNode.
    deformer = cmds.deformer(target_list, type="proximityWrap", name=name)[0]

    # personal preference of proximity wrap attributes, these could be exposed as kwargs too!
    cmds.setAttr(deformer + ".maxDrivers", 1)
    attr_params = attr_params if attr_params else {}
    for key, value in attr_params.items():
        cmds.setAttr("{0}.{1}".format(deformer, key), value)

    # Add driver using version-compatible method
    success = _add_proximity_wrap_driver_compatible(deformer, source + "Shape")
    if not success:
        logger = logging.getLogger(__name__)
        logger.warning(
            f"Failed to add driver {source}Shape to proximityWrap {deformer}",
        )

    return deformer


def get_deformer_targets(deformer):
    if not cmds.objExists(deformer):
        return None
    # query targets of deformer
    targets = cmds.deformer(deformer, query=True, geometry=True)
    return targets


def add_target_to_deformer(deformer, new_target):
    # add new target to deformer
    if cmds.objExists(new_target) and cmds.objExists(deformer):
        cmds.deformer(deformer, edit=True, geometry=new_target)
    else:
        raise ValueError(f"Target '{new_target}' does not exist.")


def remove_target_from_deformer(deformer, target_to_remove):
    # delete target from deformer
    if cmds.objExists(target_to_remove):
        cmds.deformer(deformer, edit=True, remove=True, geometry=target_to_remove)
    else:
        raise ValueError(f"Target '{target_to_remove}' does not exist.")
