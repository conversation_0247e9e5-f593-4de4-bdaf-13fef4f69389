# Import built-in modules
from enum import Enum
import logging
import os
import time

# Import third-party modules
import maya.api.OpenMaya as om2
import maya.cmds as cmds
import maya.mel as mel
import numpy as np

# Import local modules
import cgame_avatar_factory.common.constants as const
from cgame_avatar_factory.common.utils import utils
import cgame_avatar_factory.face_sculpting_center.mesh_align as mesh_align


# Define an enum for eye selection
class EyeSelection(Enum):
    LEFT = "left"
    RIGHT = "right"


# -------------------------
# Module-level state variables
# -------------------------
_symmetric_mode = True
_selected_eye = EyeSelection.LEFT
_eye_config = None
_logger = logging.getLogger(__name__)


def get_symmetric_mode():
    """Get the current symmetric mode state.

    Returns:
        bool: True if symmetric mode is enabled, False otherwise
    """
    return _symmetric_mode


def set_symmetric_mode(enabled):
    """Set the symmetric mode state.

    Args:
        enabled (bool): True to enable symmetric mode, False to disable
    """
    global _symmetric_mode
    _symmetric_mode = enabled


def get_selected_eye():
    """Get the currently selected eye.

    Returns:
        EyeSelection: The currently selected eye (LEFT or RIGHT)
    """
    return _selected_eye


def set_selected_eye(eye_selection):
    """Set the currently selected eye.

    Args:
        eye_selection (EyeSelection): The eye to select (LEFT or RIGHT)
    """
    global _selected_eye
    _selected_eye = eye_selection


def get_eye_config():
    """Get the eye configuration data, loading it if necessary.

    Returns:
        dict: The eye configuration dictionary
    """
    global _eye_config
    if _eye_config is None:
        eye_config_path = os.path.join(const.CONFIG_PATH, const.EYE_CONFIG_PATH)
        if not os.path.exists(eye_config_path):
            eye_config_path = os.path.join(const.FACE_RESOURCE_ROOT, const.EYE_CONFIG_PATH)
        _eye_config = utils.read_json(eye_config_path)
    return _eye_config


def create_eyeball_drivers():
    """Create and organize eyeball driver objects in the scene.

    This function creates the parametric eyes group and eyeball driver spheres,
    deleting any existing ones first to ensure a clean setup.
    """
    start_time = time.time()
    # Delete existing objects if they exist
    try:
        # Delete eye drivers if they exist
        if cmds.objExists(const.EYE_SHAPE_DRIVER_L):
            cmds.delete(const.EYE_SHAPE_DRIVER_L)
        if cmds.objExists(const.EYE_SHAPE_DRIVER_R):
            cmds.delete(const.EYE_SHAPE_DRIVER_R)
        if cmds.objExists(const.CORNEA_DRIVER_L):
            cmds.delete(const.CORNEA_DRIVER_L)
        if cmds.objExists(const.CORNEA_DRIVER_R):
            cmds.delete(const.CORNEA_DRIVER_R)

        # Delete zero space groups if they exist
        if cmds.objExists(const.EYE_DRIVER_L_ZERO):
            cmds.delete(const.EYE_DRIVER_L_ZERO)
        if cmds.objExists(const.EYE_DRIVER_R_ZERO):
            cmds.delete(const.EYE_DRIVER_R_ZERO)
        if cmds.objExists(const.CORNEA_DRIVER_L_ZERO):
            cmds.delete(const.CORNEA_DRIVER_L_ZERO)
        if cmds.objExists(const.CORNEA_DRIVER_R_ZERO):
            cmds.delete(const.CORNEA_DRIVER_R_ZERO)

        # Delete parametric eyes group if it exists
        if cmds.objExists(const.PARAMETRIC_EYES_GRP):
            cmds.delete(const.PARAMETRIC_EYES_GRP)
    except Exception as e:
        _logger.warning(f"Could not delete existing objects: {e}")

    # Create parametric eyes group
    cmds.group(empty=True, name=const.PARAMETRIC_EYES_GRP)

    # Place the group under HEADRIG_GRP if it exists
    if cmds.objExists(const.HEADRIG_GRP):
        try:
            cmds.parent(const.PARAMETRIC_EYES_GRP, const.HEADRIG_GRP)
        except Exception as e:
            _logger.warning(f"Could not parent {const.PARAMETRIC_EYES_GRP} to {const.HEADRIG_GRP}: {e}")

    # Create left eye driver
    left_eye_driver = cmds.polySphere(radius=1, subdivisionsX=20, subdivisionsY=20, name=const.EYE_SHAPE_DRIVER_L)[0]
    left_cornea_driver = cmds.polySphere(radius=1, subdivisionsX=20, subdivisionsY=20, name=const.CORNEA_DRIVER_L)[0]

    # Hide driver shape
    cmds.setAttr(f"{left_eye_driver}.visibility", False)
    cmds.setAttr(f"{left_cornea_driver}.visibility", False)

    # Set the shape node to template display mode
    shape_node = cmds.listRelatives(left_eye_driver, shapes=True)[0]
    cmds.setAttr(f"{shape_node}.overrideEnabled", 1)
    cmds.setAttr(f"{shape_node}.overrideDisplayType", 2)
    shape_node = cmds.listRelatives(left_cornea_driver, shapes=True)[0]
    cmds.setAttr(f"{shape_node}.overrideEnabled", 1)
    cmds.setAttr(f"{shape_node}.overrideDisplayType", 2)

    # Create right eye driver
    right_eye_driver = cmds.polySphere(radius=1, subdivisionsX=20, subdivisionsY=20, name=const.EYE_SHAPE_DRIVER_R)[0]
    right_cornea_driver = cmds.polySphere(radius=1, subdivisionsX=20, subdivisionsY=20, name=const.CORNEA_DRIVER_R)[0]

    # Hide driver shape
    cmds.setAttr(f"{right_eye_driver}.visibility", False)
    cmds.setAttr(f"{right_cornea_driver}.visibility", False)

    # Set the shape node to template display mode
    shape_node = cmds.listRelatives(right_eye_driver, shapes=True)[0]
    cmds.setAttr(f"{shape_node}.overrideEnabled", 1)
    cmds.setAttr(f"{shape_node}.overrideDisplayType", 2)
    shape_node = cmds.listRelatives(right_cornea_driver, shapes=True)[0]
    cmds.setAttr(f"{shape_node}.overrideEnabled", 1)
    cmds.setAttr(f"{shape_node}.overrideDisplayType", 2)

    # Create zero groups for both drivers
    cmds.group(empty=True, name=const.EYE_DRIVER_L_ZERO)
    cmds.group(empty=True, name=const.EYE_DRIVER_R_ZERO)

    cmds.group(empty=True, name=const.CORNEA_DRIVER_L_ZERO)
    cmds.group(empty=True, name=const.CORNEA_DRIVER_R_ZERO)

    # Parent both zero groups to the parametric eyes group
    try:
        cmds.parent(left_eye_driver, const.EYE_DRIVER_L_ZERO)
        cmds.parent(right_eye_driver, const.EYE_DRIVER_R_ZERO)

        cmds.parent(left_cornea_driver, const.CORNEA_DRIVER_L_ZERO)
        cmds.parent(right_cornea_driver, const.CORNEA_DRIVER_R_ZERO)

        cmds.parent(
            [
                const.EYE_DRIVER_L_ZERO,
                const.EYE_DRIVER_R_ZERO,
                const.CORNEA_DRIVER_L_ZERO,
                const.CORNEA_DRIVER_R_ZERO,
            ],
            const.PARAMETRIC_EYES_GRP,
        )
    except Exception as e:
        _logger.warning(f"Parent eye driver groups failed: {e}")

    # Align the eye drivers to match the eye meshes
    align_eyes_drivers()
    # Create wrap deformer
    create_wrap_deformer()
    # Create expressions
    create_symmetric_expressions()

    end_time = time.time()
    _logger.info(f"Time taken to create eye drivers: {end_time - start_time} seconds")


def align_eyes_drivers():
    """Align eye eye_driver_zero_space objects to match the eyeball meshes.

    This function uses the align_object_to_target function from mesh_align module to align
    the eye eye_driver_zero_space objects to the eyeball meshes based on anchor points defined in the config.
    """
    eye_config = get_eye_config()

    # Process both eyes using the same logic
    for eye_driver, eye_driver_zero_space, eye_driven, eye_anchor_key, cornea_driver_zero_space, cornea_anchor_key, in [
        (
            const.EYE_SHAPE_DRIVER_L,
            const.EYE_DRIVER_L_ZERO,
            const.EYE_DRIVEN_L,
            "eyeball_left_anchors",
            const.CORNEA_DRIVER_L_ZERO,
            "cornea_left_coordinate_anchors",
        ),
        (
            const.EYE_SHAPE_DRIVER_R,
            const.EYE_DRIVER_R_ZERO,
            const.EYE_DRIVEN_R,
            "eyeball_right_anchors",
            const.CORNEA_DRIVER_R_ZERO,
            "cornea_right_coordinate_anchors",
        ),
    ]:
        # Get the appropriate anchor indices for each eye
        eyeball_anchors_indices = eye_config["eye_anchor_info"][eye_anchor_key]
        cornea_anchors_indices = eye_config["eye_anchor_info"][cornea_anchor_key]

        # Use the mesh_align function to align the eye_driver_zero_space to the target
        (
            eye_center_position,
            front_axis,
            eye_scale_values,
            cornea_center_position,
            cornea_scale_values,
        ) = fit_sphere_drivers_to_eyes(
            eye_driver,
            eye_driver_zero_space,
            eye_driven,
            eyeball_anchors_indices,
            cornea_driver_zero_space,
            cornea_anchors_indices,
        )


def fit_sphere_drivers_to_eyes(
    eye_driver,
    eye_driver_zero_space,
    eye_driven,
    driven_anchor_indices,
    cornea_driver_zero_space,
    cornea_anchor_indices,
):
    """Align driver objects to target meshes using anchor points.

    This function aligns both eyeball and cornea driver objects to their target meshes
    by matching anchor points. It sets the position, rotation, and scale of the drivers
    to match the targets, and also adjusts the pivot points.

    Args:
        eye_driver (str): The name of the eyeball driver object
        eye_driver_zero_space (str): The name of the zero space group for the eyeball driver
        eye_driven (str): The name of the target eyeball mesh to align to
        driven_anchor_indices (list): List of vertex indices on the eyeball mesh in order:
                                  [top, front, bottom, back, right, left]
        cornea_driver_zero_space (str): The name of the cornea driver object
        cornea_anchor_indices (list): List of vertex indices for cornea alignment in order:
                                  [top, bottom, left, right, front]

    Returns:
        tuple: (eye_center_position, front_axis, eye_scale_values, cornea_center_position, cornea_scale_values)
              - eye_center_position: The calculated center position of the eyeball
              - front_axis: The front axis vector of the eyeball
              - eye_scale_values: The scale values used for the eyeball alignment (x, y, z)
              - cornea_center_position: The calculated center position of the cornea
              - cornea_scale_values: The scale values used for the cornea alignment (x, y, z)
    """
    ################################ Eyeball ###################################
    # Get vertex positions from target mesh
    target_anchors_positions = mesh_align.get_point_positions(eye_driven, driven_anchor_indices)

    # Calculate center point between right (index 4) and left (index 5) vertices
    target_center_position = (target_anchors_positions[4] + target_anchors_positions[5]) / 2.0

    # Calculate distances for scaling
    # 1. Calculate distance between right vertex (index 4) and center for scaleX
    scale_x = np.linalg.norm(target_anchors_positions[4] - target_center_position)
    # 2. Calculate distance between top vertex (index 0) and center for scaleY
    scale_y = np.linalg.norm(target_anchors_positions[0] - target_center_position)
    # 3. Calculate distance between front vertex (index 1) and center for scaleZ
    scale_z = np.linalg.norm(target_anchors_positions[1] - target_center_position)

    # Calculate front axis (vector from center to front vertex)
    target_front_axis = target_anchors_positions[1] - target_center_position
    # Normalize the front axis vector
    target_front_axis = target_front_axis / np.linalg.norm(target_front_axis)

    # First reset transformations
    cmds.rotate(0, 0, 0, eye_driver_zero_space, absolute=True)
    cmds.scale(1, 1, 1, eye_driver_zero_space, absolute=True)

    # 1. Position the driver at the target center position
    cmds.xform(eye_driver_zero_space, translation=target_center_position.tolist(), worldSpace=True)

    # 2. Calculate and apply rotation to align driver's z-axis with the front axis
    # Create a temporary locator at the target position (center + front_axis)
    target_pos = target_center_position + target_front_axis

    # Use Maya's built-in function to create a rotation matrix
    # that points the z-axis toward the target

    # Create vectors for the calculation
    eye_pos = om2.MVector(*target_center_position)
    target = om2.MVector(*target_pos)

    # Calculate the direction vector (aim vector)
    aim_vector = (target - eye_pos).normalize()

    # Define the up vector
    world_up = om2.MVector(0, 0, 1) if cmds.upAxis(q=True, axis=True).lower() == "z" else om2.MVector(0, 1, 0)

    # Calculate the right vector (perpendicular to aim and up)
    right_vector = (aim_vector ^ world_up).normalize()  # Cross product

    # Recalculate a perpendicular up vector
    up_vector = (right_vector ^ aim_vector).normalize()  # Cross product

    # Create rotation matrix from these vectors
    rotation_matrix = [
        right_vector.x,
        right_vector.y,
        right_vector.z,
        0,
        up_vector.x,
        up_vector.y,
        up_vector.z,
        0,
        aim_vector.x,
        aim_vector.y,
        aim_vector.z,
        0,
        0,
        0,
        0,
        1,
    ]

    # Store the current position before applying rotation matrix
    current_position = target_center_position.tolist()

    # Apply the rotation matrix to the driver (this will reset position)
    cmds.xform(eye_driver_zero_space, matrix=rotation_matrix, worldSpace=True)

    # Re-apply the position after setting rotation
    cmds.xform(eye_driver_zero_space, translation=current_position, worldSpace=True)

    # 3. Apply the calculated scale values to the driver
    cmds.scale(scale_x, scale_y, scale_z, eye_driver_zero_space, absolute=True)

    # Store the center positions and front axis for future reference
    # Convert MVector to Python list manually
    aim_vector_list = [aim_vector.x, aim_vector.y, aim_vector.z]

    ################################ Cornea ###################################

    cornea_anchors_positions = mesh_align.get_point_positions(eye_driven, cornea_anchor_indices)

    cornea_center_position = (cornea_anchors_positions[2] + cornea_anchors_positions[3]) / 2.0

    cornea_driver_scale_x = np.linalg.norm(cornea_anchors_positions[2] - cornea_center_position)
    cornea_driver_scale_y = np.linalg.norm(cornea_anchors_positions[0] - cornea_center_position)

    cmds.xform(cornea_driver_zero_space, matrix=rotation_matrix, worldSpace=True)
    cmds.xform(cornea_driver_zero_space, translation=cornea_center_position.tolist(), worldSpace=True)
    cmds.scale(
        cornea_driver_scale_x,
        cornea_driver_scale_y,
        cornea_driver_scale_x,
        cornea_driver_zero_space,
        absolute=True,
    )

    # Freeze transformations
    cmds.makeIdentity(
        eye_driver_zero_space,
        apply=True,
        translate=False,
        rotate=False,
        scale=True,
        normal=False,
        jointOrient=False,
    )
    cmds.makeIdentity(
        cornea_driver_zero_space,
        apply=True,
        translate=False,
        rotate=False,
        scale=True,
        normal=False,
        jointOrient=False,
    )

    # Adjust eye driver pivot
    pivot_translation_vector = om2.MVector(aim_vector.x * scale_z, aim_vector.y * scale_z, aim_vector.z * scale_z)
    cmds.move(
        pivot_translation_vector.x,
        pivot_translation_vector.y,
        pivot_translation_vector.z,
        f"{eye_driver}.scalePivot",
        f"{eye_driver}.rotatePivot",
        relative=True,
    )

    return (
        current_position,
        aim_vector_list,
        (scale_x, scale_y, scale_z),
        cornea_center_position.tolist(),
        (
            cornea_driver_scale_x,
            cornea_driver_scale_y,
            cornea_driver_scale_x,
        ),
    )


def create_wrap_deformer():
    """Create wrap deformers for eye and cornea control."""
    required_objects = [
        const.CORNEA_DRIVER_L,
        const.EYE_DRIVEN_L,
        const.EYE_SHAPE_DRIVER_L,
        const.CORNEA_DRIVER_R,
        const.EYE_DRIVEN_R,
        const.EYE_SHAPE_DRIVER_R,
    ]

    if any(not cmds.objExists(obj) for obj in required_objects):
        _logger.warning("Geometry does not exist")
        return

    eye_config = get_eye_config()

    # Define wrap deformer settings once
    # doWrapArgList command parameters explained:
    # "7"   - Command version
    # "1"   - Influence type: 1=Face, 0=Point (determines how the deformer calculates influences)
    # "0"   - Exclusive bind: 0=Off, 1=On (when off, allows multiple deformers to affect the same area)
    # "1"   - Auto weight threshold: 1=On, 0=Off (when on, automatically calculates weights)
    # "2"   - Render influences: 2=Post, 1=Pre, 0=None (determines when the deformation is applied)
    # "1"   - Weight threshold: Range 0-1, controls the falloff threshold for influence
    # "1"   - Max distance: Controls the maximum distance for influence
    # "0"   - Smooth factor: Controls the smoothness of weight transitions
    # "0"   - Preserve maps: 0=Don't preserve, 1=Preserve (preserves UV and color maps)
    wrap_settings = '"7" { "1", "0", "1", "2", "1", "1", "0", "0" }'

    # Function to create wrap deformer
    def create_wrap(driven, driver):
        cmds.select(clear=True)
        cmds.select(driven)
        cmds.select(driver, add=True)
        return mel.eval(f"doWrapArgList {wrap_settings}")

    # Create cornea wrap deformers
    # Left cornea
    cornea_left_verts_list = eye_config["eye_vertices"]["cornea_left_verts"]
    cornea_left_driven = [const.EYE_DRIVEN_L + verts for verts in cornea_left_verts_list]
    create_wrap(cornea_left_driven, const.CORNEA_DRIVER_L)

    # Right cornea
    cornea_right_verts_list = eye_config["eye_vertices"]["cornea_right_verts"]
    cornea_right_driven = [const.EYE_DRIVEN_R + verts for verts in cornea_right_verts_list]
    create_wrap(cornea_right_driven, const.CORNEA_DRIVER_R)

    # Create eyeball wrap deformers
    # Left eyeball
    create_wrap(const.EYE_DRIVEN_L, const.EYE_SHAPE_DRIVER_L)

    # Right eyeball
    create_wrap(const.EYE_DRIVEN_R, const.EYE_SHAPE_DRIVER_R)

    cmds.dgdirty(a=True)
    cmds.refresh()


def check_driver_alignment():
    """Check driver alignment (placeholder function)."""


def set_eyeball_curvature(curvature_multiplier, eye_selection=None):
    """Set eyeball curvature by adjusting Z-scale.

    Args:
        curvature_multiplier (float): Multiplier for the curvature
        eye_selection (EyeSelection, optional): Which eye to modify. If None, uses current selection.
    """
    if eye_selection is None:
        eye_selection = get_selected_eye()

    eye_driver = const.EYE_SHAPE_DRIVER_L if eye_selection == EyeSelection.LEFT else const.EYE_SHAPE_DRIVER_R
    curvature = curvature_multiplier * cmds.getAttr(f"{eye_driver}.scaleX")
    cmds.setAttr(f"{eye_driver}.scaleZ", curvature)


def set_eyeball_size(size, eye_selection=None):
    """Set eyeball size while maintaining curvature ratio.

    Args:
        size (float): The new size for the eyeball
        eye_selection (EyeSelection, optional): Which eye to modify. If None, uses current selection.
    """
    if eye_selection is None:
        eye_selection = get_selected_eye()

    eye_driver = const.EYE_SHAPE_DRIVER_L if eye_selection == EyeSelection.LEFT else const.EYE_SHAPE_DRIVER_R

    curvature_ratio = cmds.getAttr(f"{eye_driver}.scaleZ") / cmds.getAttr(f"{eye_driver}.scaleX")
    cmds.setAttr(f"{eye_driver}.scaleX", size)
    cmds.setAttr(f"{eye_driver}.scaleY", size)
    cmds.setAttr(f"{eye_driver}.scaleZ", size * curvature_ratio)


def set_cornea_curvature(curvature, symmetric_mode=None, eye_selection=None):
    """Set cornea curvature.

    Args:
        curvature (float): The curvature value to set
        symmetric_mode (bool, optional): Whether to apply symmetrically. If None, uses current mode.
        eye_selection (EyeSelection, optional): Which eye to modify. If None, uses current selection.
    """
    if symmetric_mode is None:
        symmetric_mode = get_symmetric_mode()
    if eye_selection is None:
        eye_selection = get_selected_eye()

    target_drivers = []
    if symmetric_mode:
        target_drivers = [const.CORNEA_DRIVER_L, const.CORNEA_DRIVER_R]
    else:
        selected_driver = const.CORNEA_DRIVER_L if eye_selection == EyeSelection.LEFT else const.CORNEA_DRIVER_R
        target_drivers = [selected_driver]

    for driver in target_drivers:
        cmds.setAttr(f"{driver}.scaleZ", curvature)


def enter_free_edit_mode(eye_selection=None):
    """Enter free edit mode for manual eye driver manipulation.

    Args:
        eye_selection (EyeSelection, optional): Which eye to edit. If None, uses current selection.
    """
    if eye_selection is None:
        eye_selection = get_selected_eye()

    eye_driver = const.EYE_SHAPE_DRIVER_L if eye_selection == EyeSelection.LEFT else const.EYE_SHAPE_DRIVER_R
    cmds.select(eye_driver, replace=True)
    # "moveSuperContext", "RotateSuperContext", "scaleSuperContext"
    cmds.setToolTo("moveSuperContext")

    delete_symmetric_expressions()
    create_symmetric_expressions()


def exit_free_edit_mode():
    """Exit free edit mode and clean up any expressions."""
    cmds.select(clear=True)


def create_symmetric_expressions(symmetric_mode=None, eye_selection=None):
    """Create expressions for symmetric eye control.

    Args:
        symmetric_mode (bool, optional): Whether to create symmetric expressions. If None, uses current mode.
        eye_selection (EyeSelection, optional): Which eye is the source. If None, uses current selection.
    """
    if symmetric_mode is None:
        symmetric_mode = get_symmetric_mode()
    if eye_selection is None:
        eye_selection = get_selected_eye()

    if symmetric_mode:
        selected_eye_driver = (
            const.EYE_SHAPE_DRIVER_L if eye_selection == EyeSelection.LEFT else const.EYE_SHAPE_DRIVER_R
        )
        mirrored_side_eye_driver = (
            const.EYE_SHAPE_DRIVER_R if eye_selection == EyeSelection.LEFT else const.EYE_SHAPE_DRIVER_L
        )

        expr_name = f"{mirrored_side_eye_driver}_mirror_transform"
        if cmds.objExists(expr_name):
            try:
                cmds.delete(expr_name)
            except Exception as e:
                _logger.warning(f"Could not delete expression {expr_name}: {e}")

        up_axis = cmds.upAxis(q=True, axis=True).lower()

        if up_axis == "y":
            expr_string = f"""
            {mirrored_side_eye_driver}.translateX = -{selected_eye_driver}.translateX;
            {mirrored_side_eye_driver}.translateY = {selected_eye_driver}.translateY;
            {mirrored_side_eye_driver}.translateZ = {selected_eye_driver}.translateZ;

            {mirrored_side_eye_driver}.rotateX = {selected_eye_driver}.rotateX;
            {mirrored_side_eye_driver}.rotateY = -{selected_eye_driver}.rotateY;
            {mirrored_side_eye_driver}.rotateZ = -{selected_eye_driver}.rotateZ;

            {mirrored_side_eye_driver}.scaleX = {selected_eye_driver}.scaleX;
            {mirrored_side_eye_driver}.scaleY = {selected_eye_driver}.scaleY;
            {mirrored_side_eye_driver}.scaleZ = {selected_eye_driver}.scaleZ;
            """
        else:  # z-up
            expr_string = f"""
            {mirrored_side_eye_driver}.translateX = -{selected_eye_driver}.translateX;
            {mirrored_side_eye_driver}.translateY = {selected_eye_driver}.translateY;
            {mirrored_side_eye_driver}.translateZ = {selected_eye_driver}.translateZ;

            {mirrored_side_eye_driver}.rotateX = {selected_eye_driver}.rotateX;
            {mirrored_side_eye_driver}.rotateY = -{selected_eye_driver}.rotateY;
            {mirrored_side_eye_driver}.rotateZ = -{selected_eye_driver}.rotateZ;

            {mirrored_side_eye_driver}.scaleX = {selected_eye_driver}.scaleX;
            {mirrored_side_eye_driver}.scaleY = {selected_eye_driver}.scaleY;
            {mirrored_side_eye_driver}.scaleZ = {selected_eye_driver}.scaleZ;
            """

        cmds.expression(
            name=expr_name,
            string=expr_string,
            object=mirrored_side_eye_driver,
        )


def delete_symmetric_expressions():
    """Delete the symmetric expressions for the eyes."""
    eye_drivers = [const.EYE_SHAPE_DRIVER_L, const.EYE_SHAPE_DRIVER_R]

    for eye_driver in eye_drivers:
        expr_name = f"{eye_driver}_mirror_transform"
        if cmds.objExists(expr_name):
            try:
                cmds.delete(expr_name)
            except Exception as e:
                _logger.warning(f"Could not delete expression {expr_name}: {e}")


def rotate_eyeball(rotate_x, rotate_y, eye_selection=None):
    """Rotate the eyeball based on normalized coordinates.

    Args:
        rotate_x (float): Rotate angle in degrees along the X-axis
        rotate_y (float): Rotate angle in degrees along the Y-axis
        eye_selection (EyeSelection, optional): Which eye to rotate. If None, uses current selection.
    """
    if eye_selection is None:
        eye_selection = get_selected_eye()

    eye_driver = const.EYE_SHAPE_DRIVER_L if eye_selection == EyeSelection.LEFT else const.EYE_SHAPE_DRIVER_R
    cmds.setAttr(f"{eye_driver}.rotateY", -rotate_x)
    cmds.setAttr(f"{eye_driver}.rotateX", -rotate_y)


def reset_eye_drivers():
    """Reset the eye drivers to their default positions and scales."""
    create_eyeball_drivers()
    cmds.select(clear=True)


def _reset_curvature(symmetric_mode=None, eye_selection=None):
    """Reset the curvature of the eyeball.

    Args:
        symmetric_mode (bool, optional): Whether to reset both eyes. If None, uses current mode.
        eye_selection (EyeSelection, optional): Which eye to reset. If None, uses current selection.
    """
    if symmetric_mode is None:
        symmetric_mode = get_symmetric_mode()
    if eye_selection is None:
        eye_selection = get_selected_eye()

    if symmetric_mode:
        eye_drivers = [const.EYE_SHAPE_DRIVER_L, const.EYE_SHAPE_DRIVER_R]
    else:
        eye_drivers = [
            const.EYE_SHAPE_DRIVER_L if eye_selection == EyeSelection.LEFT else const.EYE_SHAPE_DRIVER_R,
        ]

    for eye_driver in eye_drivers:
        curvature = cmds.getAttr(f"{eye_driver}.scaleX")
        cmds.setAttr(f"{eye_driver}.scaleZ", curvature)


def auto_match_eye_drivers(symmetric_mode=None, eye_selection=None):
    """Automatically match the eye drivers to eyelids.

    Args:
        symmetric_mode (bool, optional): Whether to match both eyes. If None, uses current mode.
        eye_selection (EyeSelection, optional): Which eye to match. If None, uses current selection.
    """
    if symmetric_mode is None:
        symmetric_mode = get_symmetric_mode()
    if eye_selection is None:
        eye_selection = get_selected_eye()

    # Delete existing expressions
    delete_symmetric_expressions()

    # Reset curvature
    _reset_curvature(symmetric_mode, eye_selection)

    eye_config = get_eye_config()

    # Helper function to align eye driver
    def align_eye_driver(driver, driven, eyelid_key, eyeball_key):
        # Duplicate the eye driver
        driver_copy = cmds.duplicate(driver)[0]
        parent = cmds.listRelatives(driver_copy, parent=True, fullPath=True)
        if parent:
            cmds.parent(driver_copy, world=True)
        _reset_pivot_and_align_world(driver_copy)

        # Create a parent constraint
        parent_constraint = cmds.parentConstraint(driver_copy, driver, maintainOffset=True)
        scale_constraint = cmds.scaleConstraint(driver_copy, driver, maintainOffset=True)

        # Get anchor indices from config
        eyelid_anchor_indices = eye_config["eye_anchor_info"][eyelid_key]
        eyeball_anchor_indices = eye_config["eye_anchor_info"][eyeball_key]

        # Get anchor points
        eyelid_anchor_points = mesh_align.get_point_positions(const.BASE_HEAD_MESH_NAME, eyelid_anchor_indices)
        eyeball_anchor_points = mesh_align.get_point_positions(driven, eyeball_anchor_indices)

        # Compute and apply transformation
        transform_matrix = mesh_align.compute_rigid_transform(eyelid_anchor_points, eyeball_anchor_points)
        cmds.xform(driver_copy, matrix=transform_matrix.T.flatten().tolist(), os=True)

        # Clean up (Note: delete constraint first, then delete driver copy)
        cmds.delete(scale_constraint)
        cmds.delete(parent_constraint)
        cmds.delete(driver_copy)

    if symmetric_mode or (eye_selection == EyeSelection.LEFT and not symmetric_mode):
        # Align left eye driver
        align_eye_driver(
            const.EYE_SHAPE_DRIVER_L,
            const.EYE_DRIVEN_L,
            "left_eyelid_align_anchors",
            "left_eyeball_align_anchors",
        )

    if symmetric_mode or (eye_selection == EyeSelection.RIGHT and not symmetric_mode):
        # Align right eye driver
        align_eye_driver(
            const.EYE_SHAPE_DRIVER_R,
            const.EYE_DRIVEN_R,
            "right_eyelid_align_anchors",
            "right_eyeball_align_anchors",
        )

    # Re-create expressions
    create_symmetric_expressions(symmetric_mode, eye_selection)


def _reset_pivot_and_align_world(obj):
    """Reset pivot and align object to world coordinates.

    Args:
        obj (str): The object to reset
    """
    # Ensure pivot operations are performed in absolute coordinate system
    cmds.xform(obj, pivots=(0, 0, 0), worldSpace=True)
    # Synchronize rotation and scale pivots
    cmds.xform(obj, scalePivot=(0, 0, 0), rotatePivot=(0, 0, 0), worldSpace=True)
    # Freeze transformations (reset local rotation/translation/scale while maintaining world position)
    cmds.makeIdentity(obj, apply=True, translate=True, rotate=True, scale=True)
    # Directly reset rotation attributes (optional, ensures local rotation is zeroed)
    cmds.setAttr(f"{obj}.rotate", 0, 0, 0, type="double3")


def get_eye_size(eye_selection=None):
    """Get the current eye scale.

    Args:
        eye_selection (EyeSelection, optional): Which eye to get size for. If None, uses current selection.

    Returns:
        float: The current scale value of the selected eye
    """
    if eye_selection is None:
        eye_selection = get_selected_eye()

    eye_driver = const.EYE_SHAPE_DRIVER_L if eye_selection == EyeSelection.LEFT else const.EYE_SHAPE_DRIVER_R
    return cmds.getAttr(f"{eye_driver}.scaleX")


def get_eyeball_anchor_pos():
    """Get the position of the eyeball anchors.

    Returns:
        dict: Dictionary containing anchor positions for both eyes in world space.
            Structure:
            {
                "left": {
                    "top": [x, y, z],       # Top anchor position
                    "front": [x, y, z],     # Front anchor position
                    "bottom": [x, y, z],    # Bottom anchor position
                    "back": [x, y, z],      # Back anchor position
                    "right": [x, y, z],     # Right anchor position
                    "left": [x, y, z],      # Left anchor position
                    "center": [x, y, z]     # Center position (midpoint of front and back)
                },
                "right": {
                    "top": [x, y, z],       # Top anchor position
                    "front": [x, y, z],     # Front anchor position
                    "bottom": [x, y, z],    # Bottom anchor position
                    "back": [x, y, z],      # Back anchor position
                    "right": [x, y, z],     # Right anchor position
                    "left": [x, y, z],      # Left anchor position
                    "center": [x, y, z]     # Center position (midpoint of front and back)
                }
            }
    """
    if not cmds.objExists(const.EYE_DRIVEN_L) or not cmds.objExists(const.EYE_DRIVEN_R):
        return None

    eye_config = get_eye_config()

    anchors_positions_left = mesh_align.get_point_positions(
        const.EYE_DRIVEN_L,
        eye_config["eye_anchor_info"]["eyeball_left_anchors"],
    )
    anchors_positions_right = mesh_align.get_point_positions(
        const.EYE_DRIVEN_R,
        eye_config["eye_anchor_info"]["eyeball_right_anchors"],
    )

    # Define anchor position names corresponding to indices
    anchor_names = ["top", "front", "bottom", "back", "right", "left"]

    def build_eye_positions(anchors_positions):
        """Build position dictionary for a single eye."""
        positions = {}
        # Map anchor positions by name
        for i, name in enumerate(anchor_names):
            positions[name] = anchors_positions[i]
        # Calculate center as midpoint between front and back
        positions["center"] = (anchors_positions[0] + anchors_positions[2]) / 2.0
        return positions

    pos_dict = {
        "left": build_eye_positions(anchors_positions_left),
        "right": build_eye_positions(anchors_positions_right),
    }

    return pos_dict
