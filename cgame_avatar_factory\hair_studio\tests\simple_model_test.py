#!/usr/bin/env python
"""Simple test for AssetListModel - minimal version for debugging."""

# Import built-in modules
import os
import sys

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def test_imports():
    """Test if we can import required modules."""
    print("Testing imports...")

    try:
        print("1. Testing qtpy import...")
        print("   ✓ qtpy imported successfully")

        print("2. Testing AssetListModel import...")
        print("   ✓ AssetListModel imported successfully")

        return True

    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False


def test_basic_functionality():
    """Test basic model functionality."""
    print("\nTesting basic functionality...")

    try:
        # Import third-party modules
        from qtpy import QtCore
        from qtpy import QtWidgets

        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_list_model import (
            AssetListModel,
        )

        # Create QApplication if needed
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication([])
            print("   ✓ QApplication created")
        else:
            print("   ✓ QApplication already exists")

        # Test model creation
        print("3. Testing model creation...")
        model = AssetListModel()
        print("   ✓ AssetListModel created successfully")

        # Test initial state
        print("4. Testing initial state...")
        assert model.rowCount() == 0, f"Expected 0 rows, got {model.rowCount()}"
        assert model.getAssets() == [], "Expected empty assets list"
        print("   ✓ Initial state correct")

        # Test setting assets
        print("5. Testing asset setting...")
        test_assets = [
            {"id": "test1", "name": "Test Asset 1", "asset_type": "card"},
            {"id": "test2", "name": "Test Asset 2", "asset_type": "xgen"},
        ]

        model.setAssets(test_assets)
        assert model.rowCount() == 2, f"Expected 2 rows, got {model.rowCount()}"
        print("   ✓ Assets set successfully")

        # Test data retrieval
        print("6. Testing data retrieval...")
        index = model.createIndex(0, 0)
        display_data = model.data(index, QtCore.Qt.DisplayRole)
        assert display_data == "Test Asset 1", f"Expected 'Test Asset 1', got '{display_data}'"
        print("   ✓ Data retrieval works")

        return True

    except AssertionError as e:
        print(f"   ❌ Assertion failed: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        # Import built-in modules
        import traceback

        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("=" * 50)
    print("AssetListModel Simple Test")
    print("=" * 50)

    # Test imports
    imports_ok = test_imports()
    if not imports_ok:
        print("\n❌ Import tests failed - cannot proceed")
        return False

    # Test basic functionality
    functionality_ok = test_basic_functionality()
    if not functionality_ok:
        print("\n❌ Functionality tests failed")
        return False

    print("\n" + "=" * 50)
    print("🎉 All tests passed!")
    print("✅ AssetListModel is working correctly")
    print("✅ Ready to proceed to next module")
    print("=" * 50)

    return True


if __name__ == "__main__":
    success = main()

    # Don't exit immediately in case we're in a terminal that closes
    if not success:
        print("\nPress Enter to exit...")
        try:
            input()
        except:
            pass

    sys.exit(0 if success else 1)
