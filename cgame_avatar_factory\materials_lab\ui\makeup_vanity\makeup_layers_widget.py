"""
Makeup Layers Widget Module

This widget displays and manages makeup layers.
"""

# Import built-in modules
import logging

# Import third-party modules
import dayu_widgets
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.common import constants as const
from cgame_avatar_factory.common.ui.layout import FramelessHLayout
from cgame_avatar_factory.common.ui.layout import FramelessVLayout
from cgame_avatar_factory.common.ui.mixin.style_mixin import MStyleMixin


class LayersListWidget(QtWidgets.QListWidget):
    """
    Custom QListWidget for makeup layers with special handling for base layer.
    """

    def __init__(self, parent=None):
        super(LayersListWidget, self).__init__(parent)
        self.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)  # Allow reordering
        self.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.setMinimumHeight(200)

    def startDrag(self, supported_actions):
        """
        Override startDrag to prevent dragging the base layer.
        """
        current_item = self.currentItem()
        if current_item:
            # Get the layer data
            layer_data = current_item.data(QtCore.Qt.UserRole)
            # Check if this is the base layer
            if layer_data and layer_data.get("is_base_layer", False):
                # Don't start drag for base layer
                return

        # For other layers, proceed with normal drag operation
        super(LayersListWidget, self).startDrag(supported_actions)


class LayerItemWidget(QtWidgets.QWidget):
    """
    Custom widget for displaying a makeup layer item in the layers list.
    Similar to Photoshop layer item with visibility toggle and name.
    """

    def __init__(self, layer_data, parent=None):
        super(LayerItemWidget, self).__init__(parent)
        self.layer_data = layer_data
        self.setup_ui()

    def setup_ui(self):
        """Initialize UI components"""
        # Use vertical layout to center items
        main_layout = QtWidgets.QVBoxLayout()
        main_layout.setContentsMargins(8, 0, 8, 0)

        # Create horizontal layout for checkbox and label
        content_layout = FramelessHLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)

        # Add horizontal layout to vertical layout with vertical alignment
        main_layout.addLayout(content_layout)
        main_layout.setAlignment(QtCore.Qt.AlignVCenter)

        self.setLayout(main_layout)

        # Visibility checkbox
        self.visibility_checkbox = QtWidgets.QCheckBox()
        self.visibility_checkbox.setChecked(True)
        self.visibility_checkbox.setToolTip("Toggle layer visibility")

        # Make checkbox larger
        checkbox_size = QtCore.QSize(20, 20)
        self.visibility_checkbox.setIconSize(checkbox_size)

        # Layer name label
        self.name_label = QtWidgets.QLabel(self.layer_data.get("name", "Unnamed Layer"))

        # Match font size with add_layer_button
        font = self.name_label.font()
        font.setPointSize(font.pointSize() + 2)  # Increase font size
        self.name_label.setFont(font)

        # Special styling for base layer
        if self.layer_data.get("is_base_layer", False):
            font.setBold(True)  # Make base layer name bold
            self.name_label.setFont(font)
            self.name_label.setStyleSheet(f"color: {const.APP_PRIMARY_COLOR};")  # Use theme blue color
        else:
            self.name_label.setStyleSheet("color: white;")

        # Add widgets to layout with vertical centering
        content_layout.addWidget(self.visibility_checkbox)
        content_layout.addWidget(self.name_label)
        content_layout.addStretch()

        # Set fixed height using constant
        self.setFixedHeight(80)


class MakeupLayersWidget(QtWidgets.QFrame):
    """
    Widget for displaying and managing makeup layers

    This widget accepts drag and drop of makeup items from the makeup library.
    """

    # Signal emitted when a makeup item is added to the layers
    sig_makeup_item_added = QtCore.Signal(dict)

    # Signal emitted when a makeup layer is selected
    sig_makeup_layer_selected = QtCore.Signal(dict)

    def __init__(self, parent=None):
        super(MakeupLayersWidget, self).__init__(parent)
        self.logger = logging.getLogger(__name__)
        self._makeup_layers = []
        self.setup_ui()

        # Add default base layer after UI setup
        self.add_base_layer()

    def setup_ui(self):
        """Initialize UI components"""
        self.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.main_layout = FramelessVLayout()
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.setLayout(self.main_layout)

        # Create header with title
        self.setup_header()

        # Create layers list widget
        self.setup_layers_list()

        # Create footer with buttons
        self.setup_footer()

        # Setup drag and drop
        self.setAcceptDrops(True)

    def setup_header(self):
        """Setup header with title"""
        header_layout = FramelessHLayout()

        # Title label
        title_label = dayu_widgets.MLabel("妆容图层")

        # Add widgets to layout
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # Add header to main layout
        self.main_layout.addLayout(header_layout)

    def setup_footer(self):
        """Setup footer with buttons"""
        footer_layout = FramelessHLayout()
        footer_layout.setContentsMargins(0, 5, 0, 0)

        # Create a container for the footer with background
        footer_container = QtWidgets.QFrame()
        footer_container.setFrameShape(QtWidgets.QFrame.NoFrame)
        footer_container.setFixedHeight(45)  # Set fixed height for the footer
        footer_container.setStyleSheet(
            f"""
            QFrame {{
                background-color: {const.BUTTON_BG};
                padding: 2px;
            }}
            """
        )
        # Use QVBoxLayout to wrap HLayout for vertical centering
        footer_main_layout = QtWidgets.QVBoxLayout()
        footer_main_layout.setContentsMargins(0, 0, 0, 0)
        footer_container.setLayout(footer_main_layout)

        # Create HLayout and add it to VLayout
        footer_container_layout = FramelessHLayout()
        footer_container_layout.setContentsMargins(5, 0, 5, 0)
        footer_main_layout.addLayout(footer_container_layout)

        # Set VLayout alignment to vertical center
        footer_main_layout.setAlignment(QtCore.Qt.AlignVCenter)

        # Add layer button with plus icon
        self.add_layer_button = dayu_widgets.MToolButton().icon_only().svg("add_on.svg")
        self.add_layer_button.setToolTip("添加图层")
        self.add_layer_button.setFixedSize(32, 32)  # Set fixed size for button
        self.add_layer_button.setIconSize(QtCore.QSize(20, 20))  # Set icon size
        self.add_layer_button.clicked.connect(self.slot_add_empty_layer)
        self.add_layer_button = MStyleMixin.instance_wrapper(self.add_layer_button)
        self.add_layer_button.transparent_background()

        # Delete layer button with trash icon
        self.delete_layer_button = dayu_widgets.MToolButton().icon_only().svg("trash.svg")
        self.delete_layer_button.setToolTip("删除图层")
        self.delete_layer_button.setFixedSize(32, 32)  # Set fixed size for button
        self.delete_layer_button.setIconSize(QtCore.QSize(20, 20))  # Set icon size
        self.delete_layer_button.clicked.connect(self.slot_delete_selected_layer)
        self.delete_layer_button = MStyleMixin.instance_wrapper(self.delete_layer_button)
        self.delete_layer_button.transparent_background()

        # Add widgets to layout with spacing
        footer_container_layout.addStretch()  # Push buttons to the right
        footer_container_layout.addWidget(self.add_layer_button, 0, QtCore.Qt.AlignVCenter)  # Vertical center alignment
        footer_container_layout.addSpacing(10)  # Add spacing between buttons
        footer_container_layout.addWidget(
            self.delete_layer_button,
            0,
            QtCore.Qt.AlignVCenter,
        )  # Vertical center alignment
        footer_container_layout.addSpacing(5)  # Add spacing at the end

        # Add footer container to main layout
        footer_layout.addWidget(footer_container)
        self.main_layout.addLayout(footer_layout)

    def setup_layers_list(self):
        """Setup layers list widget"""
        # Create custom list widget for layers with special handling for base layer
        self.layers_list = LayersListWidget()

        # Set list widget style with formatted string to use constants
        self.layers_list.setStyleSheet(
            f"""
            QListWidget {{
                background-color: {const.UI_COLOR_BG_DARKER};
                border: 1px solid {const.BUTTON_BORDER};
                border-radius: 4px;
                padding: 5px;
            }}
            QListWidget::item {{
                background-color: {const.UI_COLOR_BG_DARKER};
                border-radius: 2px;
                margin: 2px 0px;
                padding: 5px;
            }}
            QListWidget::item:selected {{
                background-color: {const.UI_COLOR_SELECTED};
            }}
            QListWidget::item:hover {{
                background-color: {const.BUTTON_BG};
            }}
        """
        )

        # Connect signals
        self.layers_list.itemClicked.connect(self.slot_layer_selected)
        self.layers_list.model().rowsMoved.connect(self.handle_rows_moved)

        # Add to main layout
        self.main_layout.addWidget(self.layers_list)

    def add_base_layer(self):
        """Add the default base layer that cannot be deleted"""
        if self.layers_list.count() == 0:  # Only add if no layers exist
            base_layer_data = {
                "name": "底图",
                "type": "base",
                "is_base_layer": True,  # Special flag to mark as base layer
            }
            self.add_makeup_layer(base_layer_data)
            self.logger.info("Added default base layer")

    def slot_add_empty_layer(self):
        """Add an empty layer to the list"""
        layer_name = f"Layer {self.layers_list.count()}"
        self.add_makeup_layer({"name": layer_name, "type": "empty", "is_base_layer": False})

    def slot_delete_selected_layer(self):
        """Delete the selected layer"""
        current_row = self.layers_list.currentRow()
        if current_row >= 0 and current_row < len(self._makeup_layers):
            # Check if this is the base layer (which cannot be deleted)
            if self._makeup_layers[current_row].get("is_base_layer", False):
                self.logger.warning("Cannot delete the base layer")
                return

            # Remove from list widget
            self.layers_list.takeItem(current_row)
            # Remove from data
            del self._makeup_layers[current_row]
            # Log
            self.logger.info(f"Deleted layer at index {current_row}")

    def slot_layer_selected(self, item):
        """Handle layer selection"""
        row = self.layers_list.row(item)
        if 0 <= row < len(self._makeup_layers):
            layer_data = self._makeup_layers[row]

            # Create a formatted summary of the layer information
            layer_type = layer_data.get("type", "unknown")
            is_base = "Yes" if layer_data.get("is_base_layer", False) else "No"

            # Format the layer info
            layer_info = f"Layer: {layer_data.get('name', 'Unnamed')}, Type: {layer_type}, Base Layer: {is_base}"

            # Add any additional properties that might be useful
            if "item_path" in layer_data:
                layer_info += f", Path: {layer_data['item_path']}"
            if "category" in layer_data:
                layer_info += f", Category: {layer_data['category']}"

            # Log the formatted information
            self.logger.info(f"Selected layer: {layer_info}")

            # Also show a status message (if the parent widget has a statusBar)
            # Use a safer approach to find the status bar
            try:
                # Get the top-level window
                top_window = self.window()
                if top_window and hasattr(top_window, "statusBar"):
                    top_window.statusBar().showMessage(layer_info, 3000)  # Show for 3 seconds
            except Exception as e:
                # Just log the error and continue - this is not critical functionality
                self.logger.debug(f"Could not show status message: {e}")

            # Emit signal with layer data
            self.sig_makeup_layer_selected.emit(layer_data)

    def handle_rows_moved(self, source_parent, source_start, source_end, dest_parent, dest_row):
        """Handle data synchronization after layers are reordered by drag and drop"""
        # Only handle single item moves for now
        if source_start == source_end:
            # Find the base layer index (should be the last item in our bottom-up view)
            base_layer_index = -1
            for i, layer in enumerate(self._makeup_layers):
                if layer.get("is_base_layer", False):
                    base_layer_index = i
                    break

            # Check if we're trying to move the base layer
            if base_layer_index == source_start:
                self.logger.warning("Cannot move the base layer")
                self.restore_layers_state(source_start)
                return

            # Prevent moving items below the base layer
            if base_layer_index >= 0 and dest_row > base_layer_index:
                self.logger.warning("Cannot move layers below the base layer")
                self.restore_layers_state(source_start)
                return

            # Get the moved item
            moved_item = self._makeup_layers.pop(source_start)
            # Insert at new position
            if dest_row > source_start:
                dest_row -= 1  # Adjust destination index because we removed an item
            self._makeup_layers.insert(dest_row, moved_item)
            self.logger.info(f"Layer moved from position {source_start} to {dest_row}")

            # Rebuild the list widget to ensure all custom widgets are properly set
            # This prevents issues with missing checkboxes and labels after multiple drag operations
            self.rebuild_layers_list()

            # Select the moved item at its new position
            new_position = dest_row
            if 0 <= new_position < self.layers_list.count():
                self.layers_list.setCurrentRow(new_position)

    def rebuild_layers_list(self):
        """Rebuild the layers list widget with current data"""
        # Remember the current selection
        current_row = self.layers_list.currentRow()

        # Clear the list widget
        self.layers_list.clear()

        # Rebuild the list widget with the current data
        for layer_data in self._makeup_layers:
            # Create list item
            list_item = QtWidgets.QListWidgetItem()
            list_item.setData(QtCore.Qt.UserRole, layer_data)

            # Create custom widget for the layer item
            layer_widget = LayerItemWidget(layer_data)

            # Set size hint for the item to match the widget
            size = layer_widget.sizeHint()
            size.setHeight(80 + 3 * 2)
            list_item.setSizeHint(size)

            # Add to list
            self.layers_list.addItem(list_item)
            self.layers_list.setItemWidget(list_item, layer_widget)

        # Restore selection if possible
        if 0 <= current_row < self.layers_list.count():
            self.layers_list.setCurrentRow(current_row)

    def restore_layers_state(self, selected_row):
        """Restore the layers to their original state after an invalid drag operation"""
        # Save all the current data
        temp_data = self._makeup_layers.copy()

        # Clear the list widget
        self.layers_list.clear()

        # Rebuild the list widget with the original data
        for layer_data in temp_data:
            # Create list item
            list_item = QtWidgets.QListWidgetItem()
            list_item.setData(QtCore.Qt.UserRole, layer_data)

            # Create custom widget for the layer item
            layer_widget = LayerItemWidget(layer_data)

            # Set size hint for the item to match the widget
            size = layer_widget.sizeHint()
            size.setHeight(80 + 3 * 2)
            list_item.setSizeHint(size)

            # Add to list
            self.layers_list.addItem(list_item)
            self.layers_list.setItemWidget(list_item, layer_widget)

        # Select the original item
        if 0 <= selected_row < self.layers_list.count():
            self.layers_list.setCurrentRow(selected_row)

    def add_makeup_layer(self, item_data):
        """Add a makeup layer to the list"""
        self.logger.info(f"Adding makeup layer: {item_data['name']}")

        # Create list item
        list_item = QtWidgets.QListWidgetItem()
        list_item.setData(QtCore.Qt.UserRole, item_data)

        # Create custom widget for the layer item
        layer_widget = LayerItemWidget(item_data)

        # Set size hint for the item to match the widget
        # Add extra space for margins
        size = layer_widget.sizeHint()
        size.setHeight(80 + 3 * 2)  # Use constant height + margins
        list_item.setSizeHint(size)

        # Add to list
        self.layers_list.insertItem(0, list_item)  # Add at top
        self.layers_list.setItemWidget(list_item, layer_widget)  # Set the custom widget
        self.layers_list.setCurrentItem(list_item)  # Select it

        # Add to layers data
        self._makeup_layers.insert(0, item_data)

        # Emit signal
        self.sig_makeup_item_added.emit(item_data)

    def dragEnterEvent(self, event):
        """Handle drag enter event"""
        # Accept any drag event - we'll check the data in dropEvent
        event.acceptProposedAction()

        # Highlight the list widget to indicate it can accept the drop
        self.layers_list.setStyleSheet(
            f"""
            QListWidget {{
                background-color: {const.UI_COLOR_BG_DARKER};
                border: 2px dashed {const.APP_PRIMARY_COLOR};
                border-radius: 4px;
                padding: 5px;
            }}
            QListWidget::item {{
                background-color: {const.UI_COLOR_BG_DARKER};
                border-radius: 2px;
                margin: 2px 0px;
                padding: 5px;
            }}
            QListWidget::item:selected {{
                background-color: {const.UI_COLOR_SELECTED};
            }}
            QListWidget::item:hover {{
                background-color: {const.BUTTON_BG};
            }}
        """
        )

    def dragLeaveEvent(self, event):
        """Handle drag leave event"""
        # Reset the list widget style
        self.layers_list.setStyleSheet(
            f"""
            QListWidget {{
                background-color: {const.UI_COLOR_BG_DARKER};
                border: 1px solid {const.BUTTON_BORDER};
                border-radius: 4px;
                padding: 5px;
            }}
            QListWidget::item {{
                background-color: {const.UI_COLOR_BG_DARKER};
                border-radius: 2px;
                margin: 2px 0px;
                padding: 5px;
            }}
            QListWidget::item:selected {{
                background-color: {const.UI_COLOR_SELECTED};
            }}
            QListWidget::item:hover {{
                background-color: {const.BUTTON_BG};
            }}
        """
        )
        event.accept()

    def dragMoveEvent(self, event):
        """Handle drag move event"""
        # Accept any drag move event
        event.acceptProposedAction()

    def dropEvent(self, event):
        """Handle drop event"""
        item_data = None

        # Try to get data from custom format
        if event.mimeData().hasFormat("application/x-makeup-item"):
            try:
                data = event.mimeData().data("application/x-makeup-item").data().decode()
                item_data = eval(data)  # Convert string representation to dict
            except Exception as e:
                self.logger.error(f"Error parsing custom format data: {e}")

        # If custom format failed, try text format
        if not item_data and event.mimeData().hasText():
            try:
                data = event.mimeData().text()
                item_data = eval(data)  # Convert string representation to dict
            except Exception as e:
                self.logger.error(f"Error parsing text data: {e}")

        # Process the data if we got it
        if item_data:
            self.add_makeup_layer(item_data)
            event.acceptProposedAction()
        else:
            # If we couldn't parse the data, create a simple item with a default name
            default_item = {"name": "New Makeup Layer"}
            self.add_makeup_layer(default_item)
            event.acceptProposedAction()

        # Reset the list widget style
        self.layers_list.setStyleSheet(
            f"""
            QListWidget {{
                background-color: {const.UI_COLOR_BG_DARKER};
                border: 1px solid {const.BUTTON_BORDER};
                border-radius: 4px;
                padding: 5px;
            }}
            QListWidget::item {{
                background-color: {const.UI_COLOR_BG_DARKER};
                border-radius: 2px;
                margin: 2px 0px;
                padding: 5px;
            }}
            QListWidget::item:selected {{
                background-color: {const.UI_COLOR_SELECTED};
            }}
            QListWidget::item:hover {{
                background-color: {const.BUTTON_BG};
            }}
        """
        )
