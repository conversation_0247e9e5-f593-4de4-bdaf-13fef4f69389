# -*- coding: utf-8 -*-
"""Test edit_mode_utils module."""

# Import future modules
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

# Import built-in modules
import sys
import unittest
from unittest import mock

# 模拟所有需要的模块，避免导入 constants.py
# 模拟 Maya 模块（用于非 Maya 环境测试）
sys.modules["maya"] = mock.MagicMock()
sys.modules["maya.cmds"] = mock.MagicMock()

# 模拟 constants 模块
mock_constants = mock.MagicMock()
mock_constants.BASE_HEAD_MESH_NAME = "head_lod0_mesh"
mock_constants.GOZ_MESH_NAME = "head_GOZ_mesh"
mock_constants.GOZ_BLEND_SHAPE_SUFFIX = "blendShape"
sys.modules["cgame_avatar_factory.constants"] = mock_constants

# 创建本地 const 对象以保持测试代码兼容性
const = mock_constants

# Import local modules
from cgame_avatar_factory.face_sculpting_center.utils import edit_mode_utils


class TestEditModeUtils(unittest.TestCase):
    """Test cases for edit_mode_utils module."""

    @mock.patch("cgame_avatar_factory.api.edit_mode_utils.cmds")
    def test_create_edit_layer(self, mock_cmds):
        """Test create_edit_layer function."""
        # 设置模拟返回值
        mock_cmds.objExists.return_value = True
        mock_cmds.listAttr.return_value = []
        mock_cmds.duplicate.return_value = ["temp_mesh"]

        # 调用被测试的函数
        result = edit_mode_utils.create_edit_layer()

        # 验证结果
        self.assertEqual(result, f"{const.GOZ_MESH_NAME}_edit_layout_0")

        # 验证函数调用
        f"{const.GOZ_MESH_NAME}_{const.GOZ_BLEND_SHAPE_SUFFIX}"
        # 使用 assert_any_call 而不是 assert_called_with，因为 objExists 可能被多次调用
        mock_cmds.objExists.assert_any_call(const.BASE_HEAD_MESH_NAME)
        mock_cmds.blendShape.assert_called()
        mock_cmds.duplicate.assert_called()
        mock_cmds.delete.assert_called_with("temp_mesh")
        mock_cmds.setAttr.assert_called()

    @mock.patch("cgame_avatar_factory.api.edit_mode_utils.cmds")
    def test_select_blendshape_target(self, mock_cmds):
        """Test select_blendshape_target function."""
        # 设置模拟返回值
        mock_cmds.objExists.return_value = True
        mock_cmds.listAttr.return_value = ["target1", "target2"]

        # 调用被测试的函数
        result = edit_mode_utils.select_blendshape_target("target1")

        # 验证结果
        self.assertTrue(result)

        # 验证函数调用
        blendshape_name = f"{const.GOZ_MESH_NAME}_{const.GOZ_BLEND_SHAPE_SUFFIX}"
        mock_cmds.objExists.assert_called_with(blendshape_name)
        mock_cmds.listAttr.assert_called_with(f"{blendshape_name}.w", multi=True)
        mock_cmds.select.assert_called_with(blendshape_name, replace=True)
        mock_cmds.sculptTarget.assert_called_with(edit=True, target=0)

    @mock.patch("cgame_avatar_factory.api.edit_mode_utils.cmds")
    def test_get_blendshape_targets(self, mock_cmds):
        """Test get_blendshape_targets function."""
        # 设置模拟返回值
        mock_cmds.objExists.return_value = True
        mock_cmds.listAttr.return_value = [
            f"{const.GOZ_MESH_NAME}_edit_layout_0",
            f"{const.GOZ_MESH_NAME}_edit_layout_1",
            f"{const.GOZ_MESH_NAME}_edit_layout_2",
        ]

        # 调用被测试的函数
        result = edit_mode_utils.get_blendshape_targets()

        # 验证结果
        expected = [
            f"{const.GOZ_MESH_NAME}_edit_layout_2",
            f"{const.GOZ_MESH_NAME}_edit_layout_1",
            f"{const.GOZ_MESH_NAME}_edit_layout_0",
        ]
        self.assertEqual(result, expected)

        # 验证函数调用
        blendshape_name = f"{const.GOZ_MESH_NAME}_{const.GOZ_BLEND_SHAPE_SUFFIX}"
        mock_cmds.objExists.assert_called_with(blendshape_name)
        mock_cmds.listAttr.assert_called_with(f"{blendshape_name}.w", multi=True)

    @mock.patch("cgame_avatar_factory.api.edit_mode_utils.cmds")
    def test_delete_blendshape_target(self, mock_cmds):
        """Test delete_blendshape_target function."""
        # 设置模拟返回值
        mock_cmds.objExists.return_value = True
        mock_cmds.listAttr.return_value = ["target1", "target2"]

        # 调用被测试的函数
        result = edit_mode_utils.delete_blendshape_target("target1")

        # 验证结果
        self.assertTrue(result)

        # 验证函数调用
        blendshape_name = f"{const.GOZ_MESH_NAME}_{const.GOZ_BLEND_SHAPE_SUFFIX}"
        mock_cmds.objExists.assert_called_with(blendshape_name)
        mock_cmds.listAttr.assert_called_with(f"{blendshape_name}.w", multi=True)
        mock_cmds.removeMultiInstance.assert_called_with(
            f"{blendshape_name}.target1",
            b=True,
        )
        mock_cmds.aliasAttr.assert_called_with(
            f"{blendshape_name}.target1",
            remove=True,
        )

    @mock.patch("cgame_avatar_factory.api.edit_mode_utils.cmds")
    def test_set_blendshape_weight(self, mock_cmds):
        """Test set_blendshape_weight function."""
        # 设置模拟返回值
        mock_cmds.objExists.return_value = True
        mock_cmds.listAttr.return_value = ["target1", "target2"]

        # 调用被测试的函数
        result = edit_mode_utils.set_blendshape_weight("target1", 0.5)

        # 验证结果
        self.assertTrue(result)

        # 验证函数调用
        blendshape_name = f"{const.GOZ_MESH_NAME}_{const.GOZ_BLEND_SHAPE_SUFFIX}"
        mock_cmds.objExists.assert_called_with(blendshape_name)
        mock_cmds.listAttr.assert_called_with(f"{blendshape_name}.w", multi=True)
        mock_cmds.setAttr.assert_called_with(f"{blendshape_name}.target1", 0.5)


if __name__ == "__main__":
    unittest.main()
