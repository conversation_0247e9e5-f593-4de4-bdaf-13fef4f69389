#!/usr/bin/env python
"""Test script to verify that tests use only mock data.

This script runs a subset of tests to ensure they don't depend on real data.
"""

# Import built-in modules
import os
import sys
import unittest

# Add the project root to Python path for testing
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def test_mock_only():
    """Test that our tests use only mock data."""
    print("Testing Mock-Only Data Manager Tests...")
    print("=" * 50)

    try:
        # Import the test module
        # Import local modules
        from cgame_avatar_factory.hair_studio.tests.test_data_manager_core import TestAssetConfigIntegration
        from cgame_avatar_factory.hair_studio.tests.test_data_manager_core import TestDataManagerCore
        from cgame_avatar_factory.hair_studio.tests.test_data_manager_core import TestOptimizedDataManagerWorkflow
        from cgame_avatar_factory.hair_studio.tests.test_data_manager_core import TestSubTypeLogicCore

        # Create test suite with specific tests
        suite = unittest.TestSuite()

        # Add specific tests that should work with mock data only
        suite.addTest(TestDataManagerCore("test_data_manager_initialization"))
        suite.addTest(TestDataManagerCore("test_get_assets_basic"))
        suite.addTest(TestDataManagerCore("test_asset_structure"))
        suite.addTest(TestDataManagerCore("test_get_components"))
        suite.addTest(TestDataManagerCore("test_reload_assets"))

        suite.addTest(TestAssetConfigIntegration("test_get_asset_paths"))
        suite.addTest(TestAssetConfigIntegration("test_asset_path_validation"))
        suite.addTest(TestAssetConfigIntegration("test_get_asset_path_list"))
        suite.addTest(TestAssetConfigIntegration("test_get_asset_path_single"))

        suite.addTest(TestSubTypeLogicCore("test_sub_type_determination"))
        suite.addTest(TestSubTypeLogicCore("test_sub_type_case_insensitive"))
        suite.addTest(TestSubTypeLogicCore("test_sub_type_chinese_keywords"))
        suite.addTest(TestSubTypeLogicCore("test_optimized_vs_legacy_approach"))

        suite.addTest(TestOptimizedDataManagerWorkflow("test_optimized_asset_loading_workflow"))
        suite.addTest(TestOptimizedDataManagerWorkflow("test_performance_comparison_mock"))

        # Run tests
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)

        # Print results
        print("\n" + "=" * 50)
        if result.wasSuccessful():
            print(f"✅ All {result.testsRun} mock-only tests passed!")
            print("🎉 Tests are properly using mock data only.")
            return True
        else:
            print(f"❌ {len(result.failures)} failures, {len(result.errors)} errors")
            print("⚠️  Some tests may still be using real data.")

            # Print failure details
            if result.failures:
                print("\nFailures:")
                for test, traceback in result.failures:
                    print(f"- {test}: {traceback}")

            if result.errors:
                print("\nErrors:")
                for test, traceback in result.errors:
                    print(f"- {test}: {traceback}")

            return False

    except Exception as e:
        print(f"❌ Error running mock-only tests: {e}")
        # Import built-in modules
        import traceback

        traceback.print_exc()
        return False


def test_no_real_imports():
    """Test that we don't import real data manager functions."""
    print("\nTesting Import Safety...")
    print("=" * 30)

    try:
        # Try to import the test module and check for problematic imports
        # Import local modules
        import cgame_avatar_factory.hair_studio.tests.test_data_manager_core as test_module

        # Check if the module has any direct imports of real data manager functions
        problematic_imports = []

        # These are functions we should NOT import directly in tests
        dangerous_functions = [
            "_determine_sub_asset_type_from_path",
            "HairDataManager",
            "DataManager",
            "_scan_assets_in_directory",
            "get_asset_paths",
            "validate_asset_paths",
            "get_asset_path_list",
            "get_asset_path",
        ]

        module_dict = dir(test_module)
        for func_name in dangerous_functions:
            if func_name in module_dict:
                problematic_imports.append(func_name)

        if problematic_imports:
            print(f"⚠️  Found potentially problematic imports: {problematic_imports}")
            print("These should be mocked instead of imported directly.")
            return False
        else:
            print("✅ No problematic direct imports found.")
            return True

    except Exception as e:
        print(f"❌ Error checking imports: {e}")
        return False


def main():
    """Main test runner."""
    print("MOCK-ONLY TEST VERIFICATION")
    print("=" * 60)

    # Test 1: Run mock-only tests
    mock_test_success = test_mock_only()

    # Test 2: Check for problematic imports
    import_safety_success = test_no_real_imports()

    # Overall result
    print("\n" + "=" * 60)
    print("OVERALL RESULT")
    print("=" * 60)

    if mock_test_success and import_safety_success:
        print("🎉 ALL CHECKS PASSED!")
        print("✅ Tests are properly using mock data only.")
        print("✅ No problematic real data imports found.")
        print("\n💡 The tests should now work with pytest without real data dependencies.")
        return True
    else:
        print("❌ SOME CHECKS FAILED!")
        if not mock_test_success:
            print("❌ Mock-only tests failed.")
        if not import_safety_success:
            print("❌ Found problematic imports.")
        print("\n⚠️  Please fix the issues before running with pytest.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
