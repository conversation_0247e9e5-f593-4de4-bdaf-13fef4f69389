#!/usr/bin/env python

"""
Test ResponsiveAssetLibrary compatibility with BaseHairTab interface
"""

# Import built-in modules
import os
import sys
import traceback

# Add project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Set environment variables for test mode
os.environ["HAIR_STUDIO_TEST_MODE"] = "ui_test"
os.environ["HAIR_STUDIO_UI_TEST_MODE"] = "true"
os.environ["HAIR_STUDIO_MAYA_MOCK_MODE"] = "true"


def main():
    print("=" * 80)
    print("ResponsiveAssetLibrary BaseHairTab Compatibility Test")
    print("=" * 80)

    try:
        print("1. Importing modules...")
        # Import third-party modules
        from qtpy.QtWidgets import QApplication

        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.responsive_asset_library import (
            ResponsiveAssetLibrary,
        )

        print("   [OK] ResponsiveAssetLibrary import successful")

        print("\n2. Creating QApplication...")
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        print("   [OK] QApplication ready")

        print("\n3. Testing BaseHairTab-style initialization...")

        # Mock hair manager for testing
        class MockHairManager:
            def get_assets(self, asset_type):
                return [
                    {"id": f"{asset_type}_1", "name": f"{asset_type.title()} Asset 1", "asset_type": asset_type},
                    {"id": f"{asset_type}_2", "name": f"{asset_type.title()} Asset 2", "asset_type": asset_type},
                    {"id": f"{asset_type}_3", "name": f"{asset_type.title()} Asset 3", "asset_type": asset_type},
                ]

        mock_manager = MockHairManager()

        # Test initialization exactly like BaseHairTab does
        library = ResponsiveAssetLibrary(
            hair_type="card",
            hair_manager=mock_manager,
            parent=None,
        )
        print("   [OK] BaseHairTab-style initialization successful")

        print("\n4. Testing compatibility attributes...")

        # Test attributes that BaseHairTab expects
        assert hasattr(library, "hair_type"), "Should have hair_type attribute"
        assert library.hair_type == "card", f"Expected hair_type 'card', got '{library.hair_type}'"
        print("   [OK] hair_type attribute")

        assert hasattr(library, "manager"), "Should have manager attribute"
        assert library.manager is mock_manager, "Manager should be the same instance"
        print("   [OK] manager attribute")

        assert hasattr(library, "assets"), "Should have assets property"
        print("   [OK] assets property")

        print("\n5. Testing BaseHairTab interface methods...")

        # Test update_assets method (snake_case version)
        test_assets = [
            {"id": "test1", "name": "Test Asset 1", "asset_type": "card"},
            {"id": "test2", "name": "Test Asset 2", "asset_type": "card"},
        ]

        library.update_assets(test_assets)
        assert len(library.assets) == 2, f"Expected 2 assets, got {len(library.assets)}"
        print("   [OK] update_assets method")

        # Test updateAssets method (camelCase version)
        library.updateAssets(test_assets)
        assert len(library.assets) == 2, f"Expected 2 assets, got {len(library.assets)}"
        print("   [OK] updateAssets method")

        # Test refresh method
        library.refresh()
        # Should get assets from mock manager
        assert len(library.assets) == 3, f"Expected 3 assets after refresh, got {len(library.assets)}"
        print("   [OK] refresh method")

        print("\n6. Testing signal compatibility...")

        # Test that required signals exist
        required_signals = ["assetSelected", "assetDragStarted", "assetDoubleClicked"]
        for signal_name in required_signals:
            assert hasattr(library, signal_name), f"Should have {signal_name} signal"
            signal = getattr(library, signal_name)
            assert hasattr(signal, "connect"), f"{signal_name} should be a signal"
            assert hasattr(signal, "emit"), f"{signal_name} should be a signal"
        print("   [OK] All required signals exist")

        # Test signal connections
        signals_received = []

        def on_asset_selected(asset):
            signals_received.append(("selected", asset))

        def on_asset_drag_started(asset):
            signals_received.append(("drag_started", asset))

        def on_asset_double_clicked(asset):
            signals_received.append(("double_clicked", asset))

        library.assetSelected.connect(on_asset_selected)
        library.assetDragStarted.connect(on_asset_drag_started)
        library.assetDoubleClicked.connect(on_asset_double_clicked)
        print("   [OK] Signal connections established")

        print("\n7. Testing asset property behavior...")

        # Test assets property getter
        current_assets = library.assets
        assert isinstance(current_assets, list), "assets should return a list"
        print("   [OK] assets property getter")

        # Test assets property setter
        new_assets = [{"id": "new1", "name": "New Asset", "asset_type": "card"}]
        library.assets = new_assets
        assert len(library.assets) == 1, "assets setter should update the assets"
        assert library.assets[0]["id"] == "new1", "assets setter should set correct data"
        print("   [OK] assets property setter")

        print("\n8. Testing method compatibility with AssetLibraryOptimized...")

        # Test methods that exist in AssetLibraryOptimized
        compatibility_methods = [
            "refresh",
            "update_assets",
            "updateAssets",
            "getSelectedAsset",
            "selectAssetById",
            "clearSelection",
            "setTitle",
            "getScale",
            "setScale",
        ]

        for method_name in compatibility_methods:
            assert hasattr(library, method_name), f"Should have {method_name} method"
            method = getattr(library, method_name)
            assert callable(method), f"{method_name} should be callable"
        print("   [OK] All compatibility methods exist")

        print("\n9. Testing integration scenario...")

        # Simulate what BaseHairTab does
        # 1. Create library
        # 2. Call refresh_asset_library which calls update_assets
        # 3. Access assets property

        # Reset library
        library = ResponsiveAssetLibrary("xgen", mock_manager, None, None)

        # Simulate BaseHairTab.refresh_asset_library()
        assets_from_manager = mock_manager.get_assets("xgen")
        library.update_assets(assets_from_manager)

        # Check that assets are accessible
        assert len(library.assets) == 3, "Should have 3 xgen assets"
        assert all(asset["asset_type"] == "xgen" for asset in library.assets), "All assets should be xgen type"
        print("   [OK] BaseHairTab integration scenario")

        print("\n10. Testing error handling...")

        # Test with None hair_manager
        library_no_manager = ResponsiveAssetLibrary("curve", None, None, None)
        library_no_manager.refresh()  # Should not crash
        print("   [OK] Handles None hair_manager gracefully")

        # Test with empty assets
        library.updateAssets([])
        assert len(library.assets) == 0, "Should handle empty assets list"
        print("   [OK] Handles empty assets list")

        # Test with None assets
        library.updateAssets(None)
        assert len(library.assets) == 0, "Should handle None assets"
        print("   [OK] Handles None assets")

        print("\n" + "=" * 80)
        print("[SUCCESS] All ResponsiveAssetLibrary compatibility tests passed!")
        print("✅ BaseHairTab-style initialization")
        print("✅ Required attributes (hair_type, manager, assets)")
        print("✅ Interface methods (update_assets, refresh, etc.)")
        print("✅ Signal compatibility")
        print("✅ Property behavior")
        print("✅ Method compatibility with AssetLibraryOptimized")
        print("✅ Integration scenario simulation")
        print("✅ Error handling")
        print("\n🎉 ResponsiveAssetLibrary is fully compatible with BaseHairTab!")
        print("=" * 80)

        return 0

    except Exception as e:
        print(f"\n[ERROR] Compatibility test failed: {e}")
        print("\nFull traceback:")
        traceback.print_exc()
        print("\n" + "=" * 80)
        print("[FAILED] ResponsiveAssetLibrary compatibility tests failed")
        print("=" * 80)
        return 1


if __name__ == "__main__":
    sys.exit(main())
