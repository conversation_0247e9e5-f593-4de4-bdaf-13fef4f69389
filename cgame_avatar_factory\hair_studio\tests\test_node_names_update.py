#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test script to verify the node_names update functionality.

This script tests the new node_names dictionary format for hair operations.
"""

# Import built-in modules
import os
import sys
import unittest

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


class TestNodeNamesUpdate(unittest.TestCase):
    """Test the updated node_names functionality."""

    def setUp(self):
        """Set up test fixtures."""
        # Import local modules
        import cgame_avatar_factory.hair_studio.data.models as models
        import cgame_avatar_factory.hair_studio.maya_api as maya_api
        import cgame_avatar_factory.hair_studio.maya_api.hair_operations as hair_ops

        self.hair_ops = hair_ops
        self.maya_api = maya_api
        self.models = models

    def test_create_hair_node_new_format(self):
        """Test create_hair_node returns new node_names format."""
        # Mock WrapHairMesh
        original_wrap = getattr(self.hair_ops, "WrapHairMesh", None)

        class MockWrapHairMesh:
            def __init__(self, *args, **kwargs):
                self.hair_target = "test_hair_asset_node"
                self.hair_head_mesh = "test_ref_head_node"

        self.hair_ops.WrapHairMesh = MockWrapHairMesh

        try:
            # Test data
            asset_data = {
                "name": "Test Hair Asset",
                "metadata": {
                    "file_path": "/mock/hair.fbx",
                    "reference": "/mock/head.obj",
                },
            }

            # Call function
            result = self.hair_ops.create_hair_node(asset_data)

            # Verify new format
            self.assertIsInstance(result, dict)
            self.assertTrue(result.get("success"))
            self.assertIn("node_names", result)

            node_names = result["node_names"]
            self.assertIsInstance(node_names, dict)
            self.assertIn("hair_asset", node_names)
            self.assertIn("ref_head", node_names)
            self.assertEqual(node_names["hair_asset"], "test_hair_asset_node")
            self.assertEqual(node_names["ref_head"], "test_ref_head_node")

        finally:
            if original_wrap:
                self.hair_ops.WrapHairMesh = original_wrap

    def test_delete_node_dict_format(self):
        """Test delete_node handles dictionary format."""
        # Test with dictionary input
        node_names = {
            "hair_asset": "test_hair_node",
            "ref_head": "test_head_node",
        }

        result = self.hair_ops.delete_node(node_names)
        self.assertIsInstance(result, dict)
        self.assertIn("success", result)

    def test_delete_node_invalid_input(self):
        """Test delete_node rejects non-dict input."""
        # Test with string input (should fail)
        node_name = "test_single_node"

        result = self.hair_ops.delete_node(node_name)
        self.assertIsInstance(result, dict)
        self.assertFalse(result.get("success"))
        self.assertIn("error", result)

    def test_set_node_visibility_dict_format(self):
        """Test set_node_visibility handles dictionary format."""
        # Test with dictionary input
        node_names = {
            "hair_asset": "test_hair_node",
            "ref_head": "test_head_node",
        }

        result = self.hair_ops.set_node_visibility(node_names, True)
        self.assertIsInstance(result, dict)
        self.assertIn("success", result)

    def test_set_node_visibility_invalid_input(self):
        """Test set_node_visibility rejects non-dict input."""
        # Test with string input (should fail)
        node_name = "test_single_node"

        result = self.hair_ops.set_node_visibility(node_name, False)
        self.assertIsInstance(result, dict)
        self.assertFalse(result.get("success"))
        self.assertIn("error", result)

    def test_maya_api_delete_hair_component_new_format(self):
        """Test MayaAPI delete_hair_component with new format."""
        api = self.maya_api.MayaAPI()

        # Test with new format
        component_data = {
            "node_names": {
                "hair_asset": "test_hair_node",
                "ref_head": "test_head_node",
            },
        }

        result = api.delete_hair_component(component_data)
        self.assertIsInstance(result, dict)
        self.assertIn("success", result)

    def test_maya_api_delete_hair_component_missing_node_names(self):
        """Test MayaAPI delete_hair_component with missing node_names."""
        api = self.maya_api.MayaAPI()

        # Test with missing node_names
        component_data = {}

        result = api.delete_hair_component(component_data)
        self.assertIsInstance(result, dict)
        self.assertFalse(result.get("success"))
        self.assertIn("error", result)

    def test_maya_api_set_component_visibility_new_format(self):
        """Test MayaAPI set_component_visibility with new format."""
        api = self.maya_api.MayaAPI()

        # Test with new format
        component_data = {
            "node_names": {
                "hair_asset": "test_hair_node",
                "ref_head": "test_head_node",
            },
        }

        result = api.set_component_visibility(component_data, True)
        self.assertIsInstance(result, dict)
        self.assertIn("success", result)

    def test_hair_component_to_dict_new_format(self):
        """Test HairComponent.to_dict with new format."""
        # Test with new format (dict node_names)
        component = self.models.HairComponent(
            id="test_id",
            name="Test Component",
            node_names={
                "hair_asset": "test_hair_node",
                "ref_head": "test_head_node",
            },
        )

        result = component.to_dict()

        # Check new format fields
        self.assertIn("node_names", result)

        self.assertEqual(result["node_names"]["hair_asset"], "test_hair_node")
        self.assertEqual(result["node_names"]["ref_head"], "test_head_node")

    def test_hair_component_to_dict_invalid_node_names(self):
        """Test HairComponent.to_dict with invalid node_names."""
        # Test with invalid format (string node_names should raise error)
        component = self.models.HairComponent(
            id="test_id",
            name="Test Component",
            node_names="test_single_node",
        )

        # Should raise ValueError when calling to_dict
        with self.assertRaises(ValueError):
            component.to_dict()

    def test_hair_component_from_dict_new_format(self):
        """Test HairComponent.from_dict with new format."""
        data = {
            "id": "test_id",
            "name": "Test Component",
            "node_names": {
                "hair_asset": "test_hair_node",
                "ref_head": "test_head_node",
            },
        }

        component = self.models.HairComponent.from_dict(data)

        self.assertEqual(component.id, "test_id")
        self.assertEqual(component.name, "Test Component")
        self.assertIsInstance(component.node_names, dict)
        self.assertEqual(component.node_names["hair_asset"], "test_hair_node")
        self.assertEqual(component.node_names["ref_head"], "test_head_node")

    def test_hair_component_from_dict_invalid_data(self):
        """Test HairComponent.from_dict with invalid data."""
        # Test with missing node_names
        data = {
            "id": "test_id",
            "name": "Test Component",
        }

        # Should raise ValueError
        with self.assertRaises(ValueError):
            self.models.HairComponent.from_dict(data)


def main():
    """Run the tests."""
    print("=" * 60)
    print("Testing Node Names Update Functionality")
    print("=" * 60)

    # Run tests
    unittest.main(verbosity=2)


if __name__ == "__main__":
    main()
