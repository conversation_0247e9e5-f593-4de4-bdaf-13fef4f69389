#! /usr/bin/env python
"""Rez build_face file, must cd to this directory and run "rez build_face"."""

# Import future modules
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

# Import built-in modules
import os
from shutil import copytree

# Import third-party modules
from rez_builder import PipFromSrcBuilder


class CustomBuilder(PipFromSrcBuilder):
    """Pip Installer."""

    def copy_menu_config(self):
        """Copying config of dcc menu api into current package."""
        folders = ["config"]
        for folder in folders:
            config_file = os.path.join(self.source_path, folder)
            copytree(config_file, os.path.join(self.staging, folder))

    def custom_build(self):
        """Custom Build."""
        super(CustomBuilder, self).custom_build()
        self.copy_menu_config()


if __name__ == "__main__":
    BUILDER = CustomBuilder({})
    BUILDER.build()
