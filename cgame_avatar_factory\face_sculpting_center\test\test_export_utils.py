# -*- coding: utf-8 -*-
"""Test export_utils module."""

# Import future modules
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

# Import built-in modules
import sys
import unittest
from unittest import mock

# 模拟所有需要的模块
sys.modules["maya"] = mock.MagicMock()
sys.modules["maya.cmds"] = mock.MagicMock()
sys.modules["dna"] = mock.MagicMock()
sys.modules["dna_viewer"] = mock.MagicMock()
sys.modules["dna_viewer.DNA"] = mock.MagicMock()
sys.modules["lightbox_config"] = mock.MagicMock()

# 模拟 Configuration 类
mock_config = mock.MagicMock()
sys.modules["lightbox_config"].Configuration = mock_config

# 模拟 cgame_avatar_factory.config 模块
mock_config_module = mock.MagicMock()
mock_config_module.get_config.return_value = {}
sys.modules["cgame_avatar_factory.config"] = mock_config_module

# 模拟 constants 模块
mock_constants = mock.MagicMock()
mock_constants.BASE_HEAD_MESH_NAME = "head_lod0_mesh"
mock_constants.HEAD_DIFFUSE_TEX_NAME = "head_diffuse_texture"
sys.modules["cgame_avatar_factory.constants"] = mock_constants

# Mock TemplateDna class
class MockTemplateDna:
    @staticmethod
    def get_mesh_name():
        return []

    @staticmethod
    def get_reader():
        mock_reader = mock.MagicMock()
        mock_reader.getMeshCount.return_value = 0
        return mock_reader

    @staticmethod
    def write_mesh(path, mesh_list):
        return True


# Create a mock for dna_utils module
mock_dna_utils = mock.MagicMock()
mock_dna_utils.TemplateDna = MockTemplateDna
mock_dna_utils.get_mesh_name = MockTemplateDna.get_mesh_name
mock_dna_utils.write_mesh = MockTemplateDna.write_mesh
sys.modules["cgame_avatar_factory.api.dna_utils"] = mock_dna_utils

# Import local modules
# Now import export_utils
from cgame_avatar_factory.face_sculpting_center.utils import export_utils


class TestExportUtils(unittest.TestCase):
    """Test cases for export_utils module."""

    @mock.patch("cgame_avatar_factory.api.export_utils.cmds")
    def test_duplicate_and_export_head_mesh_success(self, mock_cmds):
        """Test successful duplication and export of head mesh group."""
        # Setup mock return values
        mock_cmds.objExists.return_value = True
        mock_cmds.duplicate.return_value = ["head_grp1"]
        mock_cmds.rename.return_value = "head_grp_output"
        mock_cmds.fileDialog2.return_value = ["/path/to/export.fbx"]

        # Call the function
        export_utils.duplicate_and_export_head_mesh()

        # Verify the function calls
        mock_cmds.duplicate.assert_called_once_with(returnRootsOnly=True)
        mock_cmds.rename.assert_called_once_with(["head_grp1"], "head_grp_output")
        mock_cmds.delete.assert_called()  # Called for construction history and cleanup

        # Verify file dialog and export
        mock_cmds.fileDialog2.assert_called_once_with(
            fileFilter="FBX Files (*.fbx)",
            dialogStyle=2,
            fileMode=0,
            caption="Export Head FBX",
        )
        mock_cmds.file.assert_called_once_with(
            "/path/to/export.fbx",
            force=True,
            exportSelected=True,
            type="FBX export",
        )
        # Verify cleanup
        self.assertEqual(mock_cmds.delete.call_count, 2)  # Once for construction history, once for cleanup

    @mock.patch("cgame_avatar_factory.api.export_utils.cmds")
    def test_duplicate_and_export_head_mesh_cancel(self, mock_cmds):
        """Test cancellation of export dialog."""
        # Setup mock return values
        mock_cmds.objExists.return_value = True
        mock_cmds.duplicate.return_value = ["head_grp1"]
        mock_cmds.rename.return_value = "head_grp_output"
        mock_cmds.fileDialog2.return_value = None  # User cancels dialog

        # Call the function
        export_utils.duplicate_and_export_head_mesh()

        # Verify the function calls
        mock_cmds.file.assert_not_called()  # Should not call export
        mock_cmds.delete.assert_called_with("head_grp_output")  # Should still clean up

    @mock.patch("cgame_avatar_factory.api.export_utils.cmds")
    def test_duplicate_and_export_head_mesh_add_extension(self, mock_cmds):
        """Test adding .fbx extension if missing."""
        # Setup mock return values
        mock_cmds.objExists.return_value = True
        mock_cmds.duplicate.return_value = ["head_grp1"]
        mock_cmds.rename.return_value = "head_grp_output"
        mock_cmds.fileDialog2.return_value = ["/path/to/export"]  # No extension

        # Call the function
        export_utils.duplicate_and_export_head_mesh()

        # Verify the function calls
        mock_cmds.file.assert_called_once_with(
            "/path/to/export.fbx",  # Should add .fbx extension
            force=True,
            exportSelected=True,
            type="FBX export",
        )

    @mock.patch("cgame_avatar_factory.api.export_utils.cmds")
    def test_duplicate_and_export_head_mesh_group_not_found(self, mock_cmds):
        """Test error when head_grp is not found."""
        # Setup mock to return False for head_grp
        mock_cmds.objExists.return_value = False

        # Verify the function raises an exception
        with self.assertRaises(RuntimeError) as context:
            export_utils.duplicate_and_export_head_mesh()

        self.assertIn("not found", str(context.exception))
        mock_cmds.duplicate.assert_not_called()  # Should not call duplicate


if __name__ == "__main__":
    unittest.main()
