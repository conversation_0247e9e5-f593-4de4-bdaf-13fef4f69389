#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
表情编辑器模块

提供表情编辑功能的界面组件，包含上下两个画布
上部画布占80%，用于表情编辑
下部画布占20%，用于控制面板
"""

# Import built-in modules
import logging

# Import third-party modules
import dayu_widgets
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.common import constants as const
from cgame_avatar_factory.common.ui.layout import FramelessVLayout


class FaceRigEditorWidget(QtWidgets.QWidget):
    # UI尺寸常量
    BUTTON_SIZE = (120, 40)
    CONTENT_MARGIN = (8, 8, 8, 8)
    BUTTON_MARGIN = (20, 10, 20, 10)
    SPLITTER_HANDLE_WIDTH = 1
    PLACEHOLDER_MIN_HEIGHT = 200

    # 样式属性常量
    BORDER_WIDTH = 3
    BORDER_RADIUS = 8
    PADDING = 3

    # 注意：颜色常量已移至constants.py

    sig_face_modified = QtCore.Signal(dict)

    def __init__(self, parent=None):
        super(FaceRigEditorWidget, self).__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.setup_ui()

    def setup_ui(self):
        """初始化UI组件"""
        layout = FramelessVLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(layout)
        splitter = QtWidgets.QSplitter(QtCore.Qt.Vertical)
        splitter.setHandleWidth(self.SPLITTER_HANDLE_WIDTH)
        splitter.setChildrenCollapsible(False)
        layout.addWidget(splitter)

        self.top_canvas = QtWidgets.QFrame()
        self.top_canvas.setObjectName("faceEditCanvas")
        top_layout = QtWidgets.QVBoxLayout()
        top_layout.setContentsMargins(*self.CONTENT_MARGIN)
        self.top_canvas.setLayout(top_layout)

        edit_placeholder = dayu_widgets.MLabel("表情编辑区域")
        edit_placeholder.setAlignment(QtCore.Qt.AlignCenter)
        edit_placeholder.setStyleSheet(
            f"background-color: {const.BUTTON_BORDER_DARK}; border: 1px solid {const.BUTTON_BORDER};",
        )
        edit_placeholder.setMinimumHeight(self.PLACEHOLDER_MIN_HEIGHT)
        top_layout.addWidget(edit_placeholder, 1)

        self.bottom_canvas = QtWidgets.QFrame()
        self.bottom_canvas.setObjectName("controlPanel")
        bottom_layout = QtWidgets.QVBoxLayout()
        bottom_layout.setContentsMargins(*self.CONTENT_MARGIN)
        self.bottom_canvas.setLayout(bottom_layout)

        button_layout = QtWidgets.QHBoxLayout()
        button_layout.setContentsMargins(*self.BUTTON_MARGIN)
        button_layout.setAlignment(QtCore.Qt.AlignCenter)
        self.wiring_button = QtWidgets.QPushButton("布线整理")
        self.wiring_button.setFixedSize(*self.BUTTON_SIZE)
        self.wiring_button.setCursor(QtCore.Qt.PointingHandCursor)
        self.wiring_button.setObjectName("wiringButton")

        button_style = f"""
            QPushButton {{
                border: {self.BORDER_WIDTH}px solid {const.BUTTON_BORDER};
                border-radius: {self.BORDER_RADIUS}px;
                background-color: {const.BUTTON_BG};
                color: {const.DAYU_PRIMARY_TEXT_COLOR};
                padding: {self.PADDING}px;
            }}
            QPushButton:hover {{
                background-color: {const.BUTTON_HOVER_BG};
            }}
            QPushButton:pressed {{
                background-color: {const.BUTTON_CHECKED_BG};
                color: {const.BUTTON_CHECKED_TEXT};
                font-weight: bold;
            }}
        """

        self.wiring_button.setStyleSheet(button_style)
        self.wiring_button.clicked.connect(self._on_wiring_button_clicked)
        button_layout.addWidget(self.wiring_button)
        bottom_layout.addLayout(button_layout)

        splitter.addWidget(self.top_canvas)
        splitter.addWidget(self.bottom_canvas)

        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 0)

    def _on_wiring_button_clicked(self):
        self.logger.info("点击了布线整理按钮")
