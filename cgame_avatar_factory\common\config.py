"""Get config information from the global config data."""

# Import third-party modules
from lightbox_config import Configuration


def get_config(key):
    """Read config file and return value by key.

    Args:
        key (str): Key to get value from the config file.

    Returns:
        str or dict: Value from key.

    """
    # Import inside function to avoid circular import
    # Import local modules
    from cgame_avatar_factory.common.constants import PACKAGE_NAME

    config = Configuration()
    return config.query(PACKAGE_NAME, key)
