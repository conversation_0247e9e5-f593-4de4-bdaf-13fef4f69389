# Import third-party modules
import maya.cmds as cmds
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
import cgame_avatar_factory.common.constants as const
from cgame_avatar_factory.face_sculpting_center import constants as face_const


class ExportDialog(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("导出模型")
        self.setFixedSize(500, 600)
        self.setModal(True)

        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        self.model_list = QtWidgets.QTreeWidget()
        self.model_list.setHeaderLabel("模型结构")
        self.model_list.itemClicked.connect(self.on_model_selected)
        self.model_list.setStyleSheet(
            """
            QTreeWidget {
                border: 1px solid #666666;
                outline: none;
            }
            QTreeWidget::item:selected {
                background-color: #5285a6;
                outline: none;
            }
        """
        )
        self.model_list.addTopLevelItem(QtWidgets.QTreeWidgetItem(["正在加载模型..."]))
        layout.addWidget(self.model_list)

        layout.addSpacing(20)

        options_layout = QtWidgets.QHBoxLayout()

        self.center_align_cb = QtWidgets.QCheckBox()
        self.center_align_cb.setChecked(True)
        center_label = QtWidgets.QLabel("模型对称")
        options_layout.addWidget(self.center_align_cb)
        options_layout.addWidget(center_label)

        options_layout.addStretch(1)
        layout.addLayout(options_layout)

        btn_layout = QtWidgets.QHBoxLayout()
        btn_layout.addStretch()

        cancel_btn = QtWidgets.QPushButton("取消")
        cancel_btn.setFixedSize(100, 40)
        cancel_btn.clicked.connect(self.close)

        export_btn = QtWidgets.QPushButton("导出")
        export_btn.setFixedSize(100, 40)
        export_btn.clicked.connect(self.export_models)

        btn_layout.addWidget(cancel_btn)
        btn_layout.addSpacing(10)
        btn_layout.addWidget(export_btn)
        layout.addLayout(btn_layout)

        QtCore.QTimer.singleShot(0, self.load_models)

    def load_models(self):
        self.model_list.clear()

        if not cmds.objExists(face_const.TARGET_TOPO_GRP_NAME):
            item = QtWidgets.QTreeWidgetItem([f"未找到组: {face_const.TARGET_TOPO_GRP_NAME}"])
            self.model_list.addTopLevelItem(item)
            return

        root_item = QtWidgets.QTreeWidgetItem([face_const.TARGET_TOPO_GRP_NAME])
        self.model_list.addTopLevelItem(root_item)

        self._build_tree_structure(face_const.TARGET_TOPO_GRP_NAME, root_item)

        self.model_list.expandAll()

    def _build_tree_structure(self, parent_obj, parent_item):
        children = cmds.listRelatives(parent_obj, children=True, type="transform") or []

        for child in children:
            child_item = QtWidgets.QTreeWidgetItem([child])
            parent_item.addChild(child_item)

            if cmds.listRelatives(child, shapes=True, type="mesh"):
                child_item.setData(0, QtCore.Qt.UserRole, "mesh")
            else:
                child_item.setData(0, QtCore.Qt.UserRole, "group")

            self._build_tree_structure(child, child_item)

    def on_model_selected(self, item, column):
        model_name = item.text(0)
        if cmds.objExists(model_name):
            cmds.select(model_name, replace=True)

    def export_models(self):
        models = self._collect_mesh_models()
        if not models:
            QtWidgets.QMessageBox.warning(self, "导出失败", "没有找到可导出的模型")
            return

        workspace_path = cmds.workspace(query=True, rootDirectory=True)
        default_path = f"{workspace_path}/exported_models.fbx"

        file_path = QtWidgets.QFileDialog.getSaveFileName(
            self,
            "选择FBX导出路径",
            default_path,
            "FBX Files (*.fbx)",
        )[0]

        if not file_path:
            return

        duplicate_grp = cmds.duplicate(face_const.TARGET_TOPO_GRP_NAME, renameChildren=True)[0]

        head_mesh_base_name = f"{const.BASE_HEAD_MESH_NAME}_{face_const.AUTO_TOPO_SUFFIX}"
        eye_lashes_mesh_base_name = f"{face_const.EYE_LASHES_MESH_NAME}_{face_const.AUTO_TOPO_SUFFIX}"
        eye_left_mesh_base_name = f"{face_const.EYE_LEFT_MESH_NAME}_{face_const.AUTO_TOPO_SUFFIX}"
        eye_right_mesh_base_name = f"{face_const.EYE_RIGHT_MESH_NAME}_{face_const.AUTO_TOPO_SUFFIX}"
        duplicated_meshes = cmds.listRelatives(duplicate_grp, allDescendents=True, type="mesh") or []

        duplicated_head_mesh = None
        for mesh in duplicated_meshes:
            transform = cmds.listRelatives(mesh, parent=True)[0]
            if head_mesh_base_name in transform:
                duplicated_head_mesh = transform
            if eye_left_mesh_base_name in transform:
                duplicated_eye_left_mesh = transform
            if eye_right_mesh_base_name in transform:
                duplicated_eye_right_mesh = transform
            if eye_lashes_mesh_base_name in transform:
                duplicated_eye_lashes_mesh = transform

        if duplicated_head_mesh:
            if self.center_align_cb.isChecked():
                # Import local modules
                from cgame_avatar_factory.common.utils import custom_topo_processing as topo_utils

                topo_utils.mesh_symmetry(duplicated_head_mesh)
                topo_utils.eye_symmetry(duplicated_eye_left_mesh, duplicated_eye_right_mesh)
                topo_utils.eye_lashes_symmetry(duplicated_eye_lashes_mesh)

        duplicate_meshes = []
        descendants = cmds.listRelatives(duplicate_grp, allDescendents=True, type="transform") or []
        for desc in descendants:
            if cmds.listRelatives(desc, shapes=True, type="mesh"):
                duplicate_meshes.append(desc)

        if duplicate_meshes:
            cmds.sets(duplicate_meshes, edit=True, forceElement="initialShadingGroup")

        cmds.select(duplicate_grp, replace=True)
        for cmd, value in [
            ("FBXExportSmoothingGroups", True),
            ("FBXExportHardEdges", False),
            ("FBXExportTangents", False),
            ("FBXExportSmoothMesh", True),
            ("FBXExportAnimationOnly", False),
            ("FBXExportInputConnections", False),
        ]:
            getattr(cmds, cmd)("-v", value)

        cmds.FBXExport("-f", file_path, "-s")
        cmds.delete(duplicate_grp)
        self.close()

    def _collect_mesh_models(self):
        models = []

        def collect_from_item(item):
            if item.data(0, QtCore.Qt.UserRole) == "mesh":
                models.append(item.text(0))

            for i in range(item.childCount()):
                collect_from_item(item.child(i))

        for i in range(self.model_list.topLevelItemCount()):
            collect_from_item(self.model_list.topLevelItem(i))

        return models
