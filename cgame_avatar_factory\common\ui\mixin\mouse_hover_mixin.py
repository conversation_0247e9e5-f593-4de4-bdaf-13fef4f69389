# Import local modules
from cgame_avatar_factory.common import constants as const


def hover_background_color_mixin(cls, hover_color=None, leave_color=None):
    """
    Amend background color when mouse enter target widget.
    When mouse enter target widget, apply hover_color.
    When mouse leave target widget, apply leave_color or recover original style.
    """
    hover_color = hover_color or const.DAYU_BG_IN_COLOR

    old_init = cls.__init__
    old_enter_event = cls.enterEvent
    old_leave_event = cls.leaveEvent
    old_set_style_sheet = cls.setStyleSheet

    def _new_init(self, *args, **kwargs):
        old_init(self, *args, **kwargs)
        self.original_styleSheet = self.styleSheet()
        self.old_set_style_sheet = old_set_style_sheet
        if leave_color:
            old_set_style_sheet(self, "#{} {{background-color: {};}}".format(self.objectName(), leave_color))

    def _new_enter_event(self, *args, **kwargs):
        old_enter_event(self, *args, **kwargs)
        old_set_style_sheet(self, "#{} {{background-color: {};}}".format(self.objectName(), hover_color))

    def _new_leave_event(self, *args, **kwargs):
        old_leave_event(self, *args, **kwargs)
        if leave_color:
            old_set_style_sheet(self, "#{} {{background-color: {};}}".format(self.objectName(), leave_color))
        else:
            old_set_style_sheet(self, self.original_styleSheet)

    def _new_setStyleSheet(self, style_sheet):
        self.original_styleSheet = style_sheet
        old_set_style_sheet(self, style_sheet)

    setattr(cls, "__init__", _new_init)
    setattr(cls, "enterEvent", _new_enter_event)
    setattr(cls, "leaveEvent", _new_leave_event)
    setattr(cls, "setStyleSheet", _new_setStyleSheet)
    return cls
