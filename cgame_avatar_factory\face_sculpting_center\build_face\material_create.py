# Import built-in modules
import os
from pathlib import Path

# Import third-party modules
import maya.cmds as cmds
import maya.mel as mel

# Import local modules
import cgame_avatar_factory.common.constants as const
import cgame_avatar_factory.face_sculpting_center.utils.dna_utils as dna


class MaterialCreate:
    """Creates and manages materials for DNA meshes

    Handles material setup, shader assignments, and texture mapping for DNA meshes.
    """

    def __init__(self, dna_path=None):
        """Initialize material creation

        Args:
            dna_path: Path to DNA file
        """
        self.dna_path = dna_path
        self.import_mat_file(os.path.join(const.CONFIG_PATH, "materials/mat.mb"))
        if self.dna_path:
            self.set_material_to_mesh()
            self.set_dx_fx_path(os.path.join(const.CONFIG_PATH, "materials/shaders"))
            self.set_tex_node_path(os.path.join(const.CONFIG_PATH, "materials/textures"))

            self.setup_viewport_display()
            dna_folder = os.path.dirname(self.dna_path)
            self.set_character_map(dna_folder)

            mel.eval("colorManagementPrefs -e -cmEnabled 0")

            if cmds.upAxis(q=True, axis=True).lower() == "y":
                cmds.rotate(-90, 0, 0, const.LIGHT_GRP, relative=True, worldSpace=True)

    def setup_viewport_display(self):
        """Configure Maya viewport display settings

        Sets up textures, lighting, shadows and camera settings.
        """
        mel.eval("modelEditor -e -displayTextures true modelPanel4")

        mel.eval("modelEditor -e -displayLights all modelPanel4")
        mel.eval("modelEditor -e -shadows true modelPanel4")

        mel.eval("modelEditor -e -displayAppearance smoothShaded modelPanel4")
        mel.eval("modelEditor -e -dtx true modelPanel4")
        mel.eval("setAttr perspShape.focalLength 65")

        cmds.refresh(force=True)

    def import_mat_file(self, path=None):
        """Import material file into Maya scene

        Args:
            path: Path to material file (.mb)
        """
        cmds.file(
            path,
            i=True,
            type="mayaBinary",
            ignoreVersion=True,
            ra=True,
            mergeNamespacesOnClash=True,
            namespace=":",
            options="v=0;",
            pr=True,
            importTimeRange="combine",
        )

    def endow_material(self, shader_name, mesh_name):
        """Assign shader to mesh

        Args:
            shader_name: Name of shader to assign
            mesh_name: Name of target mesh
        """
        shading_group = cmds.listConnections(shader_name, type="shadingEngine")
        if shading_group:
            cmds.sets(mesh_name, edit=True, forceElement=shading_group[0])

    def set_material_to_mesh(self):
        """Assign materials to DNA meshes based on mesh names"""
        for mesh_name in dna.TemplateDna.get_mesh_name():
            for name, shader in const.DX11_MATERIAL_NAME.items():
                if name in mesh_name and cmds.objExists(mesh_name):
                    self.endow_material(shader, mesh_name)

    def set_dx_fx_path(self, shader_directory: str = None):
        """Set shader paths for DirectX 11 shaders

        Args:
            shader_directory: Directory containing shader files
        """
        shader_directory_path = shader_directory or os.environ.get(const.PACKAGES_RESOURCE_NAME)

        for destination in const.DX11_MATERIAL_NAME.values():
            if cmds.objExists(destination):
                shader_type = cmds.nodeType(destination)
                if shader_type == "dx11Shader":
                    fx_file_path = os.path.join(shader_directory_path, f"{destination}.fx")
                    cmds.setAttr(f"{destination}.shader", fx_file_path, type="string")

    def find_files_by_extension(self, file_extensions: list, search_path: str) -> list:
        """Find files with specified extensions

        Args:
            file_extensions: List of file extensions to search for
            search_path: Directory to search in

        Returns:
            list: Found files matching extensions
        """
        search_path = Path(search_path)
        return [file for ext in file_extensions for file in search_path.glob(f"**/*{ext}")]

    def set_tex_node_path(self, path):
        """Sets the file texture path for all file nodes in the scene based on the specified directory.

        Args:
            path (str): The directory path where the texture files are located.
        """
        for file_path in self.find_files_by_extension([".tga", ".dds"], path):
            node_name = file_path.name.replace(file_path.suffix, "")
            if node_name in cmds.ls(type="file"):
                cmds.setAttr(f"{node_name}.fileTextureName", file_path.as_posix(), type="string")

    def create_light(self, intensity, rotate_X, rotate_Y, rotate_Z):
        """Create directional light

        Args:
            intensity: Light intensity
            rotate_X: X rotation angle
            rotate_Y: Y rotation angle
            rotate_Z: Z rotation angle
        """
        light = cmds.directionalLight(intensity=intensity)
        cmds.rotate(rotate_X, rotate_Y, rotate_Z, light)

    def set_character_map(self, path):
        """Check if there's a textures folder and set texture maps to dx11_shd_head material.

        Args:
            path (str): The DNA folder path to check
        """
        textures_path = os.path.join(path, "textures")
        has_textures_folder = os.path.exists(textures_path) and os.path.isdir(textures_path)
        if has_textures_folder:
            if not cmds.objExists("dx11_shd_head"):
                return

            for filename in os.listdir(textures_path):
                file_path = os.path.join(textures_path, filename)
                if os.path.isfile(file_path):
                    if "color_map" in filename:
                        if cmds.objExists("head_color_map"):
                            cmds.setAttr("head_color_map.fileTextureName", file_path, type="string")
                    elif "normal_map" in filename:
                        if cmds.objExists("head_normal_map"):
                            cmds.setAttr("head_normal_map.fileTextureName", file_path, type="string")
                    elif "roughness_map" in filename:
                        if cmds.objExists("head_roughness_map"):
                            cmds.setAttr("head_roughness_map.fileTextureName", file_path, type="string")
