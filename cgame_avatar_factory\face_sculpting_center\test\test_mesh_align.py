#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试 mesh_align 模块的功能

这个模块测试网格对齐相关的功能，包括：
- 刚体变换计算
- 变换矩阵应用
- 面部区域对齐
- 网格对齐差值计算
"""

# Import built-in modules
import sys
import unittest
from unittest import mock

# Import third-party modules
import numpy as np

# 模拟所有需要的模块，避免导入 constants.py


# 模拟 constants 模块
mock_constants = mock.MagicMock()
mock_constants.BASE_HEAD_MESH_NAME = "head_lod0_mesh"
mock_constants.VERTEX_USED_FOR_ALIGNMENT = "vertices_used_for_alignment.json"
mock_constants.CONFIG_PATH = "/mock/config/path"
mock_constants.WEIGHT_JSON_PATH = "vertex_weights.json"
mock_constants.RESOURCE_ROOT = "/mock/resource/root"
mock_constants.REGION_TO_MODEL_MAP = {
    "eyes_l_blendshape": [
        "eyeLeft_lod0_mesh",
        "eyeEdge_lod0_mesh",
        "eyeshell_lod0_mesh",
    ],
    "eyes_r_blendshape": [
        "eyeRight_lod0_mesh",
        "eyeEdge_lod0_mesh",
        "eyeshell_lod0_mesh",
    ],
    "mouth_blendshape": ["teeth_lod0_mesh"],
}
sys.modules["cgame_avatar_factory.constants"] = mock_constants

# 创建本地 const 对象以保持测试代码兼容性
const = mock_constants

# Import local modules
from cgame_avatar_factory.face_sculpting_center import mesh_align


class TestMeshAlign(unittest.TestCase):
    """测试 mesh_align 模块的各种功能"""

    def setUp(self):
        """设置测试数据"""
        # 创建测试用的点集
        self.src_points = np.array(
            [
                [0.0, 0.0, 0.0],
                [1.0, 0.0, 0.0],
                [0.0, 1.0, 0.0],
                [0.0, 0.0, 1.0],
            ],
        )

        self.tgt_points = np.array(
            [
                [1.0, 1.0, 1.0],
                [2.0, 1.0, 1.0],
                [1.0, 2.0, 1.0],
                [1.0, 1.0, 2.0],
            ],
        )

        # 创建测试用的网格数据
        self.test_mesh_data = {
            "test_mesh": [
                [0.0, 0.0, 0.0],
                [1.0, 0.0, 0.0],
                [0.0, 1.0, 0.0],
                [0.0, 0.0, 1.0],
            ],
        }

    def test_compute_rigid_transform(self):
        """测试刚体变换计算"""
        transform = mesh_align.compute_rigid_transform(self.src_points, self.tgt_points)

        # 验证返回的是4x4矩阵
        self.assertEqual(transform.shape, (4, 4))

        # 验证变换矩阵的最后一行是[0, 0, 0, 1]
        np.testing.assert_array_almost_equal(transform[3, :], [0, 0, 0, 1])

    def test_apply_transform(self):
        """测试变换矩阵应用"""
        # 创建单位变换矩阵
        identity_transform = np.eye(4)

        # 应用单位变换，结果应该不变
        result = mesh_align.apply_transform(
            self.test_mesh_data.copy(),
            identity_transform,
        )

        # 验证结果
        self.assertEqual(len(result), len(self.test_mesh_data))
        for mesh_name in self.test_mesh_data:
            np.testing.assert_array_almost_equal(
                result[mesh_name],
                self.test_mesh_data[mesh_name],
            )

    def test_apply_transform_to_vertices(self):
        """测试对特定顶点应用变换"""
        identity_transform = np.eye(4)
        vertex_indices = [0, 2]  # 只变换第0和第2个顶点

        result = mesh_align.apply_transform_to_vertices(
            self.test_mesh_data.copy(),
            identity_transform,
            vertex_indices,
        )

        # 验证结果结构
        self.assertEqual(len(result), len(self.test_mesh_data))
        for mesh_name in self.test_mesh_data:
            self.assertEqual(
                len(result[mesh_name]),
                len(self.test_mesh_data[mesh_name]),
            )

    def test_get_point_positions_list_mode(self):
        """测试从顶点列表获取位置"""
        vertex_positions = [
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0],
            [0.0, 0.0, 1.0],
        ]
        indices = [0, 2]

        result = mesh_align.get_point_positions(vertex_positions, indices)

        # 验证结果
        self.assertEqual(len(result), 2)
        np.testing.assert_array_equal(result[0], [0.0, 0.0, 0.0])
        np.testing.assert_array_equal(result[1], [0.0, 1.0, 0.0])

    @mock.patch("cgame_avatar_factory.api.mesh_align.cmds.xform")
    def test_get_point_positions_maya_mode(self, mock_xform):
        """测试从Maya对象获取位置"""
        # 模拟Maya命令返回值
        mock_xform.side_effect = [
            [0.0, 0.0, 0.0],  # 第一个顶点
            [1.0, 0.0, 0.0],  # 第二个顶点
        ]

        result = mesh_align.get_point_positions("test_object", [0, 1])

        # 验证Maya命令被正确调用
        self.assertEqual(mock_xform.call_count, 2)
        mock_xform.assert_any_call("test_object.vtx[0]", q=True, t=True, ws=True)
        mock_xform.assert_any_call("test_object.vtx[1]", q=True, t=True, ws=True)

        # 验证结果
        self.assertEqual(len(result), 2)

    def test_get_point_positions_invalid_source(self):
        """测试无效源类型"""
        with self.assertRaises(ValueError):
            mesh_align.get_point_positions(123, [0, 1])  # 无效的源类型

    def test_calculate_mesh_alignment_deltas(self):
        """测试网格对齐差值计算"""
        # 创建测试数据
        face_align_data = [
            {"mesh1": [[1.0, 1.0, 1.0], [2.0, 2.0, 2.0]]},  # 基础模型
            {"mesh1": [[1.5, 1.5, 1.5], [2.5, 2.5, 2.5]]},  # 对齐后模型
        ]

        original_data = [
            {"mesh1": [[1.0, 1.0, 1.0], [2.0, 2.0, 2.0]]},  # 基础模型
            {"mesh1": [[1.0, 1.0, 1.0], [2.0, 2.0, 2.0]]},  # 原始模型
        ]

        result = mesh_align.calculate_mesh_alignment_deltas(
            face_align_data,
            original_data,
        )

        # 验证结果结构
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0], face_align_data[0])  # 第一个模型应该保持不变

    def test_calculate_mesh_alignment_deltas_length_mismatch(self):
        """测试长度不匹配的情况"""
        face_align_data = [{"mesh1": [[1.0, 1.0, 1.0]]}]
        original_data = [{"mesh1": [[1.0, 1.0, 1.0]]}, {"mesh2": [[2.0, 2.0, 2.0]]}]

        with self.assertRaises(ValueError):
            mesh_align.calculate_mesh_alignment_deltas(face_align_data, original_data)

    @mock.patch("cgame_avatar_factory.api.mesh_align.utils.read_json")
    @mock.patch("cgame_avatar_factory.api.mesh_align.os.path.exists")
    def test_apply_facial_region_alignment(self, mock_exists, mock_read_json):
        """测试面部区域对齐功能"""
        # 模拟权重文件存在
        mock_exists.return_value = True

        # 模拟权重数据
        mock_read_json.return_value = {
            const.BASE_HEAD_MESH_NAME: {
                "test_region": {
                    "0": 1.0,
                    "1": 0.5,
                },
            },
        }

        # 创建测试数据
        test_mesh_collection = [
            {const.BASE_HEAD_MESH_NAME: [[0.0, 0.0, 0.0], [1.0, 0.0, 0.0]]},  # 基础模型
            {const.BASE_HEAD_MESH_NAME: [[0.5, 0.5, 0.5], [1.5, 0.5, 0.5]]},  # 目标模型
        ]

        result = mesh_align.apply_facial_region_alignment(test_mesh_collection)

        # 验证结果是深拷贝
        self.assertIsNot(result, test_mesh_collection)
        self.assertEqual(len(result), len(test_mesh_collection))

    @mock.patch("cgame_avatar_factory.api.mesh_align.utils.get_file_path_with_time")
    @mock.patch("cgame_avatar_factory.api.mesh_align.get_point_positions")
    @mock.patch("cgame_avatar_factory.api.mesh_align.compute_rigid_transform")
    @mock.patch("cgame_avatar_factory.api.mesh_align.apply_transform")
    @mock.patch("cgame_avatar_factory.api.mesh_align.apply_facial_region_alignment")
    @mock.patch("cgame_avatar_factory.api.mesh_align.calculate_mesh_alignment_deltas")
    @mock.patch("builtins.open", new_callable=mock.mock_open)
    def test_start_align(
        self,
        mock_open,
        mock_calculate_deltas,
        mock_facial_align,
        mock_apply,
        mock_compute,
        mock_get_points,
        mock_get_file_path,
    ):
        """测试完整的对齐流程"""
        # 模拟 vertices_used_for_alignment.json 文件内容
        mock_vertex_data = '{"vertex_list": [0, 1, 2, 3, 4]}'
        mock_open.return_value.read.return_value = mock_vertex_data

        # 模拟各个函数的返回值
        mock_get_points.return_value = [[0.0, 0.0, 0.0], [1.0, 0.0, 0.0]]
        mock_compute.return_value = np.eye(4)
        mock_apply.side_effect = lambda x, _: x  # 返回原始数据
        mock_facial_align.return_value = "face_aligned_data"
        mock_calculate_deltas.return_value = "mesh_deltas_data"
        mock_get_file_path.side_effect = ["mesh_path.json", "face_path.json"]

        # 创建测试数据 - 4个模型
        mesh_data = [
            {const.BASE_HEAD_MESH_NAME: [[1.0, 0.0, 0.0]]},
            {const.BASE_HEAD_MESH_NAME: [[0.0, 1.0, 0.0]]},
            {const.BASE_HEAD_MESH_NAME: [[0.0, 0.0, 1.0]]},
            {const.BASE_HEAD_MESH_NAME: [[1.0, 1.0, 1.0]]},
        ]

        # 调用被测试的函数
        mesh_deltas_result, face_align_result = mesh_align.start_align(mesh_data)

        # 验证结果
        self.assertEqual(mesh_deltas_result, "mesh_deltas_data")
        self.assertEqual(face_align_result, "face_aligned_data")

        # 验证函数调用
        self.assertEqual(mock_get_points.call_count, 4)  # 4个模型，每个调用一次
        self.assertEqual(mock_compute.call_count, 3)  # 计算3个变换矩阵
        self.assertEqual(mock_apply.call_count, 3)  # 应用3个变换
        mock_facial_align.assert_called_once()
        mock_calculate_deltas.assert_called_once()


if __name__ == "__main__":
    unittest.main()
