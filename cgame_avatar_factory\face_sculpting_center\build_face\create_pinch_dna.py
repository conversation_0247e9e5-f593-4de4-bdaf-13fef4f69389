# Import built-in modules


# Import third-party modules
from maya import cmds

# Import local modules
from cgame_avatar_factory.common import constants as const
from cgame_avatar_factory.face_sculpting_center.utils import mesh_util
import cgame_avatar_factory.face_sculpting_center.utils.dna_utils as dna


def create_pinch_dna() -> str:
    """Create and save DNA file with pinch face data

    Handles loading DNA calibration data, updating joint and vertex positions,
    and saving the modified DNA file.

    Returns:
        str: Path where DNA file was saved
    """
    reader = dna.TemplateDna.get_reader()
    output_path = mesh_util.create_workspace_dna_dir(const.PINCH_OUTPUT_DNA_NAME)
    writer = dna.init_writer_from_reader(output_path, reader)

    dna.run_joints_command(reader, writer)
    writer.write()

    return output_path


def update_vertex_positions(reader, mesh_list, writer):
    """Update vertex positions in DNA file

    Gets current vertex positions from Maya meshes
    and updates them in the DNA writer.

    Args:
        reader: DNA reader object
        mesh_list: List of mesh names
        writer: DNA writer object
    """
    mesh_count = reader.getMeshCount()
    for i in range(mesh_count):
        name = reader.getMeshName(i)
        for m in mesh_list:
            if name == m:
                vertices_position = []
                vertices = cmds.ls("{}.vtx[*]".format(name), fl=True)
                for vertex in vertices:
                    position = cmds.pointPosition(vertex, world=True)
                    vertices_position.append(position)
                writer.setVertexPositions(i, vertices_position)
