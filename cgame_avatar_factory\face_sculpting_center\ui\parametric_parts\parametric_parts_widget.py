# -*- coding: utf-8 -*-
"""
Parametric Attachments widget containing collapsible sections for eye-related features.
"""
# Import built-in modules
import logging

# Import third-party modules
import maya.utils as maya_utils
from qtpy import QtWidgets


class ParametricPartsWidget(QtWidgets.QFrame):
    """Widget for the parametric attachments tab with collapsible sections.

    Displays the '参数化附件' tab in the UI with various collapsible sections.
    """

    def __init__(self, parent=None):
        super(ParametricPartsWidget, self).__init__(parent)
        maya_utils.executeDeferred(self.startup_parametric_parts_widget)

    def startup_parametric_parts_widget(self):
        # Import third-party modules
        from dayu_widgets import MCollapse

        # Import local modules
        from cgame_avatar_factory.common.ui.layout import FramelessVLayout
        from cgame_avatar_factory.face_sculpting_center.ui.parametric_parts.parametric_catchlight_widget import (
            CatchlightWidget,
        )
        from cgame_avatar_factory.face_sculpting_center.ui.parametric_parts.parametric_eyes_widget import (
            ParametricEyesWidget,
        )

        self.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.main_layout = FramelessVLayout()
        self.main_layout.setContentsMargins(5, 5, 5, 5)
        self.setLayout(self.main_layout)

        # Create a single MCollapse widget and add sections to it
        self.collapse_widget = MCollapse()

        # Create the eyeball widget
        self.eyeball_widget = ParametricEyesWidget()

        # Create the eye light widget
        self.catchlight_widget = CatchlightWidget()

        # Define sections with their titles and widgets
        sections = [
            {"title": "眼球", "expand": True, "widget": self.eyeball_widget},
            {"title": "眼壳", "expand": True, "widget": QtWidgets.QWidget()},
            {"title": "口腔", "expand": True, "widget": QtWidgets.QWidget()},
            {"title": "眼神光片", "expand": True, "widget": self.catchlight_widget},
        ]

        # Add sections to the collapse widget
        self.collapse_widget.add_section_list(sections)

        # Add the collapse widget to the main layout
        self.main_layout.addWidget(self.collapse_widget)

        # Stretch at bottom to push collapsibles up
        self.main_layout.addStretch(1)

        # Connect to build_face signal manager for catchlight reset
        self.setup_signal_connections()

    def setup_signal_connections(self):
        """Setup signal connections for build_face process notifications."""
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.build_face.customization_rig_builder import (
            get_build_signal_manager,
        )

        try:
            signal_manager = get_build_signal_manager()
            signal_manager.catchlight_reset_signal.connect(
                self.catchlight_widget.slot_reset_catchlight_system,
            )
        except Exception as e:
            logging.warning(f"Failed to connect catchlight reset signal: {e}")
