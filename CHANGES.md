## 1.6.1 (2025-08-13)

### Fix

- bug fix

## 1.6.0 (2025-08-13)

### Fix

- lint baseline
- conflict resolution

### Feat

- automated topology and texture baking
- symmetrical addition of the model
- baking texture code organization
- baking texture
- automated creation of wrap
- automated creation of wrap ui

## 1.5.0 (2025-08-13)

### Fix

- lint and baseline
- AI comment.
- lint and baseline
- remove debug log
- change info and adjust shader param.
- maya version for wrap node
- delete component for head and hair

### Feat

- commit for dl
- add sub tab, scalp shader and adjust hair shader
- support fbx/obj and y/z up exchange

## 1.4.1 (2025-08-11)

### Fix

- lint-baseline.
- Remove top view and add 30° & 40° angle view.
- Fix logger calling.
- Set different colors for the 5 sliders.
- Add reset slider button.
- Add rotate slider & optimize code.
- Fix the Chinese character issues.​
- Only focus on mesh at first view access.
- Set the image plane's picture as the background for the view-switching button.
- Hide image planes of non-activated views.
- Synchronize slider values when switching view.
- Ensure left camera exists.
- Connect image plane attributes editing functions to UI.
- Adjust ui style.
- Support adding/deleting image plane for view.
- Functions for switching view.
- Reference image plane widgets.

## 1.4.0 (2025-08-08)

### Fix

- optimize the performance of launching

### Feat

- export catching light when exporting fbx

## 1.3.3 (2025-08-05)

### Fix

- remove chardet

## 1.3.2 (2025-08-04)

### Fix

- lint-baseline.
- Refactor MeshBuilder class.
- Remove unused code.

## 1.3.1 (2025-08-04)

### Fix

- lint
- fix json_codec and add lod_gene message box
- fix json_codec and add lod_gene message box

## 1.3.0 (2025-08-01)

### Fix

- lint and baseline
- AI comment.

### Feat

- add hair_shader and read new hair lib, some fix for hair studio.

## 1.2.1 (2025-08-01)

### Fix

- lint-baseline
- Refactor the file directory structure.
- Refactor the file directory structure.
- Refactor the ParametricCatchlights from a singleton class to module-level calls.​
- Refactor the ParametricEyes from a singleton class to module-level calls.​
- Refactor the SceneStateManager from a singleton class to module-level calls.​
- Refactor the singleton class to use module-level functions and global variables.​

## 1.2.0 (2025-07-29)

### Feat

- export catching light when exporting fbx

### Fix

- support encoding tag file with gdk type, not only utf-8

## 1.1.3 (2025-07-28)

### Fix

- lint-baseline.
- Rename ExportMonitor to EventMonitor.
- Avoid duplicate reporting during manual exports.​
- Stop monitoring save operations.
- Report exported object information.
- Avoid clearing callback ids before calling stop export monitor.
- Report all export operations.

## 1.1.2 (2025-07-23)

### Fix

- Stop timer before processing base dna.
- Disable WeCom reporting during unit test execution.
- preflight & lint.
- Add a WeCom reporting bot.​
- Display controllers exclusively in either Basic/Detailed Face Editing tabs.​

## 1.1.1 (2025-07-23)

### Fix

- test case

## 1.1.0 (2025-07-22)

### Fix

- lint and baseline
- for old test case import constant issue
- update test
- AI comment apply
- lint and baseline
- print, sperate test and different version api

### Feat

- hait studio basic

## 1.0.8 (2025-07-11)

### Fix

- collapse mesh reset back up head mesh

## 1.0.7 (2025-07-10)

### Fix

- Make the parametric eyes compatible with NBA's specs.
- Revise logging methodology and correct mesh align unit test failures.
- set Maya main window as parent for MainWidget & set Maya main window as parent for MainWidget.
- Fix the joint axis missalignment issue.
- Fix the lod axis missalignment issue.
- modify the generated code of lod
- modify the vertex reading method used for alignment
- modify the vertex reading method used for alignment
- project configuration modification

## 1.0.6 (2025-07-08)

### Fix

- random mix modify to random selection of the highlighted area

## 1.0.5 (2025-07-04)

### Fix

- face alignment code correction

## 1.0.4 (2025-07-03)

### Fix

- face align bug fix and full mix logic add
- unit test modification
- code organization
- full mix model and logic amend
- face align bug fix
- fix _create_blendshape
- build perform enhance

## 1.0.3 (2025-07-03)

### Fix

- improve logic in scene_state_mgr
- character in ring can be deleted

## 1.0.2 (2025-07-01)

### Fix

- fix note in scene_weight_mgr
- remove useless func from scene_weight_mgr
- scene_call to scene_weight_mgr
- scene state recover

## 1.0.1 (2025-06-30)

### Fix

- Fix the joint axis misalignment issue.

## 1.0.0 (2025-06-26)

### Fix

- modification of the position and fix bug of the edit model button
- the bug in the edit model button
- modification of the position of the edit model button

## 0.11.0 (2025-06-26)

### Fix

- add custom tag in config

### Feat

- Optimize facial customization features and expand control point configuration

## 0.10.2 (2025-06-24)

### Fix

- Make the build, catchlight, and screenshot processes fully compatible with both Y-up and Z-up.

## 0.10.1 (2025-06-23)

### Fix

- Improve tag filter and tree view; add drive match

## 0.10.0 (2025-06-20)

### Feat

- animation preview logic modification and animation addition and uniform management of UI colors

### Fix

- animation preview skin amend

## 0.9.0 (2025-06-20)

### Fix

- revert test_parametric

### Feat

- add character tag filter

## 0.8.3 (2025-06-12)

### Fix

- Remove the icons from items in the CatchlightListWidget.​

## 0.8.2 (2025-06-12)

### Fix

- Change the drag behavior in Catchlight to match the custom drag implementation used in the Makeup Library.​

## 0.8.1 (2025-06-11)

### Fix

- Try to fix crash when dragging catchlight icons.

## 0.8.0 (2025-06-11)

### Fix

- Fix minor issues.
- Fix unit tests errors.
- Add unit tests for parametric catchlight.
- Reset catchlight ui after rig building.
- Auto-update the build progress bar.
- Catchlight edit mode.
- boundary condition checks.
- Update catchlight ui state when selecting list item.
- Preview catchlight color in realtime.
- Set catchlight material properties.
- Set initial left/right button states based on catchlight visibility.
- Add left/right options for catchlights.
- Optimize code writing style.
- Optimize code writing style.
- Avoid resetting scale.
- rotate catchlights.
- Optimize code writing.
- Offset catchlights.
- Automatically select the new catchlight after creation.​
- Convert the ParametricEye into a singleton.​
- Scale catchlights.
- Rotate catchlights.
- Rotate catchlights.
- Align catchlights to eyes.
- Maintain the rotation panel at a 1:1 aspect ratio and normalize its range.​
- Catchlight details panel.
- Create and delete catchlights.
- Capture catchlight icons.
- Import catchlight presets.
- Support dragging catchlight presets to the catchlight list.
- first draft of catchlight UI.

### Feat

- Complete catchlight implementation.

## 0.7.1 (2025-06-06)

### Fix

- upgrade the pydantic dependency version in Maya 2022 to make it compatible with cgm_maya_shelf

## 0.7.0 (2025-06-03)

### Fix

- remove fuse in right click
- revise pydantic version
- revise dna_util

### Feat

- add face select area

## 0.6.5 (2025-05-23)

### Fix

- pince face bug fix

## 0.6.4 (2025-05-22)

### Fix

- Support drag-and-drop to reorder layers。

## 0.6.3 (2025-05-22)

### Fix

- character eyes accessory mesh add

## 0.6.2 (2025-05-21)

### Fix

- improve  export character package UI

## 0.6.1 (2025-05-20)

### Fix

- add head_tex_name

## 0.6.0 (2025-05-19)

### Fix

- lint

### Feat

- export character package

## 0.5.2 (2025-05-15)

### Fix

- bug fix for model point alignment

## 0.5.1 (2025-05-14)

### Fix

- up axis fix

## 0.5.0 (2025-05-13)

### Fix

- ui organization and add collapse mesh
- ui organ

### Feat

- collapse model add

## 0.4.5 (2025-05-13)

### Fix

- preflight & lint.
- Fix the issue where cornea curvature adjustment fails.
- Drag item from the makeup library to makeup layer.
- makeup items display.
- update parametric eyes config.

## 0.4.4 (2025-05-09)

### Fix

- Use CONFIG_PATH to load materials.
- **WIP**: makeup vanity ui.

## 0.4.3 (2025-05-09)

### Fix

- preflight & lint.
- Remove batch merging related code.
- UI framework for the materials lab part.

## 0.4.2 (2025-04-30)

### Fix

- prelight & add lod select method

## 0.4.1 (2025-04-29)

### Fix

- preflight & lint.
- Add unit tests for parametric eyes.
- Change the eye selection combo to radio buttons。
- auto match the eyeball with the eyelid.
- Adjust the timing of LOD model loading and freeze transforms after loading is complete.
- Remap slider range.
- Add eyeball reset function.
- fix the attribute reset issue when adjusting eye size and curvature.
- add rotate eyeball widget
- create expressions for symmetric free edit mode.
- Basic eye shape editing functions.
- setup eyes wrap drivers.
- adjust the UI layout for parametric eyes.
- create and align eyes shape driver.
- **WIP**: Adjust the UI interface and create classes related to parametric eyes
- update lods.fbx

## 0.4.0 (2025-04-27)

### Feat

- add the edit mode slider and turn off the button function in editing mode

## 0.3.1 (2025-04-23)

### Fix

- lint
- preflight & lint add lod generate json
- preflight & lint

## 0.3.0 (2025-04-18)

### Feat

- edit mode and face align amend add

## 0.2.5 (2025-04-16)

### Fix

- set the default menu language to Chinese
- preflight & lint
- load lod models based on configuration
- make the public library only accessible when the project's character model complies with the MetaHuman specifications
- move the icons to the resources directory
- add support for configuring local library path.
- support filtering for both public and project libraries
- Move the base DNA and vertex weights to the ​config​ directory in the project's cloud storage.
- Add the public avatar library path
- different projects retrieve the avatar library path via configuration files

## 0.2.4 (2025-04-11)

### Fix

- preflight & lint
- prevent duplicate expressions when selecting controllers
- add compatibility for both Z-Up and Y-Up
- **WIP**: Add compatibility for both Z-up and Y-up modes.

## 0.2.3 (2025-04-10)

### Fix

- modify the symmetry function of the fusion phase and controller modification

## 0.2.2 (2025-04-07)

### Fix

- example modify the area fusion logic

## 0.2.1 (2025-04-02)

### Fix

- last version file packaging failure

## 0.2.0 (2025-04-02)

### Feat

- character factory first edition submission
- character factory first edition submission

## 0.1.1 (2025-02-26)

### Fix

- move func to adam menu.

## 0.1.0 (2025-02-25)

### Feat

- add head mesh export tool
