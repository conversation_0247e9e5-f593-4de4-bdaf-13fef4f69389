"""Asset Grid View for QListView implementation.

This module provides a custom QListView configured for grid-based display
of hair assets, replacing the problematic QScrollArea + QGridLayout approach.
"""

# Import built-in modules
import json
import logging
import os
import subprocess
import sys

# Import third-party modules
from qtpy import QtCore
from qtpy import QtGui
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.constants import ASSET_ITEM_HEIGHT
from cgame_avatar_factory.hair_studio.constants import ASSET_ITEM_WIDTH
from cgame_avatar_factory.hair_studio.constants import GRID_SPACING_DEFAULT
from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_properties_dialog import (
    AssetPropertiesDialog,
)


class AssetGridView(QtWidgets.QListView):
    """Custom QListView for displaying assets in a grid layout.

    This view replaces the QScrollArea + QGridLayout implementation and provides:
    - Automatic grid layout with responsive column calculation
    - Built-in scrolling without spacing inconsistencies
    - Drag and drop support
    - Selection and interaction handling
    - Consistent spacing that doesn't change with window resizing

    Signals:
        assetSelected: Emitted when an asset is selected (dict)
        assetDragStarted: Emitted when asset drag operation starts (dict)
        assetDoubleClicked: Emitted when an asset is double-clicked (dict)
    """

    # Signals
    assetSelected = QtCore.Signal(dict)
    assetDragStarted = QtCore.Signal(dict)
    assetDoubleClicked = QtCore.Signal(dict)

    def __init__(self, parent=None):
        """Initialize the asset grid view.

        Args:
            parent (QWidget, optional): Parent widget
        """
        super().__init__(parent)

        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Initialize internal state first (needed by setup methods)
        self._base_item_size = QtCore.QSize(ASSET_ITEM_WIDTH, ASSET_ITEM_HEIGHT)
        self._current_scale = 1.0
        self._last_selected_asset = None
        self._drag_start_position = None

        # Configure basic view properties
        self._setup_view_properties()

        # Configure grid layout
        self._setup_grid_layout()

        # Configure scrolling
        self._setup_scrolling()

        # Configure drag and drop
        self._setup_drag_drop()

        # Configure selection
        self._setup_selection()

        self._logger.debug("AssetGridView initialized")

    def _setup_view_properties(self):
        """Configure basic view properties."""
        # Set view mode to icon mode for grid layout
        self.setViewMode(QtWidgets.QListView.IconMode)

        # Enable automatic layout adjustment when view is resized
        self.setResizeMode(QtWidgets.QListView.Adjust)

        # Disable item movement (items stay in their positions)
        self.setMovement(QtWidgets.QListView.Static)

        # Set layout direction
        self.setFlow(QtWidgets.QListView.LeftToRight)

        # Enable word wrap for item text
        self.setWordWrap(True)

        # Set size policy
        self.setSizePolicy(
            QtWidgets.QSizePolicy.Expanding,
            QtWidgets.QSizePolicy.Expanding,
        )

        # Fix selection border highlighting issue: Disable focus to prevent container border
        # This removes the distracting border around the entire GridView when items are selected
        # while preserving individual item selection highlighting through the delegate
        self.setFocusPolicy(QtCore.Qt.NoFocus)

    def _setup_grid_layout(self):
        """Configure grid layout properties."""
        # Set uniform item sizes for better performance and consistent layout
        self.setUniformItemSizes(True)

        # Set initial grid size
        self.setGridSize(self._base_item_size)

        # Set spacing between items - this solves the spacing inconsistency problem
        self.setSpacing(GRID_SPACING_DEFAULT)

        # Set layout mode to batched for better performance
        self.setLayoutMode(QtWidgets.QListView.Batched)
        self.setBatchSize(100)  # Process 100 items at a time

        # Fix grid alignment issue: Set symmetric content margins to center the grid
        # This ensures equal spacing on left and right sides
        margin = GRID_SPACING_DEFAULT  # Use same value as item spacing for consistency
        self.setContentsMargins(margin, margin, margin, margin)

    def _setup_scrolling(self):
        """Configure scrolling behavior."""
        # Vertical scrolling as needed
        self.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)

        # No horizontal scrolling - this solves the width matching problem
        self.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)

        # Smooth scrolling
        self.setVerticalScrollMode(QtWidgets.QAbstractItemView.ScrollPerPixel)

    def _setup_drag_drop(self):
        """Configure drag and drop behavior."""
        # Enable dragging
        self.setDragEnabled(True)

        # Set drag drop mode to drag only (we're the source, not target)
        self.setDragDropMode(QtWidgets.QAbstractItemView.DragOnly)

        # Set default drop action
        self.setDefaultDropAction(QtCore.Qt.CopyAction)

    def _setup_selection(self):
        """Configure selection behavior."""
        # Single selection mode
        self.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)

        # Select entire items
        self.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectItems)

        # Connect selection signals (will be called again in setModel)
        self._connect_selection_signals()

    def _connect_selection_signals(self):
        """Connect selection model signals."""
        selection_model = self.selectionModel()
        if selection_model:
            # Disconnect any existing connections to avoid duplicates
            try:
                selection_model.currentChanged.disconnect(self._on_selection_changed)
            except TypeError:
                pass  # No existing connection

            # Connect the signal
            selection_model.currentChanged.connect(self._on_selection_changed)

    def setModel(self, model):
        """Set the model and ensure selection signals are connected.

        Args:
            model: The model to set
        """
        super().setModel(model)
        # Reconnect selection signals after setting new model
        self._connect_selection_signals()

    def setItemSize(self, width, height):
        """Set the size of grid items.

        Args:
            width (int): Item width in pixels
            height (int): Item height in pixels
        """
        item_size = QtCore.QSize(width, height)
        self.setGridSize(item_size)

        # Force layout update
        self.scheduleDelayedItemsLayout()

        self._logger.debug(f"Item size set to {width}x{height}")

    def setScale(self, scale_factor):
        """Set the scale factor for items.

        Args:
            scale_factor (float): Scale factor (0.5 to 2.0)
        """
        # Clamp scale factor
        self._current_scale = max(0.5, min(2.0, scale_factor))

        # Calculate new size
        scaled_width = int(self._base_item_size.width() * self._current_scale)
        scaled_height = int(self._base_item_size.height() * self._current_scale)

        # Update item size
        self.setItemSize(scaled_width, scaled_height)

        self._logger.debug(f"Scale set to {self._current_scale}x")

    def getScale(self):
        """Get the current scale factor.

        Returns:
            float: Current scale factor
        """
        return self._current_scale

    def _on_selection_changed(self, current, previous):
        """Handle selection changes.

        Args:
            current (QModelIndex): Currently selected index
            previous (QModelIndex): Previously selected index
        """
        if current.isValid():
            # Get asset data from model
            model = self.model()
            if model:
                from .asset_list_model import AssetListModel

                asset_data = current.data(AssetListModel.AssetDataRole)
                if asset_data:
                    self._last_selected_asset = asset_data
                    self.assetSelected.emit(asset_data)
                    self._logger.debug(
                        f"Asset selected: {asset_data.get('name', 'Unknown')}",
                    )

    def mousePressEvent(self, event):
        """Handle mouse press events.

        Args:
            event (QMouseEvent): Mouse event
        """
        # Store the press position for drag detection
        if event.button() == QtCore.Qt.LeftButton:
            self._drag_start_position = event.pos()

        # Call parent implementation first to handle selection
        super().mousePressEvent(event)

        # Handle selection for empty areas
        index = self.indexAt(event.pos())
        if not index.isValid():
            # Clicked on empty area - clear selection
            self.clearSelection()
            self._last_selected_asset = None
        else:
            # Ensure the item is selected for potential drag operation
            if not self.selectionModel().isSelected(index):
                self.setCurrentIndex(index)

    def mouseMoveEvent(self, event):
        """Handle mouse move events for drag operations.

        Args:
            event (QMouseEvent): Mouse event
        """
        # Check if we should start a drag operation
        if event.buttons() & QtCore.Qt.LeftButton and self._drag_start_position is not None:
            # Calculate distance moved
            distance = (event.pos() - self._drag_start_position).manhattanLength()

            # Check if distance is sufficient to start drag
            if distance >= QtWidgets.QApplication.startDragDistance():
                # Get the item at the original press position
                index = self.indexAt(self._drag_start_position)
                if index.isValid():
                    # Ensure item is selected
                    self.setCurrentIndex(index)

                    # Start drag operation manually
                    self.startDrag(QtCore.Qt.CopyAction)

                    # Reset drag start position
                    self._drag_start_position = None
                    return

        # Call parent implementation for normal behavior
        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """Handle mouse release events.

        Args:
            event (QMouseEvent): Mouse event
        """
        # Reset drag start position on release
        if event.button() == QtCore.Qt.LeftButton:
            self._drag_start_position = None

        # Call parent implementation
        super().mouseReleaseEvent(event)

    def mouseDoubleClickEvent(self, event):
        """Handle mouse double-click events.

        Args:
            event (QMouseEvent): Mouse event
        """
        super().mouseDoubleClickEvent(event)

        index = self.indexAt(event.pos())
        if index.isValid():
            model = self.model()
            if model:
                from .asset_list_model import AssetListModel

                asset_data = index.data(AssetListModel.AssetDataRole)
                if asset_data:
                    self.assetDoubleClicked.emit(asset_data)
                    self._logger.debug(
                        f"Asset double-clicked: {asset_data.get('name', 'Unknown')}",
                    )

    def contextMenuEvent(self, event):
        """Handle right-click context menu events.

        Args:
            event (QContextMenuEvent): Context menu event
        """
        index = self.indexAt(event.pos())
        if not index.isValid():
            return

        # Get asset data
        model = self.model()
        if not model:
            return

        from .asset_list_model import AssetListModel

        asset_data = index.data(AssetListModel.AssetDataRole)
        if not asset_data:
            return

        # Create context menu
        menu = QtWidgets.QMenu(self)

        # Add menu actions
        add_action = menu.addAction("添加到场景")
        add_action.triggered.connect(lambda: self._add_asset_to_scene(asset_data))

        menu.addSeparator()

        # Add "打开资产路径目录" action
        open_folder_action = menu.addAction("打开资产路径目录")
        open_folder_action.triggered.connect(
            lambda: self._open_asset_folder(asset_data),
        )

        menu.addSeparator()

        properties_action = menu.addAction("属性")
        properties_action.triggered.connect(
            lambda: self._show_asset_properties(asset_data),
        )

        # Show menu at cursor position
        menu.exec_(event.globalPos())

    def _add_asset_to_scene(self, asset_data):
        """Add asset to scene (triggered from context menu).

        Args:
            asset_data (dict): Asset data
        """
        try:
            # Find the parent BaseHairTab to access component_list
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, "component_list"):
                parent_tab = parent_tab.parent()

            if parent_tab and hasattr(parent_tab, "component_list"):
                # Use the existing public interface to add component
                parent_tab.component_list.add_component(asset_data)
                self._logger.info(f"Asset added to scene: {asset_data.get('name', 'Unknown')}")
            else:
                self._logger.error("Cannot find component list to add asset")

        except Exception as e:
            self._logger.error(f"Failed to add asset to scene: {e}")

    def _open_asset_folder(self, asset_data):
        """Open the asset's directory in file explorer.

        Args:
            asset_data (dict): Asset data containing metadata with file_path
        """
        try:
            # Get file path from metadata
            metadata = asset_data.get("metadata", {})
            file_path = metadata.get("file_path", "")

            if not file_path:
                self._logger.warning(f"No file_path found in asset metadata: {asset_data.get('name', 'Unknown')}")
                return

            # Get directory path
            directory_path = os.path.dirname(file_path)

            if not os.path.exists(directory_path):
                self._logger.warning(f"Directory does not exist: {directory_path}")
                return

            # Open directory in file explorer
            if sys.platform == "win32":
                # Windows
                os.startfile(directory_path)
            elif sys.platform == "darwin":
                # macOS
                subprocess.run(["open", directory_path])
            else:
                # Linux
                subprocess.run(["xdg-open", directory_path])

            self._logger.info(f"Opened asset directory: {directory_path}")

        except Exception as e:
            self._logger.error(f"Failed to open asset directory: {e}")

    def _show_asset_properties(self, asset_data):
        """Show asset properties dialog.

        Args:
            asset_data (dict): Asset data
        """
        try:
            # Create and show the properties dialog
            dialog = AssetPropertiesDialog(asset_data, parent=self)
            dialog.exec_()
            self._logger.debug(f"Showed properties for: {asset_data.get('name', 'Unknown')}")
        except Exception as e:
            self._logger.error(f"Failed to show asset properties: {e}")

    def startDrag(self, supportedActions):
        """Start drag operation.

        Args:
            supportedActions (Qt.DropActions): Supported drop actions
        """
        index = self.currentIndex()
        if not index.isValid():
            self._logger.warning("startDrag called but no valid current index")
            return

        # Get asset data
        model = self.model()
        if not model:
            self._logger.warning("startDrag called but no model available")
            return

        from .asset_list_model import AssetListModel

        asset_data = index.data(AssetListModel.AssetDataRole)
        if not asset_data:
            self._logger.warning("startDrag called but no asset data available")
            return

        self._logger.debug(
            f"Starting drag operation for: {asset_data.get('name', 'Unknown')}",
        )

        # Create drag object
        drag = QtGui.QDrag(self)
        mime_data = QtCore.QMimeData()

        # Set asset data as JSON text (matching original AssetItem implementation)
        asset_json = json.dumps(asset_data)
        mime_data.setText(asset_json)

        # Set custom MIME type for hair studio assets (matching original)
        mime_data.setData(
            "application/x-hair-asset",
            QtCore.QByteArray(asset_json.encode("utf-8")),
        )

        drag.setMimeData(mime_data)

        # Create drag pixmap
        pixmap = self._create_drag_pixmap(asset_data)
        if not pixmap.isNull():
            drag.setPixmap(pixmap)
            drag.setHotSpot(QtCore.QPoint(pixmap.width() // 2, pixmap.height() // 2))

        # Emit drag started signal BEFORE executing drag
        self.assetDragStarted.emit(asset_data)

        # Execute drag
        result = drag.exec_(supportedActions)

        self._logger.debug(
            f"Drag operation completed: {asset_data.get('name', 'Unknown')}, result: {result}",
        )

        return result

    def _create_drag_pixmap(self, asset_data):
        """Create a pixmap for drag operations.

        Args:
            asset_data (dict): Asset data

        Returns:
            QPixmap: Drag pixmap
        """
        # Create pixmap with current item size
        current_size = self.gridSize()
        pixmap = QtGui.QPixmap(current_size)
        pixmap.fill(QtCore.Qt.transparent)

        painter = QtGui.QPainter(pixmap)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # Draw semi-transparent background
        painter.fillRect(
            pixmap.rect(),
            QtGui.QColor(64, 128, 255, 128),  # Semi-transparent blue
        )

        # Draw border
        painter.setPen(QtGui.QPen(QtGui.QColor(64, 128, 255), 2))
        painter.drawRect(pixmap.rect().adjusted(1, 1, -1, -1))

        # Skip thumbnail loading during drag to avoid I/O blocking
        # Just draw a simple icon placeholder instead
        icon_rect = pixmap.rect().adjusted(20, 20, -20, -30)  # Leave space for text
        painter.fillRect(icon_rect, QtGui.QColor(128, 128, 128, 180))

        # Draw a simple asset icon
        painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 255), 2))
        painter.drawRect(icon_rect.adjusted(4, 4, -4, -4))

        # Draw a simple "A" for Asset
        painter.setFont(QtGui.QFont("Arial", 16, QtGui.QFont.Bold))
        painter.drawText(icon_rect, QtCore.Qt.AlignCenter, "A")

        # Draw asset name
        painter.setPen(QtGui.QColor(255, 255, 255))
        font = painter.font()
        font.setPointSize(8)
        font.setBold(True)
        painter.setFont(font)

        text_rect = QtCore.QRect(4, pixmap.height() - 20, pixmap.width() - 8, 16)

        asset_name = asset_data.get("name", "Asset")
        painter.drawText(text_rect, QtCore.Qt.AlignCenter, asset_name)

        painter.end()
        return pixmap

    def wheelEvent(self, event):
        """Handle mouse wheel events for zooming.

        Args:
            event (QWheelEvent): Wheel event
        """
        # Check if Ctrl is pressed for zoom
        if event.modifiers() & QtCore.Qt.ControlModifier:
            # Calculate zoom delta
            delta = event.angleDelta().y()
            zoom_factor = 1.1 if delta > 0 else 0.9

            # Apply zoom
            new_scale = self._current_scale * zoom_factor
            self.setScale(new_scale)

            # Accept event to prevent scrolling
            event.accept()
        else:
            # Normal scrolling
            super().wheelEvent(event)

    def keyPressEvent(self, event):
        """Handle key press events.

        Args:
            event (QKeyEvent): Key event
        """
        # Handle Enter/Return key
        if event.key() in (QtCore.Qt.Key_Return, QtCore.Qt.Key_Enter):
            current_index = self.currentIndex()
            if current_index.isValid():
                model = self.model()
                if model:
                    from .asset_list_model import AssetListModel

                    asset_data = current_index.data(AssetListModel.AssetDataRole)
                    if asset_data:
                        self.assetDoubleClicked.emit(asset_data)
                        event.accept()
                        return

        # Handle other keys
        super().keyPressEvent(event)

    def getSelectedAsset(self):
        """Get the currently selected asset.

        Returns:
            dict or None: Selected asset data, or None if no selection
        """
        return self._last_selected_asset

    def selectAssetById(self, asset_id):
        """Select an asset by its ID.

        Args:
            asset_id (str): Asset ID to select

        Returns:
            bool: True if asset was found and selected
        """
        model = self.model()
        if not model:
            return False

        # Search for asset in model
        for row in range(model.rowCount()):
            index = model.createIndex(row, 0)
            from .asset_list_model import AssetListModel

            current_id = index.data(AssetListModel.AssetIdRole)
            if current_id == asset_id:
                self.setCurrentIndex(index)
                return True

        return False

    def clearSelection(self):
        """Clear the current selection."""
        super().clearSelection()
        self._last_selected_asset = None

    def resizeEvent(self, event):
        """Handle resize events.

        Args:
            event (QResizeEvent): Resize event
        """
        super().resizeEvent(event)

        # QListView automatically handles column recalculation
        # This solves the responsive layout problem without manual calculation
        self._logger.debug(
            f"View resized to {event.size().width()}x{event.size().height()}",
        )

    def scrollToAsset(self, asset_id):
        """Scroll to make an asset visible.

        Args:
            asset_id (str): Asset ID to scroll to

        Returns:
            bool: True if asset was found
        """
        model = self.model()
        if not model:
            return False

        # Find asset index
        for row in range(model.rowCount()):
            index = model.createIndex(row, 0)
            from .asset_list_model import AssetListModel

            current_id = index.data(AssetListModel.AssetIdRole)
            if current_id == asset_id:
                self.scrollTo(index, QtWidgets.QAbstractItemView.EnsureVisible)
                return True

        return False
