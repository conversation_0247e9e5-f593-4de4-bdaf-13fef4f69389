#!/usr/bin/env python
"""Test suite for AssetItemDelegate."""

# Import built-in modules
import os
import sys

# Add project root to path
project_root = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))),
)
sys.path.insert(0, project_root)


def test_asset_item_delegate():
    """Test AssetItemDelegate functionality."""
    print("=== Testing AssetItemDelegate ===")

    try:
        print("1. Testing imports...")
        # Import third-party modules
        from qtpy import QtCore
        from qtpy import QtGui
        from qtpy import QtWidgets

        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_item_delegate import (
            AssetItemDelegate,
        )
        from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_list_model import (
            AssetListModel,
        )

        print("   ✓ All imports successful")

        print("2. Creating QApplication...")
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication([])
        print("   ✓ QApplication ready")

        print("3. Testing AssetItemDelegate creation...")
        delegate = AssetItemDelegate()
        print("   ✓ AssetItemDelegate created")

        print("4. Testing scale functionality...")
        delegate.setScale(1.5)
        assert delegate.getScale() == 1.5, f"Scale should be 1.5, got {delegate.getScale()}"

        delegate.setScale(3.0)  # Should clamp to 2.0
        assert delegate.getScale() == 2.0, f"Scale should be clamped to 2.0, got {delegate.getScale()}"

        delegate.setScale(0.1)  # Should clamp to 0.5
        assert delegate.getScale() == 0.5, f"Scale should be clamped to 0.5, got {delegate.getScale()}"
        print("   ✓ Scale functionality works")

        print("5. Testing size hint...")
        option = QtWidgets.QStyleOptionViewItem()
        index = QtCore.QModelIndex()
        size_hint = delegate.sizeHint(option, index)
        assert size_hint.width() > 0 and size_hint.height() > 0, "Size hint should have valid dimensions"
        print(f"   ✓ Size hint: {size_hint.width()}x{size_hint.height()}")

        print("6. Testing with model integration...")
        model = AssetListModel()
        test_assets = [
            {"id": "test1", "name": "Test Asset 1", "asset_type": "card", "thumbnail": "/nonexistent.jpg"},
            {"id": "test2", "name": "Very Long Asset Name That Should Be Elided", "asset_type": "xgen"},
        ]
        model.setAssets(test_assets)

        # Test with valid model index
        first_index = model.createIndex(0, 0)
        size_hint = delegate.sizeHint(option, first_index)
        assert size_hint.width() > 0 and size_hint.height() > 0, "Size hint with model should be valid"
        print("   ✓ Model integration works")

        print("7. Testing thumbnail cache...")
        delegate.clearThumbnailCache()
        print("   ✓ Thumbnail cache operations work")

        print("8. Testing paint method (basic)...")
        # Create a test pixmap to paint on
        pixmap = QtGui.QPixmap(200, 200)
        pixmap.fill(QtCore.Qt.white)
        painter = QtGui.QPainter(pixmap)

        # Create test option
        option = QtWidgets.QStyleOptionViewItem()
        option.rect = QtCore.QRect(10, 10, 120, 140)
        option.state = QtWidgets.QStyle.State_None

        # Test painting
        delegate.paint(painter, option, first_index)
        painter.end()

        assert not pixmap.isNull(), "Paint operation should produce valid pixmap"
        print("   ✓ Paint method works")

        print("9. Testing paint with selection state...")
        pixmap2 = QtGui.QPixmap(200, 200)
        pixmap2.fill(QtCore.Qt.white)
        painter2 = QtGui.QPainter(pixmap2)

        # Test with selected state
        option.state = QtWidgets.QStyle.State_Selected
        delegate.paint(painter2, option, first_index)
        painter2.end()

        assert not pixmap2.isNull(), "Paint with selection should produce valid pixmap"
        print("   ✓ Paint with selection works")

        print("10. Testing paint with hover state...")
        pixmap3 = QtGui.QPixmap(200, 200)
        pixmap3.fill(QtCore.Qt.white)
        painter3 = QtGui.QPainter(pixmap3)

        # Test with hover state
        option.state = QtWidgets.QStyle.State_MouseOver
        delegate.paint(painter3, option, first_index)
        painter3.end()

        assert not pixmap3.isNull(), "Paint with hover should produce valid pixmap"
        print("   ✓ Paint with hover works")

        print("\n🎉 All AssetItemDelegate tests passed!")
        return True

    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        # Import built-in modules
        import traceback

        traceback.print_exc()
        return False
    except AssertionError as e:
        print(f"   ❌ Test assertion failed: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        # Import built-in modules
        import traceback

        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("=" * 50)
    print("AssetItemDelegate Test")
    print("=" * 50)

    success = test_asset_item_delegate()

    print("\n" + "=" * 50)
    if success:
        print("✅ AssetItemDelegate implementation is working correctly!")
        print("✅ Ready to proceed to ResponsiveAssetLibrary")
    else:
        print("❌ AssetItemDelegate tests failed - please fix issues")
    print("=" * 50)

    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
