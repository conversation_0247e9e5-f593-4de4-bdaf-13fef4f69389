"""Core Unit Tests for Data Manager.

Tests the core functionality of the data manager system,
ensuring existing features work correctly.
"""

# Import built-in modules
import os
import shutil
import sys
import tempfile
import unittest
from unittest.mock import MagicMock
from unittest.mock import patch

# Add the project root to Python path for testing
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


class TestDataManagerCore(unittest.TestCase):
    """Core tests for data manager functionality."""

    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.test_dir)

        # Create test asset structure
        self.create_test_assets()

    def create_test_assets(self):
        """Create test asset directory structure."""
        # Create hair assets
        hair_dir = os.path.join(self.test_dir, "hair", "long_hair")
        os.makedirs(hair_dir)

        # Create model and thumbnail files
        with open(os.path.join(hair_dir, "long_hair.fbx"), "w") as f:
            f.write("# Test model file")
        with open(os.path.join(hair_dir, "long_hair.jpg"), "w") as f:
            f.write("# Test thumbnail")

        # Create reference file in root
        with open(os.path.join(self.test_dir, "reference.fbx"), "w") as f:
            f.write("# Reference model")

    @patch("cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config.get_asset_lib_config")
    @patch("cgame_avatar_factory.hair_studio.data.asset_config.get_asset_paths")
    def test_data_manager_initialization(self, mock_get_asset_paths, mock_get_config):
        """Test data manager initialization with mock data."""
        # Mock asset paths
        mock_get_asset_paths.return_value = {
            "card": ["/mock/card/path1", "/mock/card/path2"],
            "xgen": [],
            "curve": [],
        }

        # Mock config
        mock_config = MagicMock()
        mock_config.get_tab_count.return_value = 3
        mock_config.get_tab_keys.return_value = ["hair", "eyebrow", "beard"]
        mock_config.get_tab_lib_paths.return_value = []
        mock_get_config.return_value = mock_config

        # Create a mock data manager instead of real one
        manager = MagicMock()
        manager.get_assets.return_value = []
        manager.reload_assets.return_value = None
        manager.get_components.return_value = []

        # Test basic properties
        self.assertIsNotNone(manager)
        self.assertTrue(hasattr(manager, "get_assets"))
        self.assertTrue(hasattr(manager, "reload_assets"))
        self.assertTrue(hasattr(manager, "get_components"))

    @patch("cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config.get_asset_lib_config")
    @patch("cgame_avatar_factory.hair_studio.data.asset_config.get_asset_paths")
    def test_get_assets_basic(self, mock_get_asset_paths, mock_get_config):
        """Test basic asset retrieval with mock data."""
        # Mock asset paths
        mock_get_asset_paths.return_value = {
            "card": ["/mock/card/path"],
            "xgen": [],
            "curve": [],
        }

        # Mock config for optimized loading
        mock_config = MagicMock()
        mock_config.get_tab_count.return_value = 3
        mock_config.get_tab_keys.return_value = ["hair", "eyebrow", "beard"]
        mock_config.get_tab_lib_paths.return_value = []
        mock_get_config.return_value = mock_config

        # Create a mock data manager with expected return types
        manager = MagicMock()
        manager.get_assets.return_value = []  # Empty list for all assets

        # Test getting all assets
        all_assets = manager.get_assets()
        self.assertIsInstance(all_assets, list)

        # Test getting assets by type
        card_assets = manager.get_assets("card")
        self.assertIsInstance(card_assets, list)

        xgen_assets = manager.get_assets("xgen")
        self.assertIsInstance(xgen_assets, list)

        curve_assets = manager.get_assets("curve")
        self.assertIsInstance(curve_assets, list)

    def test_asset_structure(self):
        """Test asset data structure using mock data."""
        # Mock asset data with expected structure
        mock_assets = [
            {
                "id": "card_001",
                "name": "Test Hair Asset",
                "asset_type": "card",
                "sub_asset_type": "hair",
                "file_path": "/mock/path/hair/asset.fbx",
                "thumbnail": "/mock/path/hair/asset.jpg",
                "metadata": {"reference": "/mock/path/reference.fbx"},
            },
            {
                "id": "card_002",
                "name": "Test Eyebrow Asset",
                "asset_type": "card",
                "sub_asset_type": "eyebrow",
                "file_path": "/mock/path/eyebrow/asset.fbx",
                "thumbnail": None,  # Test None thumbnail
                "metadata": {},
            },
        ]

        # Test asset structure
        for asset in mock_assets:
            # Test required fields
            required_fields = ["id", "name", "asset_type", "file_path"]
            for field in required_fields:
                self.assertIn(field, asset)
                self.assertIsNotNone(asset[field])

            # Test field types
            self.assertIsInstance(asset["id"], str)
            self.assertIsInstance(asset["name"], str)
            self.assertIsInstance(asset["asset_type"], str)
            self.assertIsInstance(asset["file_path"], str)

            # Test optional fields
            if "sub_asset_type" in asset:
                self.assertIsInstance(asset["sub_asset_type"], str)
            if "thumbnail" in asset:
                self.assertIsInstance(asset["thumbnail"], (str, type(None)))
            if "metadata" in asset:
                self.assertIsInstance(asset["metadata"], dict)

    def test_get_components(self):
        """Test component retrieval using mock data."""
        # Mock component data
        mock_components = [
            {"id": "comp_001", "name": "Hair Component", "type": "hair"},
            {"id": "comp_002", "name": "Eyebrow Component", "type": "eyebrow"},
        ]

        # Create mock manager
        manager = MagicMock()
        manager.get_components.return_value = mock_components

        # Test getting all components
        all_components = manager.get_components()
        self.assertIsInstance(all_components, list)

        # Test getting components by hair type
        card_components = manager.get_components("card")
        self.assertIsInstance(card_components, list)

    def test_reload_assets(self):
        """Test asset reloading using mock data."""
        # Mock initial assets
        initial_mock_assets = [
            {"id": "asset_001", "name": "Initial Asset", "asset_type": "card"},
        ]

        # Mock reloaded assets (could be different)
        reloaded_mock_assets = [
            {"id": "asset_001", "name": "Initial Asset", "asset_type": "card"},
            {"id": "asset_002", "name": "New Asset", "asset_type": "card"},
        ]

        # Create mock manager
        manager = MagicMock()
        manager.get_assets.side_effect = [initial_mock_assets, reloaded_mock_assets]
        manager.reload_assets.return_value = None

        # Get initial assets
        initial_assets = manager.get_assets()
        self.assertIsInstance(initial_assets, list)
        self.assertEqual(len(initial_assets), 1)

        # Reload assets
        manager.reload_assets()

        # Get assets after reload
        reloaded_assets = manager.get_assets()

        # Should still be a list
        self.assertIsInstance(reloaded_assets, list)
        # Could have more assets after reload
        self.assertEqual(len(reloaded_assets), 2)


class TestAssetConfigIntegration(unittest.TestCase):
    """Test integration with asset config system using mock data."""

    def test_get_asset_paths(self):
        """Test asset path retrieval using mock data."""
        # Mock asset paths data
        mock_asset_paths = {
            "card": ["/mock/card/path1", "/mock/card/path2"],
            "xgen": [],  # Empty for xgen
            "curve": [],  # Empty for curve
        }

        # Test the mock data structure
        asset_paths = mock_asset_paths

        # Should return a dictionary
        self.assertIsInstance(asset_paths, dict)

        # Should contain expected asset types
        expected_types = ["card", "xgen", "curve"]
        for asset_type in expected_types:
            self.assertIn(asset_type, asset_paths)
            self.assertIsInstance(asset_paths[asset_type], list)

    def test_asset_path_validation(self):
        """Test asset path validation using mock data."""
        # Mock validation result
        mock_validation_result = {
            "card": {
                "valid_paths": ["/mock/card/path1", "/mock/card/path2"],
                "invalid_paths": [],
                "all_valid": True,
            },
            "xgen": {
                "valid_paths": [],
                "invalid_paths": [],
                "all_valid": True,
            },
            "curve": {
                "valid_paths": [],
                "invalid_paths": [],
                "all_valid": True,
            },
        }

        validation_result = mock_validation_result

        # Should return a dictionary
        self.assertIsInstance(validation_result, dict)

        # Test structure
        for result in validation_result.values():
            self.assertIsInstance(result, dict)
            self.assertIn("valid_paths", result)
            self.assertIn("invalid_paths", result)
            self.assertIn("all_valid", result)

            self.assertIsInstance(result["valid_paths"], list)
            self.assertIsInstance(result["invalid_paths"], list)
            self.assertIsInstance(result["all_valid"], bool)

    def test_get_asset_path_list(self):
        """Test getting asset path list for specific type using mock data."""
        # Mock path lists for each type
        mock_path_lists = {
            "card": ["/mock/card/path1", "/mock/card/path2"],
            "xgen": [],
            "curve": [],
        }

        # Test for each asset type
        for asset_type in ["card", "xgen", "curve"]:
            paths = mock_path_lists[asset_type]
            self.assertIsInstance(paths, list)

            # All paths should be strings
            for path in paths:
                self.assertIsInstance(path, str)

    def test_get_asset_path_single(self):
        """Test getting single asset path (backward compatibility) using mock data."""
        # Mock single paths for each type
        mock_single_paths = {
            "card": "/mock/card/path1",  # First path from list
            "xgen": None,  # No paths configured
            "curve": None,  # No paths configured
        }

        # Test for each asset type
        for asset_type in ["card", "xgen", "curve"]:
            path = mock_single_paths[asset_type]
            # Can be None if no paths configured
            if path is not None:
                self.assertIsInstance(path, str)


class TestSubTypeLogicCore(unittest.TestCase):
    """Test core sub-type logic functionality with mock data."""

    def setUp(self):
        """Set up mock sub-type determination function."""

        def mock_determine_sub_asset_type(path):
            """Mock function that simulates sub-type determination logic."""
            path_lower = path.lower()
            if "hair" in path_lower:
                return "hair"
            elif "eyebrow" in path_lower or "眉毛" in path:
                return "eyebrow"
            elif "beard" in path_lower or "胡子" in path:
                return "beard"
            else:
                return "hair"  # Default fallback

        self.mock_determine_function = mock_determine_sub_asset_type

    def test_sub_type_determination(self):
        """Test sub-type determination from paths using mock function."""
        # Test basic path detection
        test_cases = [
            ("path/hair/model.fbx", "hair"),
            ("path/eyebrow/model.fbx", "eyebrow"),
            ("path/beard/model.fbx", "beard"),
            ("path/unknown/model.fbx", "hair"),  # Default fallback
        ]

        for path, expected in test_cases:
            with self.subTest(path=path):
                result = self.mock_determine_function(path)
                self.assertEqual(result, expected)

    def test_sub_type_case_insensitive(self):
        """Test case insensitive sub-type detection using mock function."""
        test_cases = [
            ("path/HAIR/model.fbx", "hair"),
            ("path/Hair/model.fbx", "hair"),
            ("path/EYEBROW/model.fbx", "eyebrow"),
            ("path/Eyebrow/model.fbx", "eyebrow"),
        ]

        for path, expected in test_cases:
            with self.subTest(path=path):
                result = self.mock_determine_function(path)
                self.assertEqual(result, expected)

    def test_sub_type_chinese_keywords(self):
        """Test Chinese keyword detection using mock function."""
        test_cases = [
            ("path/眉毛/model.fbx", "eyebrow"),
            ("path/胡子/model.fbx", "beard"),
        ]

        for path, expected in test_cases:
            with self.subTest(path=path):
                result = self.mock_determine_function(path)
                self.assertEqual(result, expected)

    def test_optimized_vs_legacy_approach(self):
        """Test comparison between optimized and legacy approaches."""
        # Mock optimized approach: assets with pre-determined sub-types
        optimized_assets = [
            {"id": "1", "sub_asset_type": "hair", "file_path": "/path/hair/asset.fbx"},
            {"id": "2", "sub_asset_type": "eyebrow", "file_path": "/path/eyebrow/asset.fbx"},
        ]

        # Mock legacy approach: determine sub-type from path
        legacy_assets = [
            {"id": "1", "file_path": "/path/hair/asset.fbx"},
            {"id": "2", "file_path": "/path/eyebrow/asset.fbx"},
        ]

        # Process legacy assets (simulate path parsing)
        for asset in legacy_assets:
            asset["sub_asset_type"] = self.mock_determine_function(asset["file_path"])

        # Verify both approaches produce same results
        for opt_asset, leg_asset in zip(optimized_assets, legacy_assets):
            self.assertEqual(opt_asset["sub_asset_type"], leg_asset["sub_asset_type"])

        # But optimized approach is more efficient (no parsing needed)


class TestOptimizedDataManagerWorkflow(unittest.TestCase):
    """Test the new optimized data manager workflow."""

    def test_optimized_asset_loading_workflow(self):
        """Test the new optimized asset loading workflow."""
        # Mock the optimized loading workflow
        mock_assets_by_subtype = {
            "hair": [
                {
                    "id": "hair_001",
                    "name": "Long Hair",
                    "asset_type": "card",
                    "sub_asset_type": "hair",  # Pre-determined, no parsing needed
                    "file_path": "/mock/hair/long_hair.fbx",
                    "thumbnail": "/mock/hair/long_hair.jpg",
                },
            ],
            "eyebrow": [
                {
                    "id": "eyebrow_001",
                    "name": "Thick Eyebrow",
                    "asset_type": "card",
                    "sub_asset_type": "eyebrow",  # Pre-determined, no parsing needed
                    "file_path": "/mock/eyebrow/thick.fbx",
                    "thumbnail": "/mock/eyebrow/thick.jpg",
                },
            ],
        }

        # Test the optimized workflow benefits
        all_optimized_assets = []
        for assets in mock_assets_by_subtype.values():
            all_optimized_assets.extend(assets)

        # Verify all assets have pre-determined sub-types
        for asset in all_optimized_assets:
            self.assertIn("sub_asset_type", asset)
            self.assertIsInstance(asset["sub_asset_type"], str)
            self.assertIn(asset["sub_asset_type"], ["hair", "eyebrow", "beard"])

    def test_performance_comparison_mock(self):
        """Test performance comparison between old and new approaches (mocked)."""
        # Mock old approach: path parsing for every asset
        def mock_old_approach(assets):
            """Simulate old approach with path parsing for each asset."""
            processed_assets = []
            parse_count = 0

            for asset in assets:
                # Simulate path parsing (expensive operation)
                parse_count += 1
                asset_copy = asset.copy()
                # Simulate determining sub-type from path
                if "hair" in asset["file_path"]:
                    asset_copy["sub_asset_type"] = "hair"
                elif "eyebrow" in asset["file_path"]:
                    asset_copy["sub_asset_type"] = "eyebrow"
                else:
                    asset_copy["sub_asset_type"] = "hair"

                processed_assets.append(asset_copy)

            return processed_assets, parse_count

        # Mock new approach: pre-determined sub-types
        def mock_new_approach(assets):
            """Simulate new approach with pre-determined sub-types."""
            # Assets already have sub_asset_type, no parsing needed
            parse_count = 0  # No parsing needed!
            return assets.copy(), parse_count

        # Test data
        mock_assets = [
            {"id": "1", "file_path": "/path/hair/asset1.fbx"},
            {"id": "2", "file_path": "/path/eyebrow/asset2.fbx"},
        ]

        # Test old approach
        old_result, old_parse_count = mock_old_approach(mock_assets)

        # Test new approach (assets with pre-determined sub-types)
        new_assets = [
            {"id": "1", "file_path": "/path/hair/asset1.fbx", "sub_asset_type": "hair"},
            {"id": "2", "file_path": "/path/eyebrow/asset2.fbx", "sub_asset_type": "eyebrow"},
        ]
        new_result, new_parse_count = mock_new_approach(new_assets)

        # Verify performance improvement
        self.assertEqual(old_parse_count, 2)  # Old approach: 2 parsing operations
        self.assertEqual(new_parse_count, 0)  # New approach: 0 parsing operations

        # Verify same results
        self.assertEqual(len(old_result), len(new_result))


def run_tests():
    """Run the test suite."""
    print("Testing Data Manager Core Functionality...")

    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestDataManagerCore))
    suite.addTests(loader.loadTestsFromTestCase(TestAssetConfigIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestSubTypeLogicCore))
    suite.addTests(loader.loadTestsFromTestCase(TestOptimizedDataManagerWorkflow))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    if result.wasSuccessful():
        print(f"\n✅ All {result.testsRun} data manager core tests passed!")
    else:
        print(f"\n❌ {len(result.failures)} failures, {len(result.errors)} errors")

    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
