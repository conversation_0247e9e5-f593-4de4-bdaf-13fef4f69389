# Import Maya commands using unified import system
import logging
from cgame_avatar_factory.hair_studio.maya_api.utils import get_maya_cmds

# Get Maya commands (real or mock)
cmds = get_maya_cmds(__name__)

################################################################# maya scene utils##############################################################

def delete_maya_node(node_name):
    """Delete node from maya scene."""

    if not cmds.objExists(node_name):
        cmds.wrarning("Node does not exist: {}".format(node_name))
        return False

    parent = cmds.listRelatives(node_name, p=True)
    try:
        cmds.delete(node_name)
        if parent:
            children = cmds.listRelatives(parent, c=True)
            if not children:
                cmds.delete(parent)
    except Exception as e:
        cmds.error(f"Failed to delete node {node_name}: {e}")
        return False

    return True


def check_node_exists(node_name):
    """Check if a node exists in Maya scene.

    Args:
        node_name (str): Name of the node to check

    Returns:
        bool: True if node exists, False otherwise
    """
    logger = logging.getLogger(__name__)

    try:
        exists = cmds.objExists(node_name)
        logger.debug("Maya API: Node '%s' exists: %s", node_name, exists)
        return exists
    except Exception as e:
        logger.warning("Maya API: Error checking node existence '%s': %s", node_name, str(e))
        return False


def select_maya_node(node_name):
    """Select a node in Maya scene.

    Args:
        node_name (str): Name of the node to select

    Returns:
        bool: True if selection was successful, False otherwise
    """
    logger = logging.getLogger(__name__)

    try:
        if not cmds.objExists(node_name):
            logger.warning("Maya API: Cannot select node '%s' - does not exist", node_name)
            return False

        cmds.select(node_name, replace=True)
        logger.debug("Maya API: Successfully selected node '%s'", node_name)
        return True
    except Exception as e:
        logger.warning("Maya API: Error selecting node '%s': %s", node_name, str(e))
        return False

def set_maya_attribute(node_name, attr_name, value):
    """Set attribute of a node in Maya scene.
    
    Args:
        node_name (str): Name of the node
        attr_name (str): Name of the attribute
        value (any): Value to set

    Returns:
        bool: True if successful, False otherwise
    """
    logger = logging.getLogger(__name__)

    try:
        if not cmds.objExists(node_name):
            logger.warning("Maya API: Cannot set attribute '%s' - node '%s' does not exist", attr_name, node_name)
            return False
        
        cmds.setAttr(f"{node_name}.{attr_name}", value)
        logger.debug("Maya API: Successfully set attribute '%s' to '%s' for node '%s'", attr_name, value, node_name)
        return True
    except Exception as e:
        logger.warning("Maya API: Error setting attribute '%s' for node '%s': %s", attr_name, node_name, str(e))
        return False
    
################################################################# maya scene utils##############################################################