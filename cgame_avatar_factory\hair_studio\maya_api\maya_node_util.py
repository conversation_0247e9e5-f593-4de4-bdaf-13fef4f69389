# Import Maya commands using unified import system
from cgame_avatar_factory.hair_studio.maya_api.utils import get_maya_cmds

# Get Maya commands (real or mock)
cmds = get_maya_cmds(__name__)

################################################################# maya scene utils##############################################################

def delete_maya_node(node_name):
    """Delete node from maya scene."""

    if not cmds.objExists(node_name):
        return False

    parent = cmds.listRelatives(node_name, p=True)
    try:
        cmds.delete(node_name)
        if parent:
            children = cmds.listRelatives(parent, c=True)
            if not children:
                cmds.delete(parent)
    except Exception as e:
        cmds.error(f"Failed to delete node {node_name}: {e}")
        return False

    return True



################################################################# maya scene utils##############################################################