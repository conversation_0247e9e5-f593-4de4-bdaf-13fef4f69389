#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
表情工坊模块

提供表情编辑和预览功能的界面组件
"""

# Import built-in modules
# 导入内置模块
import logging

# Import third-party modules
# 导入第三方模块
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.animation_theater.ui.face_anim_preview import FacePreviewWidget
from cgame_avatar_factory.animation_theater.ui.face_rig_editor import FaceRigEditorWidget

# 导入本地模块
from cgame_avatar_factory.common.ui.layout import FramelessVLayout


class FaceWorkshopWidget(QtWidgets.QWidget):
    """
    表情工坊组件

    提供表情编辑和预览功能的界面
    左侧为表情编辑区域，占比65%
    右侧为表情预览和列表区域，占比35%
    """

    LEFT_RATIO = 0.65
    RIGHT_RATIO = 0.35

    sig_face_selected = QtCore.Signal(str)
    sig_face_edited = QtCore.Signal(dict)
    sig_face_deleted = QtCore.Signal(str)

    def __init__(self, parent=None):
        super(FaceWorkshopWidget, self).__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.setup_ui()

    def setup_ui(self):
        """初始化UI组件"""
        layout = FramelessVLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        self.setLayout(layout)

        splitter = QtWidgets.QSplitter(QtCore.Qt.Horizontal)
        splitter.setHandleWidth(1)
        splitter.setChildrenCollapsible(False)
        layout.addWidget(splitter)

        self.left_canvas = FaceRigEditorWidget()
        self.left_canvas.setObjectName("faceEditArea")

        self.right_canvas = FacePreviewWidget()
        self.right_canvas.setObjectName("facePreviewArea")

        self.right_canvas.sig_face_selected.connect(self.sig_face_selected)
        self.right_canvas.sig_face_deleted.connect(self.sig_face_deleted)

        splitter.addWidget(self.left_canvas)
        splitter.addWidget(self.right_canvas)

        width = 1000
        splitter.setSizes([int(width * self.LEFT_RATIO), int(width * self.RIGHT_RATIO)])
        self.resizeEvent = lambda event: splitter.setSizes(
            [int(splitter.width() * self.LEFT_RATIO), int(splitter.width() * self.RIGHT_RATIO)],
        )
