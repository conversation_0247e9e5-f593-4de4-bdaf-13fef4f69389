"""Component List Module.

This module provides the component list widget for the Hair Studio tool.
It displays the list of hair components that have been added to the scene.
"""

# Import built-in modules
import json

# Import standard library
import logging

# Import third-party modules
# Import dayu widgets
from dayu_widgets import <PERSON><PERSON><PERSON><PERSON>ayout
from dayu_widgets import <PERSON><PERSON><PERSON><PERSON>
from dayu_widgets import <PERSON><PERSON><PERSON><PERSON>ie<PERSON>
from dayu_widgets import MToolButton

# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets
from qtpy.QtCore import Qt
from qtpy.QtGui import QStandardItem
from qtpy.QtGui import QStandardItemModel

# Import local modules
from cgame_avatar_factory.hair_studio.constants import BOTTOM_AREA_SPACING
from cgame_avatar_factory.hair_studio.constants import BUTTON_AREA_BUTTON_SIZE
from cgame_avatar_factory.hair_studio.constants import BUTTON_AREA_HEIGHT
from cgame_avatar_factory.hair_studio.constants import BUTTON_AREA_MARGIN
from cgame_avatar_factory.hair_studio.constants import COMPONENT_ITEM_HEIGHT
from cgame_avatar_factory.hair_studio.constants import ICON_ADD_LINE
from cgame_avatar_factory.hair_studio.constants import ICON_TRASH_LINE
from cgame_avatar_factory.hair_studio.constants import LAYOUT_SPACING_MEDIUM
from cgame_avatar_factory.hair_studio.constants import LAYOUT_SPACING_SMALL
from cgame_avatar_factory.hair_studio.constants import SUBTITLE_STYLE
from cgame_avatar_factory.hair_studio.constants import (
    BORDER_WIDTH_MEDIUM,
)  # Bottom area constants for unified height management
from cgame_avatar_factory.hair_studio.constants import BOTTOM_AREA_PADDING_HORIZONTAL
from cgame_avatar_factory.hair_studio.constants import COMPONENT_LIST_ITEM_MARGIN
from cgame_avatar_factory.hair_studio.constants import OBJECT_NAME_COMPONENT_LIST_FORMAT
from cgame_avatar_factory.hair_studio.constants import UI_TEXT_HAIR_COMPONENT_LIST
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
from cgame_avatar_factory.hair_studio.ui.component_context_menu import ComponentContextMenu
from cgame_avatar_factory.hair_studio.ui.component_context_menu import ComponentItemContextHandler
from cgame_avatar_factory.hair_studio.ui.component_item import ComponentItem
from cgame_avatar_factory.hair_studio.ui.component_operations import ComponentOperations
from cgame_avatar_factory.hair_studio.utils.icon_utils import set_button_icon_with_fallback


class ComponentList(QtWidgets.QWidget):
    """Component List Widget.

    This widget displays a list of hair components that have been added to the scene.
    Users can select components to edit their properties in the editor area.
    """

    # Signal emitted when a component is selected
    component_selected = QtCore.Signal(dict)

    # Signal emitted when a component is deleted
    component_deleted = QtCore.Signal(str)

    # Signal emitted when component is renamed
    component_renamed = QtCore.Signal(str, str)  # component_id, new_name

    # Signal emitted when component visibility is toggled
    component_visibility_toggled = QtCore.Signal(str, bool)  # component_id, visible

    def __init__(self, hair_type, hair_manager=None, parent=None):
        """Initialize the ComponentList.

        Args:
            hair_type (str): Type of hair ('card', 'xgen', 'curve')
            hair_manager (HairManager, optional): The hair manager instance. If None, a new one will be created.
            parent (QWidget, optional): The parent widget. Defaults to None.
        """
        super(ComponentList, self).__init__(parent)
        self.hair_type = hair_type
        self.object_name = "{}ComponentList".format(hair_type.capitalize())
        self.setObjectName(self.object_name)

        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Initialize manager
        self.manager = hair_manager if hair_manager is not None else HairManager(parent)

        # Connect manager signals to handle component updates and errors
        self._connect_manager_signals()

        # Initialize component operations - let them use their own loggers
        self.operations = ComponentOperations(parent=self)
        # Connect operation signals
        self.operations.component_deleted.connect(self.component_deleted)
        self.operations.component_renamed.connect(self.component_renamed)
        self.operations.component_visibility_toggled.connect(
            self.component_visibility_toggled,
        )

        # Initialize context menu management - let them use their own loggers
        self.context_menu = ComponentContextMenu(parent=self)
        self.context_handler = ComponentItemContextHandler(
            self.context_menu,
        )
        # Connect context menu signals to existing operations
        self._connect_context_menu_signals()

        # Initialize state variables
        self._pending_selection_id = None  # For handling selection after component creation
        # Note: _preserve_selection_id is no longer needed due to incremental updates

        # Initialize UI
        self.setup_ui()

        # Enable drop operations and setup interactions
        self.setAcceptDrops(True)
        self._setup_interactions()

    def setup_ui(self):
        """Set up the user interface components."""
        # Main layout
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(
            LAYOUT_SPACING_MEDIUM,
            LAYOUT_SPACING_MEDIUM,
            LAYOUT_SPACING_MEDIUM,
            LAYOUT_SPACING_MEDIUM,
        )
        main_layout.setSpacing(LAYOUT_SPACING_SMALL)

        # Title (subtitle style) - ensure consistent height with asset library
        title = MLabel(UI_TEXT_HAIR_COMPONENT_LIST)
        title.setProperty("h2", True)
        title.setStyleSheet(SUBTITLE_STYLE)
        title.setAlignment(QtCore.Qt.AlignCenter)
        title.setSizePolicy(
            QtWidgets.QSizePolicy.Expanding,
            QtWidgets.QSizePolicy.Fixed,
        )
        main_layout.addWidget(title)

        # Add separator to match other panels
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        main_layout.addWidget(separator)

        # Component list
        self.component_list = MListView()
        self.component_list.setObjectName(
            OBJECT_NAME_COMPONENT_LIST_FORMAT.format(self.hair_type),
        )

        # Set up model for MListView
        self.component_model = QStandardItemModel()
        self.component_list.setModel(self.component_model)

        # Configure list view for consistent item display
        self.component_list.setUniformItemSizes(True)
        self.component_list.setVerticalScrollMode(
            QtWidgets.QAbstractItemView.ScrollPerPixel,
        )

        # Set default item size to match ComponentItem (will be updated dynamically)
        self._update_item_size()

        # Connect to parent window resize events for responsive sizing
        self.parent_window = None

        # Note: ComponentItem widgets handle their own click events through event filters
        # This prevents double signal emission and ensures consistent behavior
        main_layout.addWidget(self.component_list)

        # Buttons - unified bottom area with consistent height
        button_layout = MFlowLayout()

        # Set consistent height and padding for bottom area
        button_layout.setContentsMargins(
            BOTTOM_AREA_PADDING_HORIZONTAL,
            BUTTON_AREA_MARGIN,
            BOTTOM_AREA_PADDING_HORIZONTAL,
            BUTTON_AREA_MARGIN,
        )
        button_layout.setSpacing(BOTTOM_AREA_SPACING)

        self.add_button = MToolButton()
        set_button_icon_with_fallback(self.add_button, ICON_ADD_LINE)
        self.add_button.clicked.connect(self._on_add_component)
        self.add_button.setFixedSize(BUTTON_AREA_BUTTON_SIZE, BUTTON_AREA_BUTTON_SIZE)
        button_layout.addWidget(self.add_button)

        self.remove_button = MToolButton()
        set_button_icon_with_fallback(self.remove_button, ICON_TRASH_LINE)
        self.remove_button.clicked.connect(self._on_remove_component)
        self.remove_button.setFixedSize(
            BUTTON_AREA_BUTTON_SIZE,
            BUTTON_AREA_BUTTON_SIZE,
        )
        button_layout.addWidget(self.remove_button)

        # Create container widget for button layout with fixed height
        button_container = QtWidgets.QWidget()
        button_container.setFixedHeight(BUTTON_AREA_HEIGHT)
        button_container.setLayout(button_layout)

        main_layout.addWidget(button_container)

    def _setup_interactions(self):
        """Set up interactions for the component list."""
        # Enable context menu
        self.component_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.component_list.customContextMenuRequested.connect(self._show_context_menu)

        # Set single selection mode only
        self.component_list.setSelectionMode(
            QtWidgets.QAbstractItemView.SingleSelection,
        )

        # Install event filter for keyboard shortcuts
        self.component_list.installEventFilter(self)

    def _connect_manager_signals(self):
        """Connect signals from the hair manager to handle component updates."""
        try:
            # Connect to manager's components_updated signal to refresh UI (full rebuild)
            self.manager.components_updated.connect(self.update_components)

            # Connect to manager's component_visibility_changed signal for incremental updates
            self.manager.component_visibility_changed.connect(
                self._on_component_visibility_changed,
            )

            # Connect to manager's component_selected signal to sync selection
            self.manager.component_selected.connect(self._on_manager_component_selected)

            # Connect to manager's operation_failed signal to show error messages
            self.manager.operation_failed.connect(self._on_operation_failed)

            self._logger.debug("Manager signals connected successfully")

        except Exception as e:
            self._logger.error("Failed to connect manager signals: %s", str(e))

    def _connect_context_menu_signals(self):
        """Connect context menu signals to existing operations."""
        try:
            # Connect context menu signals to existing operation methods
            self.context_menu.component_rename_requested.connect(
                self._rename_component_by_id,
            )
            self.context_menu.component_duplicate_requested.connect(
                self._duplicate_component_by_id,
            )
            self.context_menu.component_delete_requested.connect(
                self._delete_component_by_id,
            )
            self.context_menu.component_visibility_toggle_requested.connect(
                self._toggle_component_visibility_by_id,
            )
            self.context_menu.component_add_requested.connect(self._on_add_component)
            self.context_menu.selection_clear_requested.connect(self.clear_selection)

            self._logger.debug("Connected context menu signals")

        except Exception as e:
            self._logger.error("Failed to connect context menu signals: %s", str(e))

    def _on_manager_component_selected(self, component_data):
        """Handle component selection from manager.

        Args:
            component_data (dict or None): Selected component data or None for no selection
        """
        # IMPORTANT: Do NOT forward the signal to prevent recursion!
        # The manager's component_selected signal is already connected directly to BaseHairTab
        # Forwarding it here would create a loop: Manager -> ComponentList -> BaseHairTab -> Manager

        # Only update UI selection if needed
        if component_data:
            component_id = component_data.get("id")
            self._select_component_in_ui(component_id)

    def _on_operation_failed(self, error_message):
        """Handle operation failure from manager.

        Args:
            error_message (str): Error message to display
        """
        self._logger.error("Manager operation failed: %s", error_message)
        # You could show a message box or status bar message here
        # For now, just log the error

    def _on_component_visibility_changed(self, component_id, is_visible):
        """Handle incremental component visibility change from manager.

        This method updates only the specific component's visibility without
        rebuilding the entire component list, preserving selection and other UI states.

        Args:
            component_id (str): ID of the component whose visibility changed
            is_visible (bool): New visibility state
        """
        visibility_state = "visible" if is_visible else "hidden"
        self._logger.debug(
            "Incremental visibility update: %s -> %s",
            component_id,
            visibility_state,
        )

        # Find and update the specific ComponentItem widget
        model = self.component_list.model()
        for row in range(model.rowCount()):
            index = model.index(row, 0)
            component_data = model.data(index, QtCore.Qt.UserRole)

            if component_data and component_data.get("id") == component_id:
                # Update the data model
                component_data["is_viewed"] = is_visible
                model.setData(index, component_data, QtCore.Qt.UserRole)

                # Update the ComponentItem widget directly
                component_widget = self.component_list.indexWidget(index)
                if component_widget and isinstance(component_widget, ComponentItem):
                    # Update the component data in the widget
                    component_widget.component_data["is_viewed"] = is_visible
                    # Refresh the visibility button display
                    component_widget._update_visibility_button()

                    self._logger.debug(
                        "Updated ComponentItem widget for %s",
                        component_id,
                    )
                break

        # Emit our own signal for any external listeners (maintaining compatibility)
        self.component_visibility_toggled.emit(component_id, is_visible)

    # ============================================================================
    # Component Management - Core CRUD Operations
    # ============================================================================

    def add_component(self, asset_data):
        """Add a new component to the list from asset data.

        This method handles both direct calls and drag-and-drop operations.
        It calls the manager's add_component_from_asset method which will:
        1. Call Maya API to create the hair node
        2. Update internal component state
        3. Emit signals to update UI

        Args:
            asset_data (dict): Dictionary containing asset data with 'id' and optional 'asset_type'
        """
        if not asset_data:
            self._logger.warning("Cannot add component: asset_data is empty")
            return

        # Validate asset type if provided
        asset_type = asset_data.get("asset_type", "")
        if asset_type and asset_type != self.hair_type:
            self._logger.warning(
                "Asset type '%s' does not match current hair type '%s'",
                asset_type,
                self.hair_type,
            )
            # Still allow the operation but log the mismatch

        # Use the new add_component_from_asset method which integrates with Maya API
        asset_id = asset_data.get("id")
        if asset_id:
            self._logger.info("Adding component from asset: %s", asset_id)
            self.manager.add_component_from_asset(asset_id)
        else:
            self._logger.warning("Cannot add component: asset_data missing 'id' field")

    def clear_components(self):
        """Clear all components from the list."""
        self.component_model.clear()
        self.component_selected.emit(None)

    def update_components(self, components):
        """Update the component list with the provided components.

        Args:
            components (list): List of component dictionaries to display
        """
        # Clear existing components
        self.component_model.clear()

        # Add each component to the list
        for component in components:
            try:
                # Create QStandardItem as data container
                item = self._create_component_item(component)
                self.component_model.appendRow(item)

                # Create ComponentItem widget
                component_item_widget = ComponentItem(component, parent=self)

                # Connect signals - use unified click handler
                component_item_widget.clicked.connect(self._on_component_clicked)
                component_item_widget.visibility_toggled.connect(
                    self._on_component_visibility_toggled,
                )

                # Install context menu handler for right-click support
                self.context_handler.install_context_handler(component_item_widget)

                # Note: Size policy and height are set in ComponentItem.__init__()
                # No need to override them here

                # Get the index and set the widget
                index = self.component_model.indexFromItem(item)
                self.component_list.setIndexWidget(index, component_item_widget)

            except Exception as e:
                self._logger.warning("Could not add component item: %s", str(e))

        # Handle pending selection (from add_component)
        if self._pending_selection_id:
            self.select_component(self._pending_selection_id)
            self._pending_selection_id = None

        # Note: No longer need to handle preserved selection due to incremental updates

        # Force UI update
        self.component_list.update()
        self.component_list.repaint()

    def _create_component_item(self, component):
        """Create a list item for the given component.

        Args:
            component (dict): The component data

        Returns:
            QStandardItem: The created list item for MListView
        """
        # Create QStandardItem as data container only (no text display)
        # The ComponentItem widget will handle all visual display
        item = QStandardItem()
        item.setData(component, QtCore.Qt.UserRole)

        # Set item to be non-editable
        item.setFlags(item.flags() & ~QtCore.Qt.ItemIsEditable)

        # Set size hint to match ComponentItem height + margins
        item_height = COMPONENT_ITEM_HEIGHT + COMPONENT_LIST_ITEM_MARGIN
        item.setSizeHint(QtCore.QSize(-1, item_height))

        return item

    # ============================================================================
    # Component Selection and Interaction
    # ============================================================================

    def _on_component_clicked(self, component_data):
        """Handle component item click event from ComponentItem widget.

        This method now only handles clicks from ComponentItem widgets, providing
        consistent selection behavior across the entire component item area.

        Args:
            component_data (dict): The component data from the clicked ComponentItem
        """
        if component_data:
            # Update UI selection to sync with the clicked component
            component_id = component_data.get("id")
            if component_id:
                self._select_component_in_ui(component_id)

            # Emit selection signal
            self.component_selected.emit(component_data)
            self._logger.debug(
                "Component selected: %s",
                component_data.get("name", "Unknown"),
            )

    def _on_component_visibility_toggled(self, component_id, is_visible):
        """Handle component visibility toggle from ComponentItem widgets.

        Args:
            component_id (str): The component ID
            is_visible (bool): The new visibility state
        """
        # Use manager's set_component_visibility method
        visibility_state = "visible" if is_visible else "hidden"
        self._logger.info(
            "Setting component visibility: %s -> %s",
            component_id,
            visibility_state,
        )

        self.manager.set_component_visibility(component_id, is_visible)

        # Note: We don't emit our own signal here anymore as the manager's
        # component_visibility_changed signal will trigger _on_component_visibility_changed

    def _select_component_in_ui(self, component_id):
        """Select a component in the UI by its ID.

        Args:
            component_id (str): The component ID to select
        """
        if not component_id:
            self.component_list.clearSelection()
            # Clear selection state from all ComponentItem widgets
            self._update_all_component_selection_states(None)
            return

        # Find the component in the model and select it
        model = self.component_list.model()
        for row in range(model.rowCount()):
            index = model.index(row, 0)
            component_data = model.data(index, QtCore.Qt.UserRole)
            if component_data and component_data.get("id") == component_id:
                # Select the item in the list view
                selection_model = self.component_list.selectionModel()
                if selection_model:
                    selection_model.select(
                        index,
                        QtCore.QItemSelectionModel.ClearAndSelect,
                    )
                    # Scroll to the selected item
                    self.component_list.scrollTo(index)

                # Update ComponentItem widget selection states
                self._update_all_component_selection_states(component_id)
                break

    def _update_all_component_selection_states(self, selected_component_id):
        """Update selection state for all ComponentItem widgets.

        Args:
            selected_component_id (str or None): ID of the selected component, or None to clear all
        """
        model = self.component_list.model()
        for row in range(model.rowCount()):
            index = model.index(row, 0)
            component_widget = self.component_list.indexWidget(index)
            if component_widget and isinstance(component_widget, ComponentItem):
                component_data = component_widget.get_component_data()
                component_id = component_data.get("id")

                # Set selection state based on whether this is the selected component
                is_selected = (component_id == selected_component_id) if selected_component_id else False
                component_widget.set_selected(is_selected)

    # ============================================================================
    # Button Handlers and UI Actions
    # ============================================================================

    def _on_add_component(self):
        """Handle add component button click."""
        # This will be implemented to show the asset browser

    def _on_remove_component(self):
        """Handle remove component button click."""
        # Use model-based approach for MListView
        selection_model = self.component_list.selectionModel()
        if selection_model:
            current_index = selection_model.currentIndex()
            if current_index.isValid():
                self._delete_component_by_index(current_index)

    # ============================================================================
    # Drag and Drop Operations
    # ============================================================================

    def clear_selection(self):
        """Clear the current selection in the component list."""
        # Clear selection using MListView API
        self.component_list.clearSelection()
        # Emit signal to clear editor
        self.component_selected.emit(None)

    def select_component(self, component_id):
        """Select a component by its ID.

        Args:
            component_id (str): The ID of the component to select
        """
        try:
            # Find the component in the model
            for row in range(self.component_model.rowCount()):
                index = self.component_model.index(row, 0)
                item = self.component_model.itemFromIndex(index)
                if item:
                    component_data = item.data(QtCore.Qt.UserRole)
                    if component_data and component_data.get("id") == component_id:
                        self.component_list.setCurrentIndex(index)
                        self.component_selected.emit(component_data)
                        return
        except Exception as e:
            self._logger.warning(
                "Could not select component %s: %s",
                component_id,
                str(e),
            )

    def dragEnterEvent(self, event):
        """Handle drag enter events."""
        if self._can_accept_drop(event):
            event.acceptProposedAction()
            self._set_drop_visual_feedback(True)
        else:
            event.ignore()

    def dragMoveEvent(self, event):
        """Handle drag move events."""
        if self._can_accept_drop(event):
            event.acceptProposedAction()
        else:
            event.ignore()

    def dragLeaveEvent(self, event):
        """Handle drag leave events."""
        self._set_drop_visual_feedback(False)
        event.accept()

    def dropEvent(self, event):
        """Handle drop events."""
        self._set_drop_visual_feedback(False)

        if not self._can_accept_drop(event):
            event.ignore()
            return

        try:
            # Get asset data from the drop
            asset_data = self._extract_asset_data(event)
            if asset_data:
                # Add component from the dropped asset using unified method
                self.add_component(asset_data)
                event.acceptProposedAction()
            else:
                event.ignore()

        except Exception as e:
            self._logger.error("Error handling drop event: %s", str(e))
            event.ignore()

    def _can_accept_drop(self, event):
        """Check if the drop event can be accepted.

        Args:
            event: The drop event

        Returns:
            bool: True if the drop can be accepted, False otherwise
        """
        # Check if the event has the correct MIME type
        mime_data = event.mimeData()

        # Accept custom hair asset MIME type or plain text
        if mime_data.hasFormat("application/x-hair-asset") or mime_data.hasText():
            return True

        return False

    def _extract_asset_data(self, event):
        """Extract asset data from the drop event.

        Args:
            event: The drop event

        Returns:
            dict or None: Asset data if extraction successful, None otherwise
        """
        mime_data = event.mimeData()

        try:
            # Try custom MIME type first
            if mime_data.hasFormat("application/x-hair-asset"):
                data = mime_data.data("application/x-hair-asset")
                asset_json = data.data().decode("utf-8")
                return json.loads(asset_json)

            # Fallback to plain text
            elif mime_data.hasText():
                asset_json = mime_data.text()
                return json.loads(asset_json)

        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            self._logger.warning("Failed to parse dropped asset data: %s", str(e))

        return None

    def _set_drop_visual_feedback(self, is_active):
        """Set visual feedback for drop operations.

        Args:
            is_active (bool): True to show drop feedback, False to hide
        """
        if is_active:
            # Add visual feedback for drop target
            self.setStyleSheet(
                f"""
                ComponentList {{
                    border: {BORDER_WIDTH_MEDIUM}px dashed #3E3E3E;
                    background-color: rgba(255, 255, 255, 0.05);
                }}
            """
            )
        else:
            # Remove visual feedback
            self.setStyleSheet("")

    def _show_context_menu(self, position):
        """Show context menu for component list using the centralized menu manager.

        Args:
            position (QtCore.QPoint): Position where the context menu was requested
        """
        try:
            # Get the index at the position for MListView
            index = self.component_list.indexAt(position)
            component_data = None

            # Get item data if index is valid
            if index.isValid():
                component_data = self.component_model.data(index, QtCore.Qt.UserRole)

            # Check if we have any items in the list
            has_items = self.component_model.rowCount() > 0

            # Use the centralized context menu manager
            self.context_menu.show_context_menu(position, component_data, has_items)

        except Exception as e:
            self._logger.error(f"Failed to show context menu: {e}")

    # ============================================================================
    # Component Operations - Rename, Duplicate, Delete, Visibility
    # ============================================================================

    # ID-based operations (used by context menu)
    def _rename_component_by_id(self, component_id):
        """Rename a component by its ID.

        Args:
            component_id (str): The ID of the component to rename
        """
        index = self._find_component_index_by_id(component_id)
        if index.isValid():
            self._rename_component_by_index(index)

    def _duplicate_component_by_id(self, component_id):
        """Duplicate a component by its ID.

        Args:
            component_id (str): The ID of the component to duplicate
        """
        index = self._find_component_index_by_id(component_id)
        if index.isValid():
            self._duplicate_component_by_index(index)

    def _delete_component_by_id(self, component_id):
        """Delete a component by its ID.

        Args:
            component_id (str): The ID of the component to delete
        """
        index = self._find_component_index_by_id(component_id)
        if index.isValid():
            self._delete_component_by_index(index)

    def _toggle_component_visibility_by_id(self, component_id, new_visibility):
        """Toggle component visibility by its ID.

        Args:
            component_id (str): The ID of the component
            new_visibility (bool): The new visibility state
        """
        index = self._find_component_index_by_id(component_id)
        if index.isValid():
            # Update the component data directly
            model = self.component_list.model()
            component_data = model.data(index, QtCore.Qt.UserRole)
            if component_data:
                component_data["visible"] = new_visibility
                model.setData(index, component_data, QtCore.Qt.UserRole)

    def _find_component_index_by_id(self, component_id):
        """Find the model index for a component by its ID.

        Args:
            component_id (str): The component ID to search for

        Returns:
            QtCore.QModelIndex: The index if found, invalid index otherwise
        """
        model = self.component_list.model()
        for row in range(model.rowCount()):
            index = model.index(row, 0)
            component_data = model.data(index, QtCore.Qt.UserRole)
            if component_data and component_data.get("id") == component_id:
                return index
        return QtCore.QModelIndex()  # Invalid index

    # Index-based operations (existing methods)

    def _rename_component_by_index(self, index):
        """Rename a component by its index.

        Args:
            index (QtCore.QModelIndex): The index of the component to rename
        """
        if not index.isValid():
            return

        model = self.component_list.model()
        if not model:
            return

        component_data = model.data(index, QtCore.Qt.UserRole)
        if not component_data:
            return

        # Use operations class to handle rename
        new_name = self.operations.rename_component(component_data)
        if new_name:
            # Update the component data in model
            component_data["name"] = new_name
            model.setData(index, component_data, QtCore.Qt.UserRole)

    def _duplicate_component_by_index(self, index):
        """Duplicate a component by its index.

        Args:
            index (QtCore.QModelIndex): The index of the component to duplicate
        """
        if not index.isValid():
            return

        model = self.component_list.model()
        if not model:
            return

        component_data = model.data(index, QtCore.Qt.UserRole)
        if not component_data:
            return

        # Use operations class to handle duplication
        duplicated_data = self.operations.duplicate_component(component_data)
        if duplicated_data:
            # Add the duplicated component to the list
            self.add_component(
                {
                    "id": duplicated_data.get("asset_id", ""),
                    "name": duplicated_data["name"],
                },
            )

    def _toggle_component_visibility_by_index(self, index):
        """Toggle component visibility by its index.

        Args:
            index (QtCore.QModelIndex): The index of the component to toggle visibility for
        """
        if not index.isValid():
            return

        model = self.component_list.model()
        if not model:
            return

        component_data = model.data(index, QtCore.Qt.UserRole)
        if not component_data:
            return

        # Use operations class to handle visibility toggle
        new_visibility = self.operations.toggle_visibility(component_data)
        if new_visibility is not None:
            # Update model data
            component_data["visible"] = new_visibility
            model.setData(index, component_data, QtCore.Qt.UserRole)

    def _delete_component_by_index(self, index):
        """Delete a component by its index with confirmation.

        Args:
            index (QtCore.QModelIndex): The index of the component to delete
        """
        if not index.isValid():
            return

        model = self.component_list.model()
        if not model:
            return

        component_data = model.data(index, QtCore.Qt.UserRole)
        if not component_data:
            return

        component_id = component_data.get("id")
        component_name = component_data.get("name", "Unknown")

        if not component_id:
            self._logger.warning("Cannot delete component: missing ID")
            return

        # Show confirmation dialog
        reply = QtWidgets.QMessageBox.question(
            self,
            "Delete Component",
            "Are you sure you want to delete '{}'?".format(component_name),
            QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
            QtWidgets.QMessageBox.No,
        )

        if reply == QtWidgets.QMessageBox.Yes:
            # Use manager's delete_component method which will:
            # 1. Call maya_api to delete the Maya node
            # 2. Remove from internal component list
            # 3. Emit components_updated signal to refresh UI
            self._logger.info(
                "Deleting component: %s (ID: %s)",
                component_name,
                component_id,
            )
            self.manager.delete_component(component_id)

    # ============================================================================
    # Selection Management and Event Handling
    # ============================================================================

    def eventFilter(self, obj, event):
        """Event filter for keyboard shortcuts.

        Args:
            obj: The object that received the event
            event: The event

        Returns:
            bool: True if event was handled, False otherwise
        """
        if obj == self.component_list and event.type() == QtCore.QEvent.KeyPress:
            key = event.key()
            modifiers = event.modifiers()

            # Delete key - delete selected component
            if key == Qt.Key_Delete:
                self._delete_selected_component()
                return True

            # F2 key - rename selected component
            elif key == Qt.Key_F2:
                self._rename_selected_component()
                return True

            # Ctrl+D - duplicate selected component
            elif key == Qt.Key_D and modifiers == Qt.ControlModifier:
                self._duplicate_selected_component()
                return True

            # Space - toggle visibility
            elif key == Qt.Key_Space:
                self._toggle_selected_component_visibility()
                return True

        return super(ComponentList, self).eventFilter(obj, event)

    def _execute_on_selected(self, operation_func):
        """Execute an operation on the currently selected component.

        Args:
            operation_func: Function to call with the selected index
        """
        selection_model = self.component_list.selectionModel()
        if selection_model:
            current_index = selection_model.currentIndex()
            if current_index.isValid():
                operation_func(current_index)

    def _delete_selected_component(self):
        """Delete the currently selected component."""
        self._execute_on_selected(self._delete_component_by_index)

    def _rename_selected_component(self):
        """Rename the currently selected component."""
        self._execute_on_selected(self._rename_component_by_index)

    def _duplicate_selected_component(self):
        """Duplicate the currently selected component."""
        self._execute_on_selected(self._duplicate_component_by_index)

    def _toggle_selected_component_visibility(self):
        """Toggle visibility of the currently selected component."""
        self._execute_on_selected(self._toggle_component_visibility_by_index)

    def get_selected_component(self):
        """Get the currently selected component (single selection).

        Returns:
            dict: The selected component data, or None if no selection
        """
        # Get current selection using model-based approach
        selection_model = self.component_list.selectionModel()
        if selection_model:
            current_index = selection_model.currentIndex()
            if current_index.isValid():
                model = self.component_list.model()
                if model:
                    return model.data(current_index, QtCore.Qt.UserRole)
        return None

    def _update_item_size(self):
        """Set component item size to match ComponentItem fixed height."""
        try:
            # Use fixed height matching ComponentItem's COMPONENT_ITEM_HEIGHT
            # Add small margin for visual spacing between items
            grid_height = COMPONENT_ITEM_HEIGHT + COMPONENT_LIST_ITEM_MARGIN

            # Set grid size with fixed height
            self.component_list.setGridSize(QtCore.QSize(-1, grid_height))

            self._logger.debug(
                f"Set component item grid size to {grid_height}px (ComponentItem: {COMPONENT_ITEM_HEIGHT}px +"
                f" {COMPONENT_LIST_ITEM_MARGIN}px margin)",
            )

        except Exception as e:
            self._logger.warning(f"Failed to update item size: {e}")
            # Fallback to default size matching ComponentItem height
            self.component_list.setGridSize(
                QtCore.QSize(-1, COMPONENT_ITEM_HEIGHT + COMPONENT_LIST_ITEM_MARGIN),
            )

    def resizeEvent(self, event):
        """Handle resize events to update item sizes."""
        super().resizeEvent(event)
        self._update_item_size()
