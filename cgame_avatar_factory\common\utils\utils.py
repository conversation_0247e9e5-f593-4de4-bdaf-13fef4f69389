# Import built-in modules
import ctypes
from datetime import datetime
import json
import logging
import os
import re
import string

# Import third-party modules
from lightbox_paths.paths import normalize
from qtpy import QtCore


def path_normalize_decorator(func):
    """
    A decorator that wraps the result of a function with a path normalize function.

    Args:
        func (callable): The function to be decorated.

    Returns:
        callable: The decorated function that wraps the original function.
    """

    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        return normalize(result)

    return wrapper


def path_available(path):
    """Return if path is available."""
    return os.access(path, os.F_OK)


def path_exists(path):
    """Return if path exists."""
    return os.path.exists(path)


def get_file_name(file_path):
    """Return file name by file path."""
    return os.path.basename(file_path)


@path_normalize_decorator
def join_path(path, file_name):
    return os.path.join(path, file_name)


@path_normalize_decorator
def remove_extension(file_path):
    return os.path.splitext(file_path)[0]


@path_normalize_decorator
def get_file_directory(file_path):
    return os.path.dirname(file_path)


@path_normalize_decorator
def join_extension(file_path, extension):
    return normalize("{}{}".format(file_path, extension))


def join_extensions(file_path, extensions):
    paths = []
    for extension in extensions:
        paths.append(join_extension(file_path, extension))
    return paths


def generate_file_filter(extension_list, filter_name="supported files"):
    extensions = " ".join("*{}".format(ext) for ext in extension_list)
    return "{}({})".format(filter_name, extensions)


def validate_file_extensions(file_path, file_extension):
    """Return file extensions."""
    file_name = get_file_name(file_path)
    return file_name.endswith(file_extension)


def get_file_extension(file_path):
    """Return file extension with dot."""
    return os.path.splitext(file_path)[1].lower()


def widget_center_pos(widget):
    """Return widget's center position."""
    return widget.width() / 2.0, widget.height() / 2.0


def crop_pixmap_to_square(pixmap, resolution):
    """Crop pixmap to square."""
    # Scale pixmap, so it's short side match the resolution
    if not pixmap:
        return None

    width = pixmap.width()
    height = pixmap.height()

    if width == 0 or height == 0:
        return None

    scale_ratio = resolution / min(width, height)
    scaled_pixmap = pixmap.scaled(
        int(width * scale_ratio),
        int(height * scale_ratio),
        aspectRatioMode=QtCore.Qt.KeepAspectRatio,
    )

    # Crop the center of the scaled pixmap
    width = scaled_pixmap.width()
    height = scaled_pixmap.height()
    x = (width - resolution) / 2.0
    y = (height - resolution) / 2.0
    cropped_pixmap = scaled_pixmap.copy(QtCore.QRect(x, y, resolution, resolution))
    return cropped_pixmap


def append_current_time(input_str: str, time_format=None, join_format=None):
    """Return input_str with current time appended to it."""
    time_format = time_format or "%Y%m%d%H%M%S"
    join_format = join_format or "{}_{}"
    current_time = datetime.now().strftime(time_format)
    return join_format.format(input_str, current_time)


def get_file_path_with_time(file_dir, file_name, file_extension):
    """Return file path with current time appended to file name."""
    file_name = append_current_time(file_name, join_format="_{}_")

    # Double-check dot in file extension
    file_extension = file_extension.replace(".", "")

    file_path = os.path.join(file_dir, "{}.{}".format(file_name, file_extension))
    return file_path


def get_number_after_pattern(input_str, pattern):
    """Return number after pattern."""
    match = re.search(r"{}(\d+)".format(pattern), input_str)
    return int(match.group(1)) if match else None


def read_json(path: str, encoding_type: str = "utf-8"):
    """Read and parse JSON file

    Args:
        path: Path to JSON file

    Returns:
        dict: Parsed JSON data
    """
    try:
        with open(path, "r", encoding=encoding_type) as f:
            return json.load(f)
    except FileNotFoundError as e:
        logging.error(f"文件不存在: {path}")
        raise e
    except json.JSONDecodeError as e:
        logging.error(f"JSON 格式错误: {path}, 错误: {e}")
        raise e


def get_volume_label(drive_letter: str):
    """
    Get the volume label of the specified drive letter.

    Args:
        drive_letter (str): Drive letter, e.g. 'C:'

    Returns:
        str | None: Volume label or None if not found.
    """
    buf = ctypes.create_unicode_buffer(1024)
    fs_name = ctypes.create_unicode_buffer(1024)
    serial_number = ctypes.c_ulong()
    max_component_length = ctypes.c_ulong()
    file_system_flags = ctypes.c_ulong()
    rc = ctypes.windll.kernel32.GetVolumeInformationW(
        ctypes.c_wchar_p(drive_letter + "\\"),
        buf,
        ctypes.sizeof(buf),
        ctypes.byref(serial_number),
        ctypes.byref(max_component_length),
        ctypes.byref(file_system_flags),
        fs_name,
        ctypes.sizeof(fs_name),
    )
    if rc:
        return buf.value
    else:
        return None


def get_drive_letter_by_label(label_name: str):
    """
    Get the drive letter by volume label.

    Args:
        label_name (str): Volume label.

    Returns:
        str | None: Drive letter (e.g. 'D:') or None if not found.
    """
    for letter in string.ascii_uppercase:
        drive = letter + ":"
        label = get_volume_label(drive)
        if label and label == label_name:
            return drive
    return None


def change_drive_letter(path: str, new_drive_letter: str):
    """
    Change the drive letter of a path, unify separators to '/', and ensure trailing '/'.

    Args:
        path (str): Original path, e.g. 'D:\\folder\\file.txt' or 'D:\\folder'
        new_drive_letter (str): New drive letter, e.g. 'E:' or 'E'

    Returns:
        str: New path string, with '/' as separator and ending with '/'
    """
    path = os.path.abspath(path)
    if path.startswith("\\\\"):
        # For UNC path, just replace separator
        new_path = path.replace(os.sep, "/")
        if not new_path.endswith("/"):
            new_path += "/"
        return new_path
    drive, tail = os.path.splitdrive(path)
    if not drive:
        raise ValueError(f"Path does not contain drive letter: {path}")
    if not new_drive_letter.endswith(":"):
        new_drive_letter += ":"
    if not tail.startswith(os.sep):
        tail = os.sep + tail
    new_path = new_drive_letter + tail
    new_path = new_path.replace(os.sep, "/")
    if not new_path.endswith("/"):
        new_path += "/"
    return new_path


def get_first_unused_drive_letter():
    """
    Get the first unused drive letter (e.g. 'F:').

    Returns:
        str | None: Unused drive letter or None if all are used.
    """
    used = set()
    for letter in string.ascii_uppercase:
        drive = letter + ":\\"
        if os.path.exists(drive):
            used.add(letter)
    for letter in string.ascii_uppercase:
        if letter not in used:
            return letter + ":"
    return None


def change_to_unused_drive_letter_if_needed(label_name: str, path: str):
    """
    Given a volume label and current path, if the drive letter matches, return the original path.
    If not, change to the first unused drive letter.

    Args:
        label_name (str): Target volume label.
        path (str): Current path.

    Returns:
        str: New path string.
    """
    path = os.path.abspath(path)
    if path.startswith("\\\\"):
        return path
    drive, _ = os.path.splitdrive(path)
    if not drive:
        raise ValueError(f"Path does not contain drive letter: {path}")
    current_drive = drive.upper()
    target_drive = get_drive_letter_by_label(label_name)
    if target_drive is None:
        return path
    if current_drive == target_drive.upper():
        return path
    if target_drive:
        logging.info(f"change {path} to {target_drive}")
        return change_drive_letter(path, target_drive)
    else:
        raise RuntimeError("No available unused drive letter.")
