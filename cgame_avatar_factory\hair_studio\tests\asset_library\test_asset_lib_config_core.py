"""Core Unit Tests for AssetLibConfig System.

Tests the core functionality of the asset library configuration system,
using mock data instead of real data to ensure reliable testing.
"""

# Import built-in modules
import os
import sys
import unittest
from unittest.mock import patch

# Add the project root to Python path for testing
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


class TestAssetLibConfigCore(unittest.TestCase):
    """Core tests for AssetLibConfig functionality."""

    def setUp(self):
        """Set up test environment with mock data."""
        # Mock constants data
        self.mock_tab_map = {
            "eyebrow": {
                "show_text": "眉毛",
                "lib_path": [],
            },
            "hair": {
                "show_text": "头发",
                "lib_path": [],
            },
            "beard": {
                "show_text": "胡子",
                "lib_path": [],
            },
        }

        self.mock_asset_dirs = [
            "/mock/path/public",
            "/mock/path/project",
            "/mock/path/local",
        ]

    @patch("cgame_avatar_factory.hair_studio.constants.HAIR_ASSET_LIB_TAB_MAP")
    @patch("cgame_avatar_factory.hair_studio.constants.ALL_HAIR_ASSET_DIR")
    def test_config_initialization(self, mock_asset_dirs, mock_tab_map):
        """Test AssetLibConfig initialization with mock data."""
        # Setup mocks
        mock_tab_map.copy.return_value = self.mock_tab_map.copy()
        mock_asset_dirs.__iter__.return_value = iter(self.mock_asset_dirs)

        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import AssetLibConfig

        config = AssetLibConfig()

        # Test basic properties
        self.assertGreater(config.get_tab_count(), 0)
        self.assertIsInstance(config.get_tab_keys(), list)
        self.assertIsNotNone(config.get_default_tab_key())

    @patch("cgame_avatar_factory.hair_studio.constants.HAIR_ASSET_LIB_TAB_MAP")
    @patch("cgame_avatar_factory.hair_studio.constants.ALL_HAIR_ASSET_DIR")
    def test_tab_configuration(self, mock_asset_dirs, mock_tab_map):
        """Test tab configuration management with mock data."""
        # Setup mocks
        mock_tab_map.copy.return_value = self.mock_tab_map.copy()
        mock_asset_dirs.__iter__.return_value = iter(self.mock_asset_dirs)

        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import AssetLibConfig

        config = AssetLibConfig()

        # Test tab keys
        tab_keys = config.get_tab_keys()
        expected_keys = ["eyebrow", "hair", "beard"]
        for key in expected_keys:
            self.assertIn(key, tab_keys)

        # Test display text
        self.assertEqual(config.get_tab_display_text("hair"), "头发")
        self.assertEqual(config.get_tab_display_text("eyebrow"), "眉毛")
        self.assertEqual(config.get_tab_display_text("beard"), "胡子")

        # Test invalid key
        self.assertEqual(config.get_tab_display_text("invalid"), "invalid")

    @patch("cgame_avatar_factory.hair_studio.constants.HAIR_ASSET_LIB_TAB_MAP")
    @patch("cgame_avatar_factory.hair_studio.constants.ALL_HAIR_ASSET_DIR")
    @patch("cgame_avatar_factory.hair_studio.constants.DEFAULT_SUB_TAB_KEY", "hair")
    def test_default_tab_logic(self, mock_asset_dirs, mock_tab_map):
        """Test default tab selection logic with mock data."""
        # Setup mocks
        mock_tab_map.copy.return_value = self.mock_tab_map.copy()
        mock_asset_dirs.__iter__.return_value = iter(self.mock_asset_dirs)

        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import AssetLibConfig

        config = AssetLibConfig()

        # Test default tab
        default_key = config.get_default_tab_key()
        self.assertEqual(default_key, "hair")

        # Test default index
        default_index = config.get_default_tab_index()
        tab_keys = config.get_tab_keys()
        self.assertEqual(tab_keys[default_index], default_key)

    @patch("cgame_avatar_factory.hair_studio.constants.HAIR_ASSET_LIB_TAB_MAP")
    @patch("cgame_avatar_factory.hair_studio.constants.ALL_HAIR_ASSET_DIR")
    @patch("cgame_avatar_factory.hair_studio.constants.HAIR_TYPE_CARD", "card")
    def test_sub_tab_display_logic(self, mock_asset_dirs, mock_tab_map):
        """Test sub-tab display logic for different hair types with mock data."""
        # Setup mocks
        mock_tab_map.copy.return_value = self.mock_tab_map.copy()
        mock_asset_dirs.__iter__.return_value = iter(self.mock_asset_dirs)

        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import AssetLibConfig

        config = AssetLibConfig()

        # Test card type (should show sub-tabs)
        self.assertTrue(config.should_show_sub_tabs("card"))

        # Test other types (should not show sub-tabs)
        self.assertFalse(config.should_show_sub_tabs("xgen"))
        self.assertFalse(config.should_show_sub_tabs("curve"))
        self.assertFalse(config.should_show_sub_tabs("unknown"))

    @patch("cgame_avatar_factory.hair_studio.constants.HAIR_ASSET_LIB_TAB_MAP")
    @patch("cgame_avatar_factory.hair_studio.constants.ALL_HAIR_ASSET_DIR")
    @patch("os.path.exists")
    @patch("os.listdir")
    def test_tab_lib_paths(self, mock_listdir, mock_exists, mock_asset_dirs, mock_tab_map):
        """Test getting library paths for tabs with mock data."""
        # Setup mocks
        mock_tab_map.copy.return_value = self.mock_tab_map.copy()
        mock_asset_dirs.__iter__.return_value = iter(self.mock_asset_dirs)
        mock_exists.return_value = True
        mock_listdir.return_value = ["hair", "eyebrow", "beard"]

        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import AssetLibConfig

        config = AssetLibConfig()

        # Test getting paths for each tab
        for tab_key in config.get_tab_keys():
            paths = config.get_tab_lib_paths(tab_key)
            self.assertIsInstance(paths, list)
            # Paths can be empty if not configured, but should be a list

        # Test invalid tab key
        invalid_paths = config.get_tab_lib_paths("invalid_key")
        self.assertEqual(invalid_paths, [])

    @patch("cgame_avatar_factory.hair_studio.constants.HAIR_ASSET_LIB_TAB_MAP")
    @patch("cgame_avatar_factory.hair_studio.constants.ALL_HAIR_ASSET_DIR")
    @patch("os.path.exists")
    @patch("os.listdir")
    def test_all_asset_paths(self, mock_listdir, mock_exists, mock_asset_dirs, mock_tab_map):
        """Test getting all asset paths with mock data."""
        # Setup mocks
        mock_tab_map.copy.return_value = self.mock_tab_map.copy()
        mock_asset_dirs.__iter__.return_value = iter(self.mock_asset_dirs)
        mock_exists.return_value = True
        mock_listdir.return_value = ["hair", "eyebrow", "beard"]

        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import AssetLibConfig

        config = AssetLibConfig()

        # Test getting all paths
        all_paths = config.get_all_asset_paths()
        self.assertIsInstance(all_paths, list)

        # All paths should be strings
        for path in all_paths:
            self.assertIsInstance(path, str)

    @patch("cgame_avatar_factory.hair_studio.constants.HAIR_ASSET_LIB_TAB_MAP")
    @patch("cgame_avatar_factory.hair_studio.constants.ALL_HAIR_ASSET_DIR")
    def test_tab_index_mapping(self, mock_asset_dirs, mock_tab_map):
        """Test tab index and key mapping with mock data."""
        # Setup mocks
        mock_tab_map.copy.return_value = self.mock_tab_map.copy()
        mock_asset_dirs.__iter__.return_value = iter(self.mock_asset_dirs)

        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import AssetLibConfig

        config = AssetLibConfig()

        # Test key to index mapping
        for i, key in enumerate(config.get_tab_keys()):
            self.assertEqual(config.get_tab_index_by_key(key), i)
            self.assertEqual(config.get_tab_key_by_index(i), key)

        # Test invalid mappings
        self.assertEqual(config.get_tab_index_by_key("invalid"), -1)
        self.assertIsNone(config.get_tab_key_by_index(999))

    @patch("cgame_avatar_factory.hair_studio.constants.HAIR_ASSET_LIB_TAB_MAP")
    @patch("cgame_avatar_factory.hair_studio.constants.ALL_HAIR_ASSET_DIR")
    def test_config_summary(self, mock_asset_dirs, mock_tab_map):
        """Test configuration summary generation with mock data."""
        # Setup mocks
        mock_tab_map.copy.return_value = self.mock_tab_map.copy()
        mock_asset_dirs.__iter__.return_value = iter(self.mock_asset_dirs)

        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import AssetLibConfig

        config = AssetLibConfig()
        summary = config.get_config_summary()

        # Test required fields
        required_fields = [
            "total_tabs",
            "tab_keys",
            "default_tab",
            "default_index",
            "card_hair_type",
            "total_asset_paths",
            "paths_per_tab",
            "root_directories",
        ]

        for field in required_fields:
            self.assertIn(field, summary)

        # Test data types
        self.assertIsInstance(summary["total_tabs"], int)
        self.assertIsInstance(summary["tab_keys"], list)
        self.assertIsInstance(summary["paths_per_tab"], dict)

    @patch("cgame_avatar_factory.hair_studio.constants.HAIR_ASSET_LIB_TAB_MAP")
    @patch("cgame_avatar_factory.hair_studio.constants.ALL_HAIR_ASSET_DIR")
    @patch("cgame_avatar_factory.hair_studio.constants.DEFAULT_SUB_TAB_KEY", "hair")
    @patch("cgame_avatar_factory.hair_studio.constants.HAIR_TYPE_CARD", "card")
    def test_convenience_functions(self, mock_asset_dirs, mock_tab_map):
        """Test convenience functions with mock data."""
        # Setup mocks
        mock_tab_map.copy.return_value = self.mock_tab_map.copy()
        mock_asset_dirs.__iter__.return_value = iter(self.mock_asset_dirs)

        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import get_default_tab_key
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import get_tab_display_text
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import get_tab_map
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import should_show_sub_tabs

        # Test convenience functions work
        tab_map = get_tab_map()
        self.assertIsInstance(tab_map, dict)

        default_key = get_default_tab_key()
        self.assertEqual(default_key, "hair")

        self.assertTrue(should_show_sub_tabs("card"))
        self.assertFalse(should_show_sub_tabs("xgen"))

        self.assertEqual(get_tab_display_text("hair"), "头发")


def run_tests():
    """Run the test suite."""
    print("Testing AssetLibConfig Core Functionality...")

    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestAssetLibConfigCore)

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    if result.wasSuccessful():
        print(f"\n✅ All {result.testsRun} AssetLibConfig tests passed!")
    else:
        print(f"\n❌ {len(result.failures)} failures, {len(result.errors)} errors")

    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
