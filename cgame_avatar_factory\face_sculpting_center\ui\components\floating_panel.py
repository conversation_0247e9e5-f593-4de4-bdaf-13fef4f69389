# Import built-in modules
import logging

# Import third-party modules
from dayu_widgets import dayu_theme
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.common import constants as const
from cgame_avatar_factory.common import core
from cgame_avatar_factory.common.ui.layout import FramelessVLayout
from cgame_avatar_factory.common.ui.mixin.style_mixin import MStyleMixin


class FloatingPanel(QtWidgets.QWidget):
    """A floating panel widget that can be expanded and collapsed.

    This panel provides a toggle button to show/hide a content panel.
    When expanded, it displays the content panel to the left of the floating panel.

    Attributes:
        sig_expanded_changed: Signal emitted when expanded state changes
    """

    sig_expanded_changed = QtCore.Signal(bool)

    def __init__(self, parent=None):
        """Initialize the floating panel.

        Args:
            parent: Parent widget
        """
        super(FloatingPanel, self).__init__(parent)
        self.logger = logging.getLogger(__name__)

        self._expanded = False
        self._content_panel = None
        self._content_widget = None
        self._floating_panel_pos = None
        self._fade_in_animation = None
        self._fade_out_animation = None

        self._relative_x = 10
        self._relative_bottom = 340

        self.setMinimumHeight(180)

        self._setup_ui()

    def _setup_ui(self):
        self.main_layout = QtWidgets.QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(self.main_layout)

        self.toggle_button = QtWidgets.QPushButton("\n".join(const.OPEN_EDIT_MODE_LIST))
        self.toggle_button.clicked.connect(self.toggle_expanded)
        self.toggle_button.setFixedSize(40, 180)

        self.toolbar_container = QtWidgets.QWidget()
        self.toolbar_container.setStyleSheet(
            f"background-color: {const.TOOLBAR_BG}; border-radius: {dayu_theme.border_radius_base}px;",
        )
        self.toolbar_container.setFixedWidth(40)

        self.toolbar_layout = QtWidgets.QVBoxLayout()
        self.toolbar_layout.setContentsMargins(0, 5, 0, 5)
        self.toolbar_container.setLayout(self.toolbar_layout)
        self.toolbar_layout.addWidget(self.toggle_button)
        self.main_layout.addWidget(self.toolbar_container)

        self._content_panel = MStyleMixin.cls_wrapper(QtWidgets.QWidget)()
        self._content_panel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        self._content_panel.frameless().border_radius(8).background_color(const.DAYU_BG_IN_COLOR)

        shadow = QtWidgets.QGraphicsDropShadowEffect(self._content_panel)
        shadow.setBlurRadius(15)
        self._content_panel.setGraphicsEffect(shadow)

        self._content_layout = FramelessVLayout()
        self._content_layout.setContentsMargins(15, 10, 15, 15)
        self._content_panel.setLayout(self._content_layout)
        self._content_panel.hide()
        self.toggle_button.setEnabled(False)
        self.toggle_button.setToolTip("编辑模式当前不可用")
        self.toggle_button.setCursor(QtCore.Qt.ForbiddenCursor)

    def set_content_widget(self, widget):
        """Set the content widget to be displayed in the content panel.

        Args:
            widget: Widget to be displayed in the content panel
        """
        if self._content_widget:
            self._content_layout.removeWidget(self._content_widget)
            self._content_widget.setParent(None)

        self._content_widget = widget
        if widget:
            self._content_layout.addWidget(widget)

    def toggle_expanded(self):
        with core.get_reporter_instance() as api:
            api.report_count(event_name="main-call", action="edit mode function call", tool_name=const.PACKAGE_NAME)

        self._expanded = not self._expanded
        if self._expanded:
            self._expand_panel()
        else:
            self._collapse_panel()
        self.sig_expanded_changed.emit(self._expanded)

    def _expand_panel(self):
        parent = self.parentWidget()
        if not parent:
            self.logger.warning("无法获取父组件，取消展开面板")
            self._expanded = False
            return

        if self._content_panel.parent() != parent:
            self._content_panel.setParent(parent)

        self._floating_panel_pos = self.pos()
        self._center_panel_in_parent()
        self._content_panel.show()
        QtCore.QTimer.singleShot(10, self._ensure_panels_visible)

        self.toggle_button.setText("\n".join(const.OFF_EDIT_MODE_LIST))
        if self._content_widget and hasattr(self._content_widget, "_refresh_targets_list"):
            QtCore.QTimer.singleShot(100, self._content_widget._refresh_targets_list)

    def _collapse_panel(self):
        if self._content_widget and hasattr(self._content_widget, "close_all_editing_targets"):
            self._content_widget.close_all_editing_targets()

        self._fade_out_animation = QtCore.QPropertyAnimation(self._content_panel, b"windowOpacity")
        self._fade_out_animation.setDuration(100)
        self._fade_out_animation.setStartValue(1.0)
        self._fade_out_animation.setEndValue(0.0)
        self._fade_out_animation.finished.connect(self._content_panel.hide)
        self._fade_out_animation.start()

        self.toggle_button.setText("\n".join(const.OPEN_EDIT_MODE_LIST))
        self.raise_()

    def _center_panel_in_parent(self):
        parent = self.parentWidget()
        if not self._content_panel or not parent:
            return

        if self._content_panel.parent() != parent:
            self._content_panel.setParent(parent)

        parent_rect = parent.rect()
        panel_width = int(parent_rect.width() * 0.5)
        panel_height = int(parent_rect.height() * 0.5)
        self._content_panel.resize(panel_width, panel_height)

        y = self.pos().y() + self.height() - panel_height
        y = max(0, min(y, parent_rect.height() - panel_height - 5))

        self._content_panel.move(55, y)

    def moveEvent(self, event):
        super(FloatingPanel, self).moveEvent(event)

        parent = self.parentWidget()
        if parent:
            self._relative_x = self.x()
            self._relative_bottom = parent.height() - self.y()

        if self._expanded and self._content_panel and self._content_panel.isVisible():
            QtCore.QTimer.singleShot(10, self._center_panel_in_parent)

    def parentWidget(self):
        return super(FloatingPanel, self).parentWidget()

    def updatePosition(self):
        parent = self.parentWidget()
        if not parent:
            return

        new_y = parent.height() - self._relative_bottom
        self.move(self._relative_x, new_y)

        if self._expanded and self._content_panel and self._content_panel.isVisible():
            self._center_panel_in_parent()

    def _ensure_panels_visible(self):
        if self._content_panel and self._content_panel.isVisible():
            self._content_panel.raise_()
            self.raise_()

    def closeEvent(self, event):
        if self._content_panel:
            self._content_panel.close()
        super(FloatingPanel, self).closeEvent(event)
