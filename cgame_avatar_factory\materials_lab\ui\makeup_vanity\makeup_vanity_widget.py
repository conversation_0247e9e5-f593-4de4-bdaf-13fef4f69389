"""
Makeup Vanity Widget Module

This is the main widget for the Makeup Vanity section, containing three sub-widgets:
- Makeup Details: For detailed makeup settings
- Makeup Layers: For managing makeup layers
- Makeup Library: For selecting from makeup presets
"""

# Import built-in modules
import logging

# Import third-party modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.common import constants as const
from cgame_avatar_factory.common.ui.layout import FramelessVLayout
from cgame_avatar_factory.common.ui.mixin.style_mixin import MStyleMixin
from cgame_avatar_factory.materials_lab.ui.makeup_vanity.makeup_details_widget import MakeupDetailsWidget
from cgame_avatar_factory.materials_lab.ui.makeup_vanity.makeup_layers_widget import MakeupLayersWidget
from cgame_avatar_factory.materials_lab.ui.makeup_vanity.makeup_library_widget import MakeupLibraryWidget


class MakeupVanityWidget(QtWidgets.QFrame):
    """
    Main widget for the Makeup Vanity section

    Contains three sub-widgets arranged horizontally with resizable splitters:
    - Makeup Details: Left section for detailed makeup settings
    - Makeup Layers: Middle section for managing makeup layers
    - Makeup Library: Right section for selecting from makeup presets
    """

    def __init__(self, parent=None):
        super(MakeupVanityWidget, self).__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.setup_ui()

    def setup_ui(self):
        """Initialize UI components"""
        self.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.main_layout = FramelessVLayout()
        self.main_layout.setContentsMargins(5, 5, 5, 5)
        self.setLayout(self.main_layout)

        # Create the three sub-widgets
        self.makeup_details_widget = MakeupDetailsWidget(self)
        self.makeup_layers_widget = MakeupLayersWidget(self)
        self.makeup_library_widget = MakeupLibraryWidget(self)

        # Style the sub-widgets with background color and border radius
        for widget in [self.makeup_details_widget, self.makeup_layers_widget, self.makeup_library_widget]:
            widget = MStyleMixin.instance_wrapper(widget)
            widget.background_color(const.DAYU_BG_IN_COLOR)
            widget.border_radius(4)

        # Create a splitter to allow resizing the sections
        self.splitter = QtWidgets.QSplitter(QtCore.Qt.Horizontal)
        self.splitter.setChildrenCollapsible(False)  # Prevent sections from being collapsed
        self.splitter.setHandleWidth(8)  # Set the width of the splitter handle

        # Add the sub-widgets to the splitter
        self.splitter.addWidget(self.makeup_details_widget)
        self.splitter.addWidget(self.makeup_layers_widget)
        self.splitter.addWidget(self.makeup_library_widget)

        # Set initial sizes (1.14:1:1.31 ratio)
        self.splitter.setSizes([114, 100, 131])

        # Style the splitter handles
        self.style_splitter_handles()

        # Connect signals between widgets
        self.connect_signals()

        # Add the splitter to the main layout
        self.main_layout.addWidget(self.splitter)

    def style_splitter_handles(self):
        """Style the splitter handles to make them more visible and user-friendly"""
        # Apply stylesheet to make handles more visible
        handle_style = f"""
            QSplitter::handle {{
                background-color: {const.BUTTON_BORDER};
                border-radius: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {const.BUTTON_BORDER};
            }}
            QSplitter::handle:pressed {{
                background-color: {const.BUTTON_BORDER};
            }}
        """
        self.splitter.setStyleSheet(handle_style)

        # Set cursor to horizontal resize cursor for better UX
        for i in range(self.splitter.count() - 1):  # For each handle
            handle = self.splitter.handle(i + 1)
            handle.setCursor(QtCore.Qt.SplitHCursor)

    def connect_signals(self):
        """Connect signals between widgets"""
        # Connect library item selection to layers
        self.makeup_library_widget.sig_makeup_item_selected.connect(self.slot_library_item_selected)

        # Connect layers signals to details widget (for future implementation)
        self.makeup_layers_widget.sig_makeup_layer_selected.connect(self.slot_layer_selected)

    def slot_library_item_selected(self, item_data):
        """Handle selection of an item in the makeup library"""
        self.logger.info(f"Library item selected: {item_data['name']}")
        # In the future, we might want to preview the item here
        # For now, we just log it

    def slot_layer_selected(self, layer_data):
        """Handle selection of a layer in the makeup layers"""
        self.logger.info(f"Layer selected: {layer_data['name']}")
        # In the future, we'll update the details widget with the layer properties
        # For now, we just log it
