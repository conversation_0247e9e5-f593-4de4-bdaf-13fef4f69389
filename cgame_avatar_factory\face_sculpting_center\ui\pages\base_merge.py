# Import built-in modules
import json
import logging
import os

# Import third-party modules
import dayu_widgets
from dayu_widgets import MTabWidget
from maya import cmds
import maya.utils as maya_utils
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.common import constants as const
from cgame_avatar_factory.common import core
import cgame_avatar_factory.common.state.scene_state_manager as scene_state_mgr
from cgame_avatar_factory.common.ui.layout import FramelessHLayout
from cgame_avatar_factory.common.ui.layout import FramelessVLayout
from cgame_avatar_factory.common.ui.mixin.style_mixin import MStyleMixin
import cgame_avatar_factory.face_sculpting_center.build_face.face_controllers as customization
import cgame_avatar_factory.face_sculpting_center.merge.scene_weight_manager as scene_call
from cgame_avatar_factory.face_sculpting_center.ui.automatic_wrap.semi_auto_wrap import <PERSON>A<PERSON><PERSON><PERSON><PERSON>ab
from cgame_avatar_factory.face_sculpting_center.ui.pages.face_area_layout import FaceAreaWidget
from cgame_avatar_factory.face_sculpting_center.utils import mesh_util


class BaseMergePage(QtWidgets.QFrame):
    # Add signals for state changes
    sig_weights_changed = QtCore.Signal(list)
    sig_components_changed = QtCore.Signal(list)
    sig_area_changed = QtCore.Signal(list)
    sig_pivot_move_ready = QtCore.Signal()

    def __init__(self, parent=None):
        """Initialize the BaseMergePage instance.

        Creates a new instance of the BaseMergePage class, setting up logging and scene call.

        Args:
            parent: The parent widget, defaults to None
        """
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.use_new_area_select = True
        self.scene_weight_manager = scene_call.SceneWeightManager()
        # Track reset buttons across face control panels
        self._reset_btns = []

        self.setup_ui()

    def setup_ui(self):
        """Set up the user interface for the BaseMergePage.

        Initializes the main layout, tab widget, and various UI components.
        Creates three tabs: work area, parametric parts, and AI assistance.
        Sets up the operation area, status bar, and floating panel.
        """
        self.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.main_layout = FramelessVLayout()
        self.main_layout.setContentsMargins(5, 5, 5, 5)
        self.setLayout(self.main_layout)

        # Create main tab widget
        # Use dayu_widgets styled tab widget
        self.tab_widget = MStyleMixin.instance_wrapper(MTabWidget())
        self.tab_widget.setTabPosition(QtWidgets.QTabWidget.North)
        self.main_layout.addWidget(self.tab_widget)

        # --- Tab 1: original content ---
        self.work_area_tab = QtWidgets.QWidget()
        self.work_area_layout = FramelessVLayout()
        self.work_area_tab.setLayout(self.work_area_layout)

        # Insert all original content into work_area_layout
        self.ring = None
        self.pivot_move = None
        self.dna_merge_container = None
        self.dna_merge_layout = None
        self.setup_operation_area(self.work_area_layout)
        self.work_area_layout.addSpacing(5)
        self.setup_status_bar(self.work_area_layout)
        self._setup_floating_panel()

        # --- Tab 2 and 3 are currently empty ---
        # Initialize ParametricAttachmentsWidget for tab 2
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.accessibility.accessibility_widget import AccessibilityWidget
        from cgame_avatar_factory.face_sculpting_center.ui.parametric_parts.parametric_parts_widget import (
            ParametricPartsWidget,
        )

        self.param_tab = ParametricPartsWidget()
        self.accessibility_tab = AccessibilityWidget()
        self.semi_auto_wrap_tab = SemiAutoWrapTab()
        # Add tabs to tab widget
        self.tab_widget.addTab(self.work_area_tab, "融合与捏脸工作区")
        self.tab_widget.addTab(self.param_tab, "参数化部件")
        self.tab_widget.addTab(self.accessibility_tab, "辅助功能")
        self.tab_widget.addTab(self.semi_auto_wrap_tab, "半自动化Wrap")
        # Connect tab change signal
        self.tab_widget.currentChanged.connect(self.on_main_tab_changed)

        self.restore_scene_state()

    def restore_scene_state(self):
        ring_state = scene_state_mgr.get_section("ring")
        if ring_state:
            self.ring.restore_state(ring_state)
            dna_num = len(ring_state.get("components", []))
            if dna_num >= 1:
                if hasattr(self, "floating_panel") and self.floating_panel:
                    self.floating_panel.toggle_button.setEnabled(True)
                    self.floating_panel.toggle_button.setToolTip("切换编辑模式")
                    self.floating_panel.toggle_button.setCursor(QtCore.Qt.PointingHandCursor)
            if dna_num == const.DEFAULT_DNA_NUM:
                self.enable_face_tabs()

    def save_scene_state(self):
        ring_state = self.ring.export_state()
        scene_state_mgr.set_section("ring", ring_state)

    def setup_operation_area(self, parent_layout):
        """Set up the operation area of the UI.

        Creates the main operation container with horizontal layout and adds
        the DNA merge area and DNA library components.

        Args:
            parent_layout: The parent layout to add the operation area to
        """
        self.splitter = QtWidgets.QSplitter(QtCore.Qt.Horizontal)
        self.splitter.setChildrenCollapsible(False)
        self.splitter.setHandleWidth(8)
        self._style_splitter_handles()

        self.operation_container = QtWidgets.QFrame()
        self.operation_layout = FramelessHLayout()
        self.operation_container.setLayout(self.operation_layout)
        self.setup_face_operation_area(self.splitter)
        self.setup_dna_merge_area(self.splitter)
        self.operation_layout.addWidget(self.splitter)
        maya_utils.executeDeferred(self.setup_dna_lib)
        self.splitter.setSizes([900, 600, 600])
        parent_layout.addWidget(self.operation_container)

    def add_bottom_export_area(self, parent_layout):
        """Set up the bottom export area of the UI.

        Creates a container with buttons for LOD generation, model collapsing,
        and exporting functionality. Includes a dropdown for selecting export type
        and connects all buttons to their respective handlers.

        Args:
            parent_layout: The parent layout to add the export area to
        """
        self.bottom_export_area_container = MStyleMixin.cls_wrapper(QtWidgets.QFrame)()
        self.bottom_export_area_layout = FramelessHLayout()
        self.bottom_export_area_layout.setContentsMargins(5, 5, 5, 5)
        self.bottom_export_area_container.setLayout(self.bottom_export_area_layout)

        self.bottom_export_area_container.frameless().border_radius(5).background_color(
            const.DAYU_BG_IN_COLOR,
        )

        self.generate_lod_btn = MStyleMixin.instance_wrapper(dayu_widgets.MToolButton())
        self.generate_lod_btn.text_beside_icon().svg("export.svg").small()
        self.generate_lod_btn.transparent_background(modifier=":disabled")
        self.generate_lod_btn.setText("LOD生成")

        self.collapse_model_btn = MStyleMixin.instance_wrapper(dayu_widgets.MToolButton())
        self.collapse_model_btn.text_beside_icon().svg("collapse.svg").small()
        self.collapse_model_btn.setText("塌陷模型")
        self.collapse_model_btn.setCursor(QtCore.Qt.PointingHandCursor)
        self.collapse_model_btn.setStyleSheet(
            f"""
            QToolButton {{
                background-color: {const.APP_PRIMARY_COLOR};
            }}
            QToolButton:pressed {{
                background-color: {const.PRIMARY_COLOR};
            }}
        """
        )
        self.collapse_model_btn.clicked.connect(self._on_collapse_model_clicked)

        self.generate_lod_btn.clicked.connect(self._on_generate_lod_clicked)
        self.bottom_export_area_layout.addWidget(self.generate_lod_btn)
        self.bottom_export_area_layout.addWidget(self.collapse_model_btn)

        self.bottom_export_area_layout.addStretch(1)

        export_layout = FramelessHLayout()

        self.export_type_combobox = MStyleMixin.instance_wrapper(dayu_widgets.MComboBox())
        self.export_type_combobox.frameless().border_radius(5).background_color(const.DAYU_BG_COLOR)
        self.export_type_combobox.addItems(["导出融合结果", "GOZ", "导出至库中"])
        self.export_type_combobox.setCurrentText("导出融合结果")
        self.export_type_combobox.setFixedWidth(180)

        self.export_dna_btn = MStyleMixin.instance_wrapper(dayu_widgets.MToolButton())
        self.export_dna_btn.text_beside_icon().svg("export.svg").small()
        self.export_dna_btn.setText(" 导出")
        self.export_dna_btn.setCursor(QtCore.Qt.PointingHandCursor)
        self.export_dna_btn.setStyleSheet(
            f"""
            QToolButton {{
                background-color: {const.APP_PRIMARY_COLOR};
            }}
            QToolButton:pressed {{
                background-color: {const.DAYU_PRIMARY_7};
            }}
        """
        )

        self.export_dna_btn.clicked.connect(self._on_export_clicked)

        export_layout.addWidget(self.export_type_combobox)
        export_layout.addWidget(self.export_dna_btn)
        self.bottom_export_area_layout.addLayout(export_layout)

        parent_layout.addWidget(self.bottom_export_area_container)

    def setup_dna_merge_area(self, parent_layout):
        """Set up the DNA merge area with ring widget and pivot move.

        Creates a container for the DNA merge functionality, including the functional area,
        DNA ring widget, and export controls. This is the main workspace for DNA merging operations.

        Args:
            parent_layout: The parent layout to add the DNA merge area to
        """
        self.dna_merge_container = QtWidgets.QFrame(parent=self)
        self.dna_merge_layout = FramelessVLayout()
        self.dna_merge_container.setLayout(self.dna_merge_layout)

        maya_utils.executeDeferred(self.setup_functional_area, self.dna_merge_layout)
        self.dna_merge_layout.addStretch(1)
        self.setup_dna_ring(self.dna_merge_layout)
        self.dna_merge_layout.addStretch(1)
        maya_utils.executeDeferred(self.add_bottom_export_area, self.dna_merge_layout)
        parent_layout.addWidget(self.dna_merge_container)

    def setup_face_operation_area(self, parent_layout):
        """
        Set up the face operation area with a tab widget containing face blend area selection,
        basic blend, and refine blend tabs.

        Args:
            parent_layout (QLayout): The parent layout to which the tab widget will be added.

        Raises:
            KeyError: If "shoulder" is not in the facearea_name_list when attempting to remove it.
        """
        # 创建水平布局容器作为主容器
        self.face_operation_container = QtWidgets.QWidget()
        self.face_operation_layout = QtWidgets.QHBoxLayout(self.face_operation_container)
        self.face_operation_layout.setContentsMargins(0, 0, 0, 0)
        self.face_operation_layout.setSpacing(0)
        parent_layout.addWidget(self.face_operation_container)

        # 创建内容堆叠部件
        self.face_content_stack = QtWidgets.QStackedWidget()
        # 创建右侧垂直标签栏容器
        self.face_tabs_container = QtWidgets.QWidget()
        self.face_tabs_layout = QtWidgets.QVBoxLayout(self.face_tabs_container)
        self.face_tabs_layout.setContentsMargins(0, 5, 5, 5)
        self.face_tabs_layout.setSpacing(2)
        self.face_tabs_layout.setAlignment(QtCore.Qt.AlignRight)

        self.face_operation_layout.addWidget(self.face_content_stack, 19)
        self.face_operation_layout.addWidget(self.face_tabs_container, 1)

        # 存储标签按钮的字典
        self.face_tab_buttons = {}
        self.current_face_tab_index = 0

        # --- Tab 1: Face blend area selection ---
        self.face_blend_area_tab = QtWidgets.QWidget()
        self.face_blend_area_layout = FramelessVLayout()
        self.face_blend_area_tab.setLayout(self.face_blend_area_layout)

        # Get all face area names from PINCH_CTRL_ATTR, remove "shoulder" if present
        facearea_name_list = [value for key, value in const.PINCH_CTRL_ATTR.items()]
        if "shoulder" in facearea_name_list:
            facearea_name_list.remove("shoulder")

        # Generate config for FaceAreaWidget
        facearea_config = FaceAreaWidget.generate_face_area_config(
            resource_root=const.FACEAREA_PATH,
            name_list=facearea_name_list,
            button_menu=[
                # Right-click menu actions for area selection widget
                (
                    "reset当前所选区域",
                    lambda name: (
                        self.scene_weight_manager.reset_merge(self.get_current_areas()),
                        self.reset_current_area_dna(self.get_current_areas()),
                    ),
                ),
                (
                    "reset全部区域",
                    lambda name: (
                        self.scene_weight_manager.reset_merge(facearea_name_list),
                        self.reset_current_area_dna(facearea_name_list),
                    ),
                ),
                ("全选", lambda name: self.area_select_widget.select_all()),
                ("随机融合", lambda name: self.area_select_widget.random_fuse()),
            ],
            enable_fuse_mode=True,
        )

        # Create the area selection widget and add to layout
        self.area_select_widget = FaceAreaWidget(facearea_config)
        self.face_blend_area_layout.addWidget(self.area_select_widget)

        # --- Tab 2: Basic blend ---
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.pages.face_area_layout import FaceControlPointWidget

        self.basic_blend_tab = QtWidgets.QWidget()
        self.basic_blend_layout = FramelessVLayout()
        self.basic_blend_tab.setLayout(self.basic_blend_layout)
        basic_json_path = os.path.join(const.FACECUSTOMIZATION_PATH, const.FACE_CONTROL_JSON_FILES[1])
        basic_points = self.get_points_info_from_json(basic_json_path)

        def select_obj_by_name(name):
            cmds.select(clear=True)
            cmds.setToolTo("moveSuperContext")
            if cmds.objExists(name):
                cmds.select(name)

        def on_bg_clicked(event):
            cmds.select(clear=True)

        img_path = os.path.join(const.FACEAREA_PATH, "face_area_empty.png")

        self.basic_blend_face_widget = FaceControlPointWidget(
            img_path,
            basic_points,
            button_callback=select_obj_by_name,
            mirror_callback=self._on_face_symmetry_changed,
            reset_callback=self.on_reset_face_clicked,
            background_callback=on_bg_clicked,
        )
        self.basic_blend_layout.addWidget(self.basic_blend_face_widget)
        # collect reset button
        if hasattr(self.basic_blend_face_widget, "panel"):
            self._reset_btns.append(self.basic_blend_face_widget.panel.btn_reset)

        # --- Tab 3: Detail blend ---
        self.detail_blend_tab = QtWidgets.QWidget()
        self.detail_blend_tab_layout = FramelessVLayout()
        self.detail_blend_tab.setLayout(self.detail_blend_tab_layout)
        detail_json_path = os.path.join(const.FACECUSTOMIZATION_PATH, const.FACE_CONTROL_JSON_FILES[0])
        detailPoints = self.get_points_info_from_json(detail_json_path)
        self.detail_blend_face_widget = FaceControlPointWidget(
            img_path,
            detailPoints,
            button_callback=select_obj_by_name,
            mirror_callback=self._on_face_symmetry_changed,
            reset_callback=self.on_reset_face_clicked,
            background_callback=on_bg_clicked,
        )
        self.detail_blend_tab_layout.addWidget(self.detail_blend_face_widget)
        if hasattr(self.detail_blend_face_widget, "panel"):
            self._reset_btns.append(self.detail_blend_face_widget.panel.btn_reset)

        # 添加内容到堆叠部件
        self.face_content_stack.addWidget(self.face_blend_area_tab)  # 索引 0
        self.face_content_stack.addWidget(self.basic_blend_tab)  # 索引 1
        self.face_content_stack.addWidget(self.detail_blend_tab)  # 索引 2

        # 创建垂直标签按钮
        tab_titles = ["融合", "基础捏脸", "精细捏脸"]
        tab_style = """
            QPushButton {
                min-width: 18px;
                max-width: 24px;
                min-height: 120px;
                padding: 10px 5px;
                margin: 0px 0px;
                font-size: 17px;
                font-weight: normal;
                border-radius: 3px;
                background-color: rgba(65, 65, 65, 1);
                color: rgba(230, 230, 230, 1);
                border: 3px solid rgba(0, 0, 0, 0.1);
                text-align: center;
                letter-spacing: 2px;
                line-height: 1.4;
            }
            QPushButton:hover {
                color: rgba(255, 255, 255, 1);
                background-color: rgba(100, 100, 100, 1);
                border: 2px solid rgba(0, 0, 0, 0.3);
            }
            QPushButton:checked {
                background-color: rgba(50, 50, 50, 1);
                color: rgba(220, 220, 220, 1);
                font-weight: normal;
                border: 1px solid rgba(0, 0, 0, 0);
                border-right: 2px solid rgba(20, 160, 200, 1);
            }
        """

        for i, title in enumerate(tab_titles):
            vertical_title = "\n".join(title)
            tab_button = QtWidgets.QPushButton(vertical_title)
            tab_button.setCheckable(True)
            tab_button.setStyleSheet(tab_style)
            tab_button.clicked.connect(lambda checked=False, idx=i: self.switch_face_tab(idx))
            self.face_tabs_layout.addWidget(tab_button)
            self.face_tab_buttons[i] = tab_button

        self.face_tabs_layout.addStretch()

        # Initialize face tab and set controller state
        self.current_face_tab_index = 0
        self.face_content_stack.setCurrentIndex(0)
        for i, button in self.face_tab_buttons.items():
            button.setChecked(i == 0)

        self.disable_face_tabs()

    def disable_face_tabs(self):
        self.face_blend_area_tab.setEnabled(False)
        self.basic_blend_tab.setEnabled(False)
        self.detail_blend_tab.setEnabled(False)

    def enable_face_tabs(self):
        self.face_blend_area_tab.setEnabled(True)
        self.basic_blend_tab.setEnabled(True)
        self.detail_blend_tab.setEnabled(True)

    def on_main_tab_changed(self, index):
        """Handle main tab change event

        Control controller visibility based on current tab state when tab is switched

        Args:
            index: Index of currently selected tab
        """
        # Always hide controllers if not in "Merge & Face Sculpting" tab
        if index != 0:  # 0 is the index of "Merge & Face Sculpting" tab
            try:
                customization.hide_all_ctrl()
            except Exception as e:
                self.logger.warning(f"Failed to hide controllers: {e}")
        else:
            # In "Merge & Face Sculpting" tab, control controllers based on current face tab state
            if hasattr(self, "current_face_tab_index"):
                try:
                    self._update_controllers_by_face_tab(self.current_face_tab_index)
                except Exception as e:
                    self.logger.warning(f"Failed to update controllers: {e}")

    def _update_controllers_by_face_tab(self, face_tab_index):
        """Update controller state based on face tab index

        Args:
            face_tab_index: Face tab index
        """
        try:
            if face_tab_index == 0:  # "Merge" tab
                customization.hide_all_ctrl()
            elif face_tab_index == 1:  # "Basic Face Sculpting" tab
                customization.change_to_basic()
                if hasattr(self, "basic_blend_face_widget"):
                    self.basic_blend_face_widget.set_mirror_checked(customization.Iosymmetrical().return_symmetrical())
            elif face_tab_index == 2:  # "Detailed Face Sculpting" tab
                customization.change_to_detailed()
                if hasattr(self, "detail_blend_face_widget"):
                    self.detail_blend_face_widget.set_mirror_checked(customization.Iosymmetrical().return_symmetrical())
        except Exception as e:
            self.logger.warning(f"Failed to update controllers for face tab {face_tab_index}: {e}")

    def switch_face_tab(self, index):
        self.current_face_tab_index = index
        self.face_content_stack.setCurrentIndex(index)

        for i, button in self.face_tab_buttons.items():
            button.setChecked(i == index)

        # Only update controllers when currently in "Merge & Face Sculpting" tab
        if self.tab_widget.currentIndex() == 0:
            self._update_controllers_by_face_tab(index)

    def _on_collapse_model_clicked(self):
        reply = QtWidgets.QMessageBox.question(
            self,
            "确认操作",
            "确定要执行模型塌陷操作吗？此操作不可撤销。",
            QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
            QtWidgets.QMessageBox.No,
        )
        if reply == QtWidgets.QMessageBox.Yes:
            self.logger.info("Collapsing model")

            self._collapse_and_add_dna()

    def _collapse_and_add_dna(self):
        """Collapse the model and automatically load the specified DNA file.

        Performs the following operations:
        1. Sets a flag to indicate the model was collapsed
        2. Captures a screenshot of the viewport for texture
        3. Resets the DNA ring and adds the base DNA component
        4. Notifies the parent widget of the DNA addition

        Handles errors by displaying appropriate error messages to the user.
        """
        # Import local modules
        from cgame_avatar_factory.common.utils.get_view_image_utils import get_viewport_screenshot

        if hasattr(self.parent(), "is_from_collapse_model"):
            self.parent().is_from_collapse_model = True
        else:
            setattr(self.parent(), "is_from_collapse_model", True)

        dna_file_path = os.path.join(const.CONFIG_PATH, const.BASE_DNA_PATH)

        try:
            mesh_util.focus_camera_on_head()
            texture_path = get_viewport_screenshot()

            if not os.path.exists(dna_file_path) or not os.path.exists(texture_path):
                self.logger.error(f"DNA文件或贴图不存在: {dna_file_path} 或 {texture_path}")
                QtWidgets.QMessageBox.warning(
                    self,
                    "文件不存在",
                    f"无法找到指定的DNA文件或贴图:\n{dna_file_path}\n{texture_path}",
                    QtWidgets.QMessageBox.Ok,
                )
                return

            self.reset_dna_ring()
            widget = self.ring.create_component(self._create_dna_info(dna_file_path, texture_path))
            self.ring.add_component_to_center(widget)

        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self,
                "操作失败",
                f"塌陷模型过程中发生错误:\n{str(e)}",
                QtWidgets.QMessageBox.Ok,
            )
            return

        if hasattr(self.parent(), "slot_dna_added_to_ring"):
            self.parent().slot_dna_added_to_ring(self._create_dna_info(dna_file_path, texture_path))
        if hasattr(self, "ring") and self.ring:
            self.ring.setAcceptDrops(True)

    def _create_dna_info(self, dna_file_path, texture_path):
        """创建DNA信息字典

        Args:
            dna_file_path: DNA文件路径
            texture_path: 贴图路径

        Returns:
            dict: DNA信息字典
        """
        return {
            "dna_file_path": dna_file_path,
            "dna_texture_path": texture_path,
            "thumbnail_file_path": texture_path,
            "dna_name": "BB_AT_Jacket_1215",
            "dna_order": 1,
            "collapsed_model": True,
        }

    def setup_dna_ring(self, parent_layout):
        """Set up the DNA ring widget and initialize pivot move.

        Creates the drag point ring widget for DNA component manipulation and
        initializes the pivot move functionality. Sets up signal connections and
        emits the pivot move ready signal.

        Args:
            parent_layout: The parent layout to add the DNA ring to
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.dna_merge_ring.drag_point_ring import DragPointRingWidget
        from cgame_avatar_factory.face_sculpting_center.ui.pivot_move import UIPivotMove

        self.logger.info("Setting up DNA ring")

        if not self.ring:
            self.ring = DragPointRingWidget(parent=self)
            self.pivot_move = UIPivotMove(ring_widget=self.ring, parent=self)
            maya_utils.executeDeferred(self._setup_ring_connections)
            maya_utils.executeDeferred(self.sig_pivot_move_ready.emit)

        parent_layout.addWidget(self.ring)
        self.logger.info("DNA ring setup completed")

    def _style_splitter_handles(self):
        """Style the splitter handles to make them more visible and user-friendly"""
        # Apply stylesheet to make handles more visible
        handle_style = f"""
            QSplitter::handle {{
                background-color: {const.BUTTON_BORDER};
                border-radius: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {const.BUTTON_BORDER};
            }}
            QSplitter::handle:pressed {{
                background-color: {const.BUTTON_BORDER};
            }}
        """
        self.splitter.setStyleSheet(handle_style)

        # Set cursor to horizontal resize cursor for better UX
        for i in range(self.splitter.count() - 1):  # For each handle
            handle = self.splitter.handle(i + 1)
            handle.setCursor(QtCore.Qt.SplitHCursor)

    def _setup_floating_panel(self):
        """Set up the floating panel for additional controls.

        Creates a floating panel with content panel inside it. The floating panel
        provides access to editing functionality that can be expanded or collapsed.
        Connects signals and positions the panel in the UI.
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.components.content_panel import ContentPanel
        from cgame_avatar_factory.face_sculpting_center.ui.components.floating_panel import FloatingPanel

        self.floating_panel = FloatingPanel(self.dna_merge_container)

        self.content_panel = ContentPanel(self)

        self.floating_panel.set_content_widget(self.content_panel)

        self.floating_panel.sig_expanded_changed.connect(self._on_floating_panel_state_changed)

        def _place_floating_panel():
            if not self.dna_merge_container or not self.floating_panel:
                return
            margin = 65
            x = 5
            y = max(0, self.dna_merge_container.height() - self.floating_panel.height() - margin)
            self.floating_panel.move(x, y)

        QtCore.QTimer.singleShot(200, _place_floating_panel)

    def _on_floating_panel_state_changed(self, expanded):
        """Handle changes in the floating panel's expanded state.

        When the panel is expanded (edit mode), disables certain UI elements and adjusts
        layer selectability to facilitate editing. When collapsed, restores normal operation
        mode and re-enables previously disabled UI elements.

        Args:
            expanded: Boolean indicating whether the panel is expanded (True) or collapsed (False)
        """
        if expanded:
            mesh_util.set_layer_display_type(const.CONTROLLER_LAYER_NAME, 2)
            mesh_util.set_layer_display_type(const.HEAD_LAYER_NAME, 0)
            cmds.select(clear=True)
            if self.pivot_move and self.pivot_move._ring_widget:
                self.pivot_move._ring_widget.setEnabled(False)
                self._set_reset_face_btn_enabled(False)

                self.disable_face_tabs()
        else:
            self.content_panel.blendshape_target_set_enabled(self.content_panel, in_or_out="out")
            self.content_panel.reset_target_editing(self.content_panel)
            self.content_panel.remove_selected_target(self.content_panel)

            mesh_util.set_layer_display_type(const.CONTROLLER_LAYER_NAME, 0)
            mesh_util.set_layer_display_type(const.HEAD_LAYER_NAME, 2)
            cmds.select(clear=True)

            if self.pivot_move and self.pivot_move._ring_widget:
                self.pivot_move._ring_widget.setEnabled(True)
                self._set_reset_face_btn_enabled(True)

                self.enable_face_tabs()

                self.logger.info("编辑模式已关闭：启用pivot_move中的ring_widget控制器")

    def _set_reset_face_btn_enabled(self, enabled: bool):
        """Enable or disable all collected reset face buttons."""
        for btn in self._reset_btns:
            btn.setEnabled(enabled)

    def _setup_ring_connections(self):
        """Set up signal connections for the DNA ring widget.

        Connects the DNA ring signals to their respective handlers to respond to
        component additions, removals, and changes. Also connects the pivot weight
        change signal from the pivot move widget.

        Logs an error if either the ring or pivot_move is not initialized.
        """
        if not self.ring or not self.pivot_move:
            self.logger.error("Cannot setup connections - ring or pivot_move not initialized")
            return
        self.ring.sig_dna_added.connect(self._on_component_changed)
        self.ring.sig_dna_removed.connect(self._on_component_changed)
        self.ring.sig_dna_changed.connect(self._on_component_changed)
        self.pivot_move.sig_pivot_weight_changed.connect(self._on_weights_changed)
        self.logger.info("Signal connections established")

    def _disconnect_ring_connections(self):
        """Safely disconnect all ring signal connections.

        Attempts to disconnect all signal connections between the ring widget,
        pivot move widget, and their respective handlers. Catches and logs any
        errors that occur during disconnection to prevent exceptions from
        propagating.
        """
        if self.ring:
            try:
                self.ring.sig_dna_added.disconnect(self._on_component_changed)
                self.ring.sig_dna_removed.disconnect(self._on_component_changed)
                self.ring.sig_dna_changed.disconnect(self._on_component_changed)
            except (TypeError, RuntimeError) as e:
                self.logger.warning("Error disconnecting ring signals: %s", str(e))

        if self.pivot_move:
            try:
                self.pivot_move.sig_pivot_weight_changed.disconnect(self._on_weights_changed)
            except (TypeError, RuntimeError) as e:
                self.logger.warning("Error disconnecting pivot_move signals: %s", str(e))

    def reset_dna_ring(self):
        """Clear DNA components from the ring without recreating the ring widget.

        Removes all DNA components from the ring and reinitializes the pivot.
        This is used when resetting the state without destroying the entire widget.

        Returns:
            bool: True if the operation was successful, False otherwise
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.utils import collapse_utils

        try:
            self.logger.info("清除环中的DNA组件")

            if not hasattr(self, "ring") or not self.ring:
                self.logger.error("环组件不存在，无法清除DNA")
                return False

            self.ring.clear_components()
            collapse_utils.delete_mesh_history()
            if hasattr(self, "pivot_move") and self.pivot_move:
                self.pivot_move.init_pivot()

            self.logger.info("DNA环中的组件已清除")
            collapse_utils.delete_nodes()
            return True

        except Exception as e:
            self.logger.error("清除DNA环组件失败: %s", str(e))
            return False

    def _on_weights_changed(self, weights):
        """Handle weight changes from pivot move.

        Receives weight changes from the pivot move widget and emits a signal
        to notify other components about the weight changes. This is used to
        update the DNA merging based on the new weights.

        Args:
            weights: List of weight values for the DNA components
        """
        if not self.ring:
            self.logger.error("Ring widget not initialized")
            return

        self.sig_weights_changed.emit(weights)

    def _on_component_changed(self, *args):
        """Handle component changes in ring widget.

        Responds to DNA components being added, removed, or changed in the ring widget.
        Gets the current positions of all components and emits a signal with this information
        to update the DNA merging accordingly.

        Args:
            *args: Variable arguments passed from the signal
        """
        if not self.ring:
            self.logger.error("Ring widget not initialized")
            return

        positions = self.ring.get_component_positions()
        self.logger.debug("Component change detected")
        self.logger.debug("Number of components: %d", len(positions))
        self.logger.debug("Component positions: %s", positions)
        self.sig_components_changed.emit(positions)

    def setup_dna_lib(self):
        """Set up the DNA library widget.

        Creates and configures the DNA library widget, which provides access to
        available DNA components that can be used for merging. Sets the widget to
        be frameless and adds it to the operation layout.
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.dna_lib.dna_lib_widgets import DNALibWidget

        self.dna_lib = DNALibWidget.instance()
        self.dna_lib.frameless()
        self.dna_lib.setMinimumSize(500, 300)
        self.splitter.addWidget(self.dna_lib)

    def remove_dna_lib(self):
        """Remove the DNA library widget from the layout.

        Removes the DNA library widget from the operation layout without destroying it.
        This is typically used when reconfiguring the UI or when the DNA library is
        temporarily not needed.
        """
        self.operation_layout.removeWidget(self.dna_lib)

    def setup_functional_area(self, parent_layout):
        """Set up the functional area of the UI.

        Creates a container for functional controls including top and bottom functional areas
        and random/reset controls. The functional area is initially disabled and will be
        enabled when appropriate conditions are met.

        Args:
            parent_layout: The parent layout to add the functional area to
        """
        self.functional_area_container = QtWidgets.QFrame()
        self.functional_area_layout = FramelessVLayout()
        self.functional_area_container.setLayout(self.functional_area_layout)
        self.functional_area_container.setEnabled(False)
        self.functional_area_container.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)

        separator = QtWidgets.QFrame(self)
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.functional_area_layout.addWidget(separator)

        parent_layout.addWidget(self.functional_area_container)

    def reset_current_area_dna(self, current_areas):
        self.scene_weight_manager.reset_merge(current_areas)

        if hasattr(self, "pivot_move") and self.pivot_move:
            weights = [1.0, 0.0, 0.0, 0.0]
            self.pivot_move.set_pos_by_weight(weights, block_signal=True)

    def on_reset_face_clicked(self):
        reply = QtWidgets.QMessageBox.question(
            self,
            "确认重置",
            "确定要重置捏脸吗？",
            QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
            QtWidgets.QMessageBox.No,
        )

        if reply == QtWidgets.QMessageBox.Yes:
            customization.clear_all_ctrls()

    def switch_mode(self, index):
        """Switch between Basic and Detailed face customization modes.

        Changes the face customization mode based on the selected index in the mode combo box.
        The Basic mode provides simplified controls, while the Detailed mode offers more
        precise control options.

        Args:
            index: The index of the selected item in the mode combo box
        """
        mode = self.mode_combo_box.itemText(index)
        if mode == "Basic":
            customization.change_to_basic()
        elif mode == "Detailed":
            customization.change_to_detailed()

    def _on_multi_select_changed(self, checked):
        """Handle changes to the multi-select checkbox state.

        When multi-select is enabled, allows multiple area buttons to be selected simultaneously.
        When disabled, enforces exclusive selection by unchecking all but the first selected button.

        Args:
            checked: Boolean indicating whether multi-select is enabled (True) or disabled (False)
        """
        if hasattr(self, "area_select_widget"):
            self.area_select_widget.set_exclusive(not checked)

            if not checked:
                checked_buttons = [btn for btn in self.area_select_widget.button_group.buttons() if btn.isChecked()]
                if len(checked_buttons) > 1:
                    for button in checked_buttons[1:]:
                        button.setChecked(False)
                        button.setStyleSheet("")

                    selected_areas = [checked_buttons[0].text()]
                    self.area_select_widget.selected_buttons_changed.emit(selected_areas)

    def setup_preview_animation_select_area(self, parent_layout):
        pass

    def setup_status_bar(self, parent_layout):
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.components.status_bar import ProgressWidget

        self.status_bar = ProgressWidget()
        parent_layout.addWidget(self.status_bar)

    def get_merge_state(self):
        """Get current state of merge UI for debugging."""
        state = {
            "components_count": len(self.ring.components),
            "component_positions": self.ring.get_component_positions(),
            "point_position": self.ring.point_pos,
            "weights": self.pivot_move.weights if self.pivot_move else None,
        }
        return state

    def get_current_areas(self):
        if self.use_new_area_select:
            return self.area_select_widget.current_areas()
        else:
            return [self.merge_area_select_combobox.currentText()]

    def resizeEvent(self, event):
        super(BaseMergePage, self).resizeEvent(event)

        if hasattr(self, "floating_panel") and self.floating_panel:
            self.floating_panel.updatePosition()

        if hasattr(self, "random_and_reset_area_container"):
            parent_width = self.functional_area_container.width()
            self.random_and_reset_area_container.setFixedWidth(parent_width // 3)

    def _on_face_symmetry_changed(self, state):
        """Handle changes to face symmetry state.

        Updates the face customization symmetry setting based on checkbox state.
        When enabled, changes to one side of the face are mirrored to the other side.
        When disabled, each side can be edited independently.

        Args:
            state: The current state of the symmetry checkbox (True for checked, False for unchecked)
        """
        current_selection = cmds.ls(selection=True)
        symmetrical = customization.Iosymmetrical()
        if state:
            symmetrical.set_symmetrical_true()
            customization.symmetry_mode()

        else:
            symmetrical.set_symmetrical_false()
            customization.remove_selection_changed_script_jobs()
        if current_selection:
            cmds.select(current_selection, replace=True)

    def _on_export_clicked(self):
        """Handle export button click event.

        Exports the current model based on the selected export type in the dropdown.
        Options include exporting the merged result or sending to GoZ (ZBrush).
        Reports the export action to the analytics system.
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.dna_lib.dna_export_widgets import CharacterExportDialog
        from cgame_avatar_factory.face_sculpting_center.utils import export_utils
        from cgame_avatar_factory.face_sculpting_center.utils import goz_utils

        export_type = self.export_type_combobox.currentText()
        if export_type == "导出融合结果":
            with core.get_reporter_instance() as api:
                api.report_count(
                    event_name="export",
                    action="export mesh",
                    tool_name=const.PACKAGE_NAME,
                )
            export_utils.duplicate_and_export_head_mesh()
        elif export_type == "GOZ":
            with core.get_reporter_instance() as api:
                api.report_count(
                    event_name="export",
                    action="goz",
                    tool_name=const.PACKAGE_NAME,
                )
            goz_utils.send_to_goz()
        elif export_type == "导出至库中":
            with core.get_reporter_instance() as api:
                api.report_count(
                    event_name="export",
                    action="export wrap",
                    tool_name=const.PACKAGE_NAME,
                )
            export_dna_window = CharacterExportDialog(parent=self)
            if export_dna_window.exec_():
                progress_callback = lambda progress: (
                    self.status_bar.progress_bar.setValue(progress.get("value", 0)),
                    self.status_bar.success_label.setText(progress.get("text", "")) if "text" in progress else None,
                )
                export_utils.export_character_package_from_dna(export_dna_window.character_data, progress_callback)
                self.dna_lib.reload_dna_list()
                self.dna_lib.update_all_tags()

        else:
            cmds.warning(f"invalid export type{export_type}")

    def _on_generate_lod_clicked(self):
        """Handle LOD generation button click event.

        Creates an instance of the LODGeneration class and calls its generate_lod method
        to create lower level of detail models based on the current model.
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.lod_generate import LODGeneration

        lod_generation = LODGeneration()
        lod_generation.generate_lod()
        msg_box = QtWidgets.QMessageBox(
            QtWidgets.QMessageBox.Information,  # 信息提示
            "提示",
            "LOD生成已完成",
            QtWidgets.QMessageBox.Ok,
            self,
        )
        msg_box.setWindowFlags(QtCore.Qt.Tool | QtCore.Qt.WindowStaysOnTopHint)
        maya_window = self.window()
        maya_geometry = maya_window.geometry()
        msg_box.setFixedSize(msg_box.sizeHint())
        x = maya_geometry.x() + (maya_geometry.width() - msg_box.width()) // 2
        y = maya_geometry.y() + (maya_geometry.height() - msg_box.height()) // 2
        msg_box.move(x, y)
        msg_box.exec_()
        return

    def create_overlay_delayed(self):
        # Import local modules
        from cgame_avatar_factory.common.ui.overlay_widgets import create_overlay

        self.overlay = create_overlay(
            "捏脸绑定转换",
            self.on_binding_button_clicked,
            self,
            self.tab_widget,
        )

    def on_binding_button_clicked(self):
        # Import local modules
        from cgame_avatar_factory.animation_theater.ui import animation_theater_page
        from cgame_avatar_factory.animation_theater.ui.face_anim_preview import reset_face_anim_attr
        from cgame_avatar_factory.face_sculpting_center.build_face import setup_riglogic as riglogic

        self.logger.debug("点击了捏脸绑定转换按钮")
        self.output_dna_path = mesh_util.create_workspace_dna_dir(const.PINCH_OUTPUT_DNA_NAME)
        reset_face_anim_attr()
        self.reset_mesh_configuration()
        self.reset_joint_position()
        riglogic.SetupRiglogic(self.output_dna_path, const.PINCE_CTRL_NAME, const.PINCE_NODE_NAME, connect=False)
        animation_theater_page.find_target_and_create_overlay(self, animation_theater_page.AnimationTheaterPage)

    def reset_joint_position(self):
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.utils import dna_utils as dna

        reader = dna.load_dna_calib(self.output_dna_path)
        joint_count = reader.getJointCount()
        mesh_util.disconnect_joint_attr(joint_count, reader)
        for joint_index in range(joint_count):
            joint_name = reader.getJointName(joint_index)
            mesh_util.set_joint_position(
                joint_name,
                reader.getNeutralJointTranslation(joint_index),
                reader.getNeutralJointRotation(joint_index),
            )

    def reset_mesh_configuration(self):
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.utils import dna_utils as dna

        for mesh_name in dna.TemplateDna.get_mesh_name():
            cmds.delete(mesh_name)
            now_name = mesh_name + f"_{const.PINCH_OUTPUT_DNA_NAME}"
            cmds.rename(now_name, mesh_name)
            cmds.setAttr(f"{mesh_name}.visibility", 1)

    def get_points_info_from_json(self, json_path):
        """
        Load control point information from a JSON file.

        Args:
            json_path (str): The file path to the JSON file containing point data.

        Returns:
            list: A list of dictionaries, each containing:
                - "name" (str): The name of the control point.
                - "pos" (list): The position of the control point.
                - "radius" (float): The visual radius of the control point (default 0.012).
        """
        if not os.path.exists(json_path):
            logging.error(f"JSON file not found: {json_path}")
            return []

        with open(json_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        points = []
        for item in data:
            name = item.get("Name")
            pos = item.get("pos")
            if name is not None and pos is not None:
                points.append(
                    {
                        "name": name,
                        "pos": pos,
                        "radius": 0.012,
                    },
                )
        return points
