#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
表情预览模块

提供表情预览和列表功能的界面组件
"""

# Import built-in modules
# 导入内置模块
import logging
import os
from typing import Callable
from typing import Optional

# Import third-party modules
# 导入第三方模块
import dayu_widgets
import maya.cmds as cmds
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.common import constants as const

# 导入本地模块
from cgame_avatar_factory.common.ui.layout import FramelessVLayout
from cgame_avatar_factory.common.utils import utils


class FacePreviewWidget(QtWidgets.QWidget):
    """
    表情预览组件

    提供表情预览和列表功能的界面
    包含表情列表和操作按钮
    """

    # 间距和边距常量
    CONTENT_MARGIN = (8, 8, 8, 8)
    ICON_SIZE = (32, 32)
    LIST_ITEM_PADDING = 8
    LIST_SPACING = 2
    BORDER_WIDTH = 3
    BORDER_RADIUS = 8
    PADDING = 3

    sig_face_selected = QtCore.Signal(str)
    sig_face_deleted = QtCore.Signal(str)

    def __init__(self, parent=None):
        super(FacePreviewWidget, self).__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.anim_attr_json_path = os.path.join(const.CONFIG_PATH, const.ANIM_ATTR_JSON)
        self.setup_ui()

    def _create_button(
        self,
        text: str,
        tooltip: str,
        click_handler: Optional[Callable] = None,
        active_color: str = const.PRIMARY_COLOR,
    ) -> QtWidgets.QPushButton:
        """创建按钮通用方法

        Args:
            text: 按钮文本
            tooltip: 提示文本
            click_handler: 点击处理函数
            active_color: 按下状态的背景颜色，默认蓝色

        Returns:
            配置好的QPushButton实例
        """
        button = QtWidgets.QPushButton(text)
        button.setFixedHeight(40)
        button.setCursor(QtCore.Qt.PointingHandCursor)
        button.setToolTip(tooltip)

        button_style = f"""
            QPushButton {{
                border: {self.BORDER_WIDTH}px solid {const.BUTTON_BORDER};
                border-radius: {self.BORDER_RADIUS}px;
                background-color: {const.BUTTON_BG};
                color: {const.DAYU_PRIMARY_TEXT_COLOR};
                padding: {self.PADDING}px;
            }}
            QPushButton:hover {{
                background-color: {const.BUTTON_HOVER_BG};
            }}
            QPushButton:pressed {{
                background-color: {active_color};
                color: {const.BUTTON_CHECKED_TEXT};
                font-weight: bold;
            }}
        """

        button.setStyleSheet(button_style)
        if click_handler:
            button.clicked.connect(click_handler)
        return button

    def setup_ui(self):
        """初始化UI组件"""
        layout = FramelessVLayout()
        layout.setContentsMargins(*self.CONTENT_MARGIN)
        self.setLayout(layout)

        title = dayu_widgets.MLabel("表情预览")
        title.setProperty("h3", True)
        layout.addWidget(title)

        self.list_widget = QtWidgets.QListWidget()
        self.list_widget.setStyleSheet(
            f"QListWidget {{border: 1px solid {const.BUTTON_BORDER}; background-color:"
            f" {const.BUTTON_BORDER_DARK};}}QListWidget::item {{padding: {self.LIST_ITEM_PADDING}px; border-bottom: 1px"
            f" solid {const.BUTTON_BORDER};}}QListWidget::item:selected {{background-color:"
            f" {const.UI_COLOR_SELECTED};}}QListWidget::item:hover {{background-color: {const.BUTTON_BG};}}",
        )

        self.list_widget.setIconSize(QtCore.QSize(*self.ICON_SIZE))
        self.list_widget.setSpacing(self.LIST_SPACING)

        self.list_widget.itemClicked.connect(self._on_face_selected)

        self.reset_button = self._create_button(
            text="恢复中立状态",
            tooltip="将表情重置为中立状态",
            click_handler=self._on_reset_clicked,
            active_color=const.PRIMARY_COLOR,
        )

        self.delete_button = self._create_button(
            text="删除当前动画",
            tooltip="删除当前选中的表情动画",
            click_handler=self._on_delete_clicked,
            active_color=const.DAYU_ERROR_COLOR,
        )

        button_layout = QtWidgets.QHBoxLayout()
        button_layout.setContentsMargins(0, 5, 0, 0)
        button_layout.setSpacing(10)
        button_layout.addWidget(self.reset_button)
        button_layout.addWidget(self.delete_button)

        layout.addWidget(self.list_widget, 1)
        layout.addLayout(button_layout)

    def return_list_widget(self):
        return self.list_widget

    def _on_face_selected(self, item):
        """处理表情选择事件"""
        face_name = item.text()
        self.logger.info(f"选择了表情: {face_name}")
        self.set_face_anim_attr(face_name)

    def _on_reset_clicked(self):
        """处理恢复中立状态按钮点击事件"""
        reset_face_anim_attr()

    def _on_delete_clicked(self):
        """处理删除当前动画按钮点击事件"""
        current_item = self.list_widget.currentItem()
        if current_item:
            face_name = current_item.text()
            self.sig_face_deleted.emit(face_name)
            row = self.list_widget.row(current_item)
            self.list_widget.takeItem(row)
            self.logger.info(f"删除表情: {face_name}")
        else:
            self.logger.warning("未选中表情，无法删除")

    def set_face_anim_attr(self, face_name):
        data = utils.read_json(self.anim_attr_json_path)
        ctrl_name = const.FACE_CTRL_NAME
        reset_face_anim_attr()
        if cmds.objExists(ctrl_name):
            anim_attr = data.get(face_name)
            if anim_attr:
                for anim_name, anim_attr in anim_attr.items():
                    if cmds.objExists(f"{ctrl_name}.{anim_name}"):
                        cmds.setAttr(f"{ctrl_name}.{anim_name}", anim_attr)
            else:
                self.logger.warning(f"未找到表情: {face_name}")
        else:
            self.logger.warning(f"未找到表情控制器: {ctrl_name}")


def reset_face_anim_attr():
    logger = logging.getLogger(__name__)
    ctrl_name = const.FACE_CTRL_NAME
    if not cmds.objExists(ctrl_name):
        logger.warning(f"未找到表情控制器: {ctrl_name}")
        return False
    keyable_attrs = cmds.listAttr(ctrl_name, keyable=True) or []

    if not keyable_attrs:
        logger.warning(f"表情控制器 {ctrl_name} 没有可关键帧的属性")
        return False
    for attr in keyable_attrs:
        attr_path = f"{ctrl_name}.{attr}"
        if cmds.getAttr(attr_path, settable=True):
            cmds.setAttr(attr_path, 0)

    return True
