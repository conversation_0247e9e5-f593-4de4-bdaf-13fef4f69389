# import third-party modules
# Import third-party modules
import maya.cmds as cmds


def align_object_to_scene_up_axis(object_name, imported_up_axis):
    """Align the up axis of a mesh with the Maya scene coordinate system.

    Args:
        object_name (str/list): Name or list of names of the objects to align
        imported_up_axis (str): Original up axis of the imported mesh ('y' or 'z')
    """
    scene_up_axis = cmds.upAxis(q=True, axis=True).lower()

    if scene_up_axis == imported_up_axis:
        return

    rotation_x = -90 if imported_up_axis == "z" else 90

    if isinstance(object_name, list):
        for obj in object_name:
            cmds.rotate(rotation_x, 0, 0, obj, relative=True, worldSpace=True)
            cmds.makeIdentity(
                obj,
                apply=True,
                translate=True,
                rotate=True,
                scale=True,
                normal=False,
            )
    else:
        cmds.rotate(rotation_x, 0, 0, object_name, relative=True, worldSpace=True)
        cmds.makeIdentity(
            object_name,
            apply=True,
            translate=True,
            rotate=True,
            scale=True,
            normal=False,
        )


def align_point_to_scene_up_axis(point, imported_up_axis):
    """Align a 3D point from imported coordinate system to Maya scene coordinate system.

    This function transforms a point's coordinates based on the difference between
    the imported model's up axis and the Maya scene's up axis. If both axes are the same,
    the original point is returned unchanged.

    Args:
        point (list/tuple or list of list/tuple): A 3D point as [x, y, z] or list of 3D points
        imported_up_axis (str): Original up axis of the imported data ('y' or 'z')

    Returns:
        list or list of list: Transformed point coordinates aligned to the scene's up axis
    """
    scene_up_axis = cmds.upAxis(q=True, axis=True).lower()

    if scene_up_axis == imported_up_axis:
        return point

    if isinstance(point, list) and point and isinstance(point[0], (list, tuple)):
        return [align_point_to_scene_up_axis(p, imported_up_axis) for p in point]

    point_fixed = [point[0], point[2], -point[1]] if imported_up_axis == "z" else [point[0], -point[2], point[1]]

    return point_fixed
