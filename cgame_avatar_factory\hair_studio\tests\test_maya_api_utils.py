"""Test maya_api utils module.

This module tests the Maya API utilities, particularly focusing on
the caching behavior of get_maya_version function.
"""

# Import built-in modules
import logging
import os

# Import the module under test
import sys
import unittest
from unittest.mock import MagicMock
from unittest.mock import patch

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
# Import local modules
from cgame_avatar_factory.hair_studio.maya_api import utils


class TestMayaApiUtils(unittest.TestCase):
    """Test cases for maya_api utils module."""

    def setUp(self):
        """Set up test fixtures."""
        # Reset global variables before each test
        utils._maya_cmds = None
        utils._maya_available = None
        utils._maya_version = utils._UNINITIALIZED

        # Set up logger
        self.logger = logging.getLogger(__name__)

    def tearDown(self):
        """Clean up after each test."""
        # Reset global variables after each test
        utils._maya_cmds = None
        utils._maya_available = None
        utils._maya_version = utils._UNINITIALIZED

    def test_get_maya_version_caching_with_maya_available(self):
        """Test that get_maya_version caches the result when Maya is available."""
        # Mock Maya availability and version
        mock_cmds = MagicMock()
        mock_cmds.about.return_value = "Maya 2023"

        with patch.object(utils, "is_maya_available", return_value=True), patch.object(
            utils,
            "get_maya_cmds",
            return_value=mock_cmds,
        ):
            # First call should trigger version detection
            version1 = utils.get_maya_version()
            self.assertEqual(version1, 2023)
            self.assertEqual(mock_cmds.about.call_count, 1)

            # Second call should use cached value
            version2 = utils.get_maya_version()
            self.assertEqual(version2, 2023)
            # about() should still only be called once
            self.assertEqual(mock_cmds.about.call_count, 1)

            # Third call should also use cached value
            version3 = utils.get_maya_version()
            self.assertEqual(version3, 2023)
            self.assertEqual(mock_cmds.about.call_count, 1)

    def test_get_maya_version_caching_with_maya_unavailable(self):
        """Test that get_maya_version caches None when Maya is unavailable."""
        with patch.object(utils, "is_maya_available", return_value=False):
            # First call should detect Maya unavailable
            version1 = utils.get_maya_version()
            self.assertIsNone(version1)

            # Second call should use cached None value
            version2 = utils.get_maya_version()
            self.assertIsNone(version2)

            # Verify that _maya_version is cached as None
            self.assertIsNone(utils._maya_version)

    def test_get_maya_version_caching_with_parse_error(self):
        """Test that get_maya_version caches None when version parsing fails."""
        mock_cmds = MagicMock()
        mock_cmds.about.return_value = "Invalid Version String"

        with patch.object(utils, "is_maya_available", return_value=True), patch.object(
            utils,
            "get_maya_cmds",
            return_value=mock_cmds,
        ):
            # First call should fail to parse version
            version1 = utils.get_maya_version()
            self.assertIsNone(version1)
            self.assertEqual(mock_cmds.about.call_count, 1)

            # Second call should use cached None value
            version2 = utils.get_maya_version()
            self.assertIsNone(version2)
            # about() should still only be called once
            self.assertEqual(mock_cmds.about.call_count, 1)

    def test_get_maya_version_caching_with_exception(self):
        """Test that get_maya_version caches None when an exception occurs."""
        mock_cmds = MagicMock()
        mock_cmds.about.side_effect = Exception("Maya API error")

        with patch.object(utils, "is_maya_available", return_value=True), patch.object(
            utils,
            "get_maya_cmds",
            return_value=mock_cmds,
        ):
            # First call should encounter exception
            version1 = utils.get_maya_version()
            self.assertIsNone(version1)
            self.assertEqual(mock_cmds.about.call_count, 1)

            # Second call should use cached None value
            version2 = utils.get_maya_version()
            self.assertIsNone(version2)
            # about() should still only be called once
            self.assertEqual(mock_cmds.about.call_count, 1)

    def test_get_maya_version_different_versions(self):
        """Test get_maya_version with different Maya versions."""
        test_cases = [
            ("Maya 2022", 2022),
            ("Maya 2023", 2023),
            ("Maya 2024", 2024),
            ("Autodesk Maya 2025", 2025),
        ]

        for version_string, expected_version in test_cases:
            with self.subTest(version_string=version_string):
                # Reset cache for each test case
                utils._maya_version = utils._UNINITIALIZED

                mock_cmds = MagicMock()
                mock_cmds.about.return_value = version_string

                with patch.object(utils, "is_maya_available", return_value=True), patch.object(
                    utils,
                    "get_maya_cmds",
                    return_value=mock_cmds,
                ):
                    version = utils.get_maya_version()
                    self.assertEqual(version, expected_version)


if __name__ == "__main__":
    # Set up logging for tests
    logging.basicConfig(level=logging.DEBUG)
    unittest.main()
