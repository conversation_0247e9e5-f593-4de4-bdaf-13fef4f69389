"""Test script to verify drag-and-drop functionality between AssetLibrary and ComponentList.

This script tests both the old QGridLayout implementation and the new QListView implementation
to identify why drag-and-drop is broken in the new version.
"""

# Import built-in modules
import json
import logging
import os
import sys
from unittest.mock import Mock

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../.."))
sys.path.insert(0, project_root)

# Import third-party modules
# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager

# Import Hair Studio modules
from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.responsive_asset_library import (
    ResponsiveAssetLibrary,
)
from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList


def create_test_assets():
    """Create test asset data for testing."""
    return [
        {
            "id": "test_asset_1",
            "name": "Test Hair Asset 1",
            "asset_type": "card",
            "file_path": "/path/to/asset1.ma",
            "thumbnail": "/path/to/thumbnail1.jpg",
        },
        {
            "id": "test_asset_2",
            "name": "Test Hair Asset 2",
            "asset_type": "card",
            "file_path": "/path/to/asset2.ma",
            "thumbnail": "/path/to/thumbnail2.jpg",
        },
    ]


def test_new_implementation_drag_drop():
    """Test drag-and-drop functionality in the new ResponsiveAssetLibrary implementation."""
    print("\n=== Testing New Implementation (ResponsiveAssetLibrary) ===")

    try:
        # Create mock manager
        mock_manager = Mock(spec=HairManager)
        mock_manager.get_assets.return_value = create_test_assets()
        mock_manager.create_component.return_value = "test_component_123"

        # Create logger
        logger = logging.getLogger("test_new_impl")
        logger.setLevel(logging.DEBUG)

        # Create widgets
        asset_library = ResponsiveAssetLibrary(
            hair_type="card",
            hair_manager=mock_manager,
        )

        component_list = ComponentList(
            hair_type="card",
            hair_manager=mock_manager,
        )

        # Update assets
        asset_library.updateAssets(create_test_assets())
        print(f"✓ Asset library updated with {len(asset_library.assets)} assets")

        # Test view configuration
        view = asset_library._view
        print(f"✓ View drag enabled: {view.dragEnabled()}")
        print(f"✓ View drag-drop mode: {view.dragDropMode()}")
        print(f"✓ View default drop action: {view.defaultDropAction()}")

        # Test model data
        model = view.model()
        if model:
            row_count = model.rowCount()
            print(f"✓ Model has {row_count} items")

            if row_count > 0:
                # Test first item data
                index = model.createIndex(0, 0)
                # Import local modules
                from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_list_model import (
                    AssetListModel,
                )

                asset_data = index.data(AssetListModel.AssetDataRole)
                print(f"✓ First item asset data: {asset_data.get('name') if asset_data else 'None'}")

                # Test drag functionality
                if hasattr(view, "startDrag"):
                    print("✓ View has startDrag method")

                    # Test drag data creation
                    try:
                        # Simulate drag operation
                        view.setCurrentIndex(index)

                        # Test drag pixmap creation
                        drag_pixmap = view._create_drag_pixmap(asset_data)
                        print(f"✓ Drag pixmap creation: {'Success' if not drag_pixmap.isNull() else 'Failed'}")

                    except Exception as e:
                        print(f"✗ Drag simulation failed: {e}")

                else:
                    print("✗ View missing startDrag method")
        else:
            print("✗ No model found in view")

        # Test component list compatibility (same as old implementation)
        test_mime_data = QtCore.QMimeData()
        test_asset_data = create_test_assets()[0]
        test_mime_data.setText(json.dumps(test_asset_data))
        test_mime_data.setData(
            "application/x-hair-asset",
            QtCore.QByteArray(json.dumps(test_asset_data).encode("utf-8")),
        )

        mock_event = Mock()
        mock_event.mimeData.return_value = test_mime_data
        mock_event.acceptProposedAction = Mock()
        mock_event.ignore = Mock()

        can_accept = component_list._can_accept_drop(mock_event)
        print(f"✓ Component list can accept drop: {can_accept}")

        print("✓ New implementation drag-drop test completed")
        return True

    except Exception as e:
        print(f"✗ New implementation test failed: {e}")
        # Import built-in modules
        import traceback

        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("Testing Hair Studio Drag-and-Drop Functionality")
    print("=" * 60)

    # Create QApplication if needed
    app = QtWidgets.QApplication.instance()
    if app is None:
        app = QtWidgets.QApplication(sys.argv)

    # Run tests
    new_result = test_new_implementation_drag_drop()

    # Summary
    print("\n" + "=" * 60)
    print("DRAG-DROP TEST SUMMARY")
    print("=" * 60)
    print(f"New Implementation (ResponsiveAssetLibrary): {'✓ PASS' if new_result else '✗ FAIL'}")

    if new_result:
        print("\n✅ ANALYSIS: New implementation working correctly")
    else:
        print("\n⚠️  ANALYSIS: Issues detected in new implementation")

    return new_result


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
