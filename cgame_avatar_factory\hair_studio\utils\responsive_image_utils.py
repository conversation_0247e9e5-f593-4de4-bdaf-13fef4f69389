"""Responsive Image Utilities for Hair Studio.

This module provides utilities for responsive image scaling and layout management
in the Hair Studio asset library. It handles dynamic image resizing based on
container dimensions while maintaining aspect ratios and visual consistency.
"""

# Import built-in modules
# Import standard library
import logging

# Import third-party modules
# Import Qt modules
from qtpy import QtCore
from qtpy import QtGui

# Import local modules
from cgame_avatar_factory.hair_studio.constants import ASSET_ITEM_HEIGHT
from cgame_avatar_factory.hair_studio.constants import ASSET_ITEM_WIDTH
from cgame_avatar_factory.hair_studio.constants import ASSET_THUMBNAIL_SIZE
from cgame_avatar_factory.hair_studio.constants import GRID_COLUMNS_MAX
from cgame_avatar_factory.hair_studio.constants import GRID_COLUMNS_MIN
from cgame_avatar_factory.hair_studio.constants import GRID_SPACING_DEFAULT
from cgame_avatar_factory.hair_studio.constants import SPACING_MAX_ABSOLUTE
from cgame_avatar_factory.hair_studio.constants import SPACING_MIN_ABSOLUTE
from cgame_avatar_factory.hair_studio.constants import SPACING_RATIO_DEFAULT
from cgame_avatar_factory.hair_studio.constants import LAYOUT_PADDING_HORIZONTAL
from cgame_avatar_factory.hair_studio.utils.icon_utils import create_square_icon_from_pixmap


class ResponsiveImageProcessor:
    """Handles responsive image processing for asset items."""

    def __init__(self):
        """Initialize the responsive image processor."""
        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Cache for processed images to improve performance
        self._image_cache = {}

        # Base dimensions for scaling calculations
        self._base_thumbnail_size = ASSET_THUMBNAIL_SIZE
        self._base_item_width = ASSET_ITEM_WIDTH
        self._base_item_height = ASSET_ITEM_HEIGHT

    def _calculate_proportional_spacing(self, item_width):
        """Calculate proportional spacing based on item width.

        Args:
            item_width (int): Width of the asset item

        Returns:
            int: Calculated spacing value with proportional constraints
        """
        # Use dynamic spacing ratio based on item size to maintain visual balance
        if item_width <= 80:
            # Larger ratio for small items to ensure minimum visible spacing
            spacing_ratio = 0.20  # 20%
        elif item_width >= 150:
            # Smaller ratio for large items to avoid excessive spacing
            spacing_ratio = 0.12  # 12%
        else:
            # Standard ratio for medium-sized items
            spacing_ratio = SPACING_RATIO_DEFAULT  # 15%

        proportional_spacing = item_width * spacing_ratio

        # Apply absolute constraints
        constrained_spacing = max(
            SPACING_MIN_ABSOLUTE,
            min(SPACING_MAX_ABSOLUTE, int(proportional_spacing)),
        )

        self._logger.debug(
            f"Proportional spacing calculation: item_width={item_width}px, "
            f"ratio={spacing_ratio:.1%}, proportional={proportional_spacing:.1f}px, "
            f"constrained={constrained_spacing}px",
        )

        return constrained_spacing

    def calculate_responsive_dimensions(
        self,
        container_width,
        num_items=1,
        user_scale_factor=None,
    ):
        """Calculate responsive dimensions for asset items based on container width.

        Args:
            container_width (int): Available width of the container
            num_items (int): Number of items to display (for optimal column calculation)
            user_scale_factor (float, optional): User-defined scale factor (currently always 1.0)

        Returns:
            dict: Dictionary containing calculated dimensions:
                - item_width: Width of each asset item
                - item_height: Height of each asset item
                - thumbnail_size: Size of thumbnail images
                - grid_spacing: Spacing between grid items
                - columns: Optimal number of columns
                - scale_factor: Final scale factor applied
        """
        try:
            # Calculate optimal number of columns
            columns = self._calculate_optimal_columns(container_width, num_items)

            # Use provided scale factor or default to 1.0
            scale_factor = user_scale_factor if user_scale_factor is not None else 1.0
            scale_factor = max(0.5, min(2.0, scale_factor))  # Clamp to valid range

            # Calculate responsive dimensions
            responsive_item_width = int(self._base_item_width * scale_factor)
            responsive_item_height = int(self._base_item_height * scale_factor)
            responsive_thumbnail_size = int(self._base_thumbnail_size * scale_factor)

            # Calculate responsive spacing using proportional method
            responsive_spacing = self._calculate_proportional_spacing(
                responsive_item_width,
            )

            dimensions = {
                "item_width": responsive_item_width,
                "item_height": responsive_item_height,
                "thumbnail_size": responsive_thumbnail_size,
                "grid_spacing": responsive_spacing,
                "columns": columns,
                "scale_factor": scale_factor,
            }

            self._logger.debug(
                f"Calculated responsive dimensions: {dimensions} "
                f"(container_width: {container_width}, num_items: {num_items})",
            )

            return dimensions

        except Exception as e:
            self._logger.error(f"Error calculating responsive dimensions: {e}")
            return self._get_default_dimensions()

    def _calculate_optimal_columns(self, container_width, num_items):
        """Calculate optimal number of columns for the given width.

        Args:
            container_width (int): Available container width
            num_items (int): Number of items to display

        Returns:
            int: Optimal number of columns
        """
        if container_width <= 0:
            return GRID_COLUMNS_MIN

        # Account for layout margins (use actual layout margins for consistency)
        layout_margins = LAYOUT_PADDING_HORIZONTAL * 2  # Use same as asset_library.py for consistency
        usable_width = container_width - layout_margins

        # Calculate maximum possible columns using estimated proportional spacing
        # Use base item width to estimate spacing for initial calculation
        estimated_spacing = self._calculate_proportional_spacing(self._base_item_width)
        item_width_with_spacing = self._base_item_width + estimated_spacing
        max_possible = max(
            1,
            (usable_width + estimated_spacing) // item_width_with_spacing,
        )

        # Consider the number of items to avoid empty columns
        if num_items > 0:
            optimal_for_items = min(num_items, max_possible)
        else:
            optimal_for_items = max_possible

        # Apply constraints
        optimal_columns = max(
            GRID_COLUMNS_MIN,
            min(GRID_COLUMNS_MAX, optimal_for_items),
        )

        # Debug logging to help track calculations
        self._logger.debug(
            f"Column calculation: container_width={container_width}, "
            f"usable_width={usable_width}, estimated_spacing={estimated_spacing}, "
            f"item_width_with_spacing={item_width_with_spacing}, "
            f"max_possible={max_possible}, optimal_columns={optimal_columns}",
        )

        return optimal_columns

    def _get_default_dimensions(self):
        """Get default dimensions as fallback.

        Returns:
            dict: Default dimensions dictionary
        """
        return {
            "item_width": self._base_item_width,
            "item_height": self._base_item_height,
            "thumbnail_size": self._base_thumbnail_size,
            "grid_spacing": GRID_SPACING_DEFAULT,
            "columns": 3,
            "scale_factor": 1.0,
        }

    def process_thumbnail_image(self, image_path, target_size, cache_key=None):
        """Process a thumbnail image with responsive sizing and caching.

        Args:
            image_path (str): Path to the source image
            target_size (int): Target size for the square thumbnail
            cache_key (str, optional): Cache key for the processed image

        Returns:
            QtGui.QPixmap: Processed thumbnail pixmap, or None if processing fails
        """
        try:
            # Generate cache key if not provided
            if cache_key is None:
                cache_key = f"{image_path}_{target_size}"

            # Check cache first
            if cache_key in self._image_cache:
                self._logger.debug(f"Using cached image: {cache_key}")
                return self._image_cache[cache_key]

            # Load the source image
            source_pixmap = QtGui.QPixmap(image_path)
            if source_pixmap.isNull():
                self._logger.warning(f"Failed to load image: {image_path}")
                return None

            # Process the image using existing icon_utils functionality
            processed_pixmap = create_square_icon_from_pixmap(
                source_pixmap,
                target_size,
            )

            # Cache the processed image
            self._image_cache[cache_key] = processed_pixmap

            self._logger.debug(
                f"Processed and cached thumbnail: {image_path} -> {target_size}x{target_size}",
            )

            return processed_pixmap

        except Exception as e:
            self._logger.error(f"Error processing thumbnail image {image_path}: {e}")
            return None

    def create_placeholder_thumbnail(self, target_size, text="No Image"):
        """Create a placeholder thumbnail for missing images.

        Args:
            target_size (int): Target size for the square thumbnail
            text (str): Text to display on the placeholder

        Returns:
            QtGui.QPixmap: Placeholder thumbnail pixmap
        """
        try:
            # Create a square pixmap
            pixmap = QtGui.QPixmap(target_size, target_size)
            pixmap.fill(QtGui.QColor("#3E3E3E"))  # Dark gray background

            # Draw placeholder content
            painter = QtGui.QPainter(pixmap)
            painter.setRenderHint(QtGui.QPainter.Antialiasing)

            # Draw border
            painter.setPen(QtGui.QPen(QtGui.QColor("#5E5E5E"), 1))
            painter.drawRect(0, 0, target_size - 1, target_size - 1)

            # Draw text
            painter.setPen(QtGui.QColor("#CCCCCC"))
            font = painter.font()
            font.setPointSize(max(8, target_size // 10))
            painter.setFont(font)

            text_rect = QtCore.QRect(0, 0, target_size, target_size)
            painter.drawText(text_rect, QtCore.Qt.AlignCenter, text)

            painter.end()

            return pixmap

        except Exception as e:
            self._logger.error(f"Error creating placeholder thumbnail: {e}")
            # Return a simple colored square as ultimate fallback
            fallback_pixmap = QtGui.QPixmap(target_size, target_size)
            fallback_pixmap.fill(QtGui.QColor("#4080FF"))
            return fallback_pixmap

    def clear_cache(self):
        """Clear the image cache to free memory."""
        self._image_cache.clear()
        self._logger.debug("Image cache cleared")

    def get_cache_size(self):
        """Get the current size of the image cache.

        Returns:
            int: Number of cached images
        """
        return len(self._image_cache)


# Global instance for shared use
_responsive_processor = None


def get_responsive_processor():
    """Get the global responsive image processor instance.

    Returns:
        ResponsiveImageProcessor: Global processor instance
    """
    global _responsive_processor
    if _responsive_processor is None:
        _responsive_processor = ResponsiveImageProcessor()
    return _responsive_processor


def calculate_responsive_layout(container_width, num_items=1, user_scale_factor=None):
    """Convenience function to calculate responsive layout dimensions.

    Args:
        container_width (int): Available width of the container
        num_items (int): Number of items to display
        user_scale_factor (float, optional): User-defined scale factor (currently always 1.0)

    Returns:
        dict: Dictionary containing calculated dimensions
    """
    processor = get_responsive_processor()
    return processor.calculate_responsive_dimensions(
        container_width,
        num_items,
        user_scale_factor,
    )


def process_asset_thumbnail(image_path, target_size, cache_key=None):
    """Convenience function to process an asset thumbnail image.

    Args:
        image_path (str): Path to the source image
        target_size (int): Target size for the square thumbnail
        cache_key (str, optional): Cache key for the processed image

    Returns:
        QtGui.QPixmap: Processed thumbnail pixmap, or None if processing fails
    """
    processor = get_responsive_processor()
    return processor.process_thumbnail_image(image_path, target_size, cache_key)
