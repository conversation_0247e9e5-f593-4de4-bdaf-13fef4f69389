#!/usr/bin/env python
"""Simple test for AssetGridView - minimal version for debugging."""

# Import built-in modules
import os
import sys

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def test_basic_imports():
    """Test basic imports first."""
    print("=== Testing Basic Imports ===")

    try:
        print("1. Testing Qt imports...")

        print("   ✓ QtPy imports successful")

        print("2. Testing constants import...")
        # Import local modules
        from cgame_avatar_factory.hair_studio.constants import ASSET_ITEM_WIDTH
        from cgame_avatar_factory.hair_studio.constants import ASSET_ITEM_HEIGHT
        from cgame_avatar_factory.hair_studio.constants import GRID_SPACING_DEFAULT

        print(
            f"   ✓ Constants loaded: {ASSET_ITEM_WIDTH}x{ASSET_ITEM_HEIGHT}, spacing={GRID_SPACING_DEFAULT}",
        )

        print("3. Testing model import...")

        print("   ✓ AssetListModel import successful")

        print("4. Testing view import...")

        print("   ✓ AssetGridView import successful")

        return True

    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        # Import built-in modules
        import traceback

        traceback.print_exc()
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        # Import built-in modules
        import traceback

        traceback.print_exc()
        return False


def test_asset_grid_view():
    """Test AssetGridView functionality."""
    print("\n=== Testing AssetGridView ===")

    try:
        # Import after basic test passes
        # Import third-party modules
        from qtpy import QtWidgets

        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_grid_view import (
            AssetGridView,
        )
        from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_list_model import (
            AssetListModel,
        )

        print("1. Creating QApplication...")
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication([])
        print("   ✓ QApplication ready")

        print("2. Testing AssetGridView creation...")
        view = AssetGridView()
        print("   ✓ AssetGridView created")

        print("3. Testing view configuration...")
        assert view.viewMode() == QtWidgets.QListView.IconMode, "View mode should be IconMode"
        assert view.dragEnabled() is True, "Drag should be enabled"
        assert view.uniformItemSizes() is True, "Uniform item sizes should be enabled"
        print("   ✓ View configuration correct")

        print("4. Testing scale functionality...")
        view.setScale(1.5)
        assert view.getScale() == 1.5, f"Scale should be 1.5, got {view.getScale()}"

        view.setScale(3.0)  # Should clamp to 2.0
        assert view.getScale() == 2.0, f"Scale should be clamped to 2.0, got {view.getScale()}"
        print("   ✓ Scale functionality works")

        print("5. Testing with model...")
        model = AssetListModel()
        test_assets = [
            {"id": "test1", "name": "Test Asset 1", "asset_type": "card"},
            {"id": "test2", "name": "Test Asset 2", "asset_type": "xgen"},
        ]
        model.setAssets(test_assets)
        view.setModel(model)
        print("   ✓ Model integration works")

        print("6. Testing selection...")
        result = view.selectAssetById("test1")
        assert result is True, "Should be able to select asset by ID"

        selected = view.getSelectedAsset()
        assert selected is not None, "Should have selected asset"
        assert selected["id"] == "test1", f"Selected asset should be test1, got {selected['id']}"
        print("   ✓ Selection functionality works")

        print("7. Testing drag pixmap creation...")
        test_asset = {
            "id": "test",
            "name": "Test Asset",
            "thumbnail": "/nonexistent.jpg",
        }
        pixmap = view._create_drag_pixmap(test_asset)
        assert not pixmap.isNull(), "Drag pixmap should be created"
        assert pixmap.width() > 0 and pixmap.height() > 0, "Pixmap should have valid dimensions"
        print("   ✓ Drag pixmap creation works")

        print("\n🎉 All AssetGridView tests passed!")
        return True

    except AssertionError as e:
        print(f"   ❌ Test assertion failed: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        # Import built-in modules
        import traceback

        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("=" * 50)
    print("AssetGridView Test")
    print("=" * 50)

    # Test imports first
    import_success = test_basic_imports()
    if not import_success:
        print("\n" + "=" * 50)
        print("❌ Basic imports failed - cannot proceed")
        print("=" * 50)
        return False

    # Test functionality
    functionality_success = test_asset_grid_view()

    print("\n" + "=" * 50)
    if functionality_success:
        print("✅ AssetGridView implementation is working correctly!")
        print("✅ Ready to proceed to AssetItemDelegate")
    else:
        print("❌ AssetGridView tests failed - please fix issues")
    print("=" * 50)

    return functionality_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
