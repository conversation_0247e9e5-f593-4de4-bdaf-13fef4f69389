"""半自动化Wrap标签页UI组件

提供模型wrap处理和贴图处理功能的用户界面
"""
# Import third-party modules
import dayu_widgets
from dayu_widgets import MCollapse
from maya import cmds
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
import cgame_avatar_factory.common.constants as const
from cgame_avatar_factory.common.ui.layout import FramelessVLayout
from cgame_avatar_factory.common.ui.mixin.style_mixin import MStyleMixin
from cgame_avatar_factory.face_sculpting_center import constants as face_const
from cgame_avatar_factory.face_sculpting_center.ui.automatic_wrap.bake_preview_widget import BakePreviewWidget
from cgame_avatar_factory.face_sculpting_center.ui.automatic_wrap.export_dialog import ExportDialog
from cgame_avatar_factory.face_sculpting_center.ui.automatic_wrap.model_wrap_widget import ModelWrapWidget


class SemiAutoWrapTab(QtWidgets.QWidget):
    """半自动化Wrap标签页"""

    DEFAULT_SPLITTER_SIZES = [400, 400]

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """设置UI布局"""
        main_layout = QtWidgets.QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(0)

        splitter = self._create_splitter()
        main_layout.addWidget(splitter)

    def _create_splitter(self):
        """创建分割器组件"""
        splitter = QtWidgets.QSplitter(QtCore.Qt.Horizontal)
        splitter.setChildrenCollapsible(False)

        left_panel = self._create_left_panel()
        right_panel = self._create_right_panel()

        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes(self.DEFAULT_SPLITTER_SIZES)

        return splitter

    def _create_left_panel(self):
        """创建左侧面板"""
        panel = QtWidgets.QFrame()
        panel.setFrameShape(QtWidgets.QFrame.NoFrame)

        layout = FramelessVLayout()
        layout.setContentsMargins(5, 5, 0, 5)
        panel.setLayout(layout)

        collapse_widget = self._create_collapse_widget()
        layout.addWidget(collapse_widget)

        layout.addStretch(1)

        bottom_container = self._create_bottom_button_container()
        layout.addWidget(bottom_container)

        return panel

    def _create_bottom_button_container(self):
        self.bottom_canvas = QtWidgets.QLabel()
        self.bottom_canvas.setFixedHeight(60)
        self.bottom_canvas.setAlignment(QtCore.Qt.AlignCenter)
        self.bottom_canvas.setStyleSheet(
            f"""
            QLabel {{
                border: 2px solid {const.BUTTON_BORDER};
                background-color: {const.DAYU_BG_IN_COLOR};
                color: {const.APP_PRIMARY_COLOR};
            }}
        """
        )

        self.bottom_button = MStyleMixin.instance_wrapper(dayu_widgets.MToolButton(parent=self.bottom_canvas))
        self.bottom_button.text_beside_icon().svg("export.svg").small()
        self.bottom_button.setText(" 导出")
        self.bottom_button.setCursor(QtCore.Qt.PointingHandCursor)
        self.bottom_button.setStyleSheet(
            f"""
            QToolButton {{
                background-color: {const.APP_PRIMARY_COLOR};
            }}
            QToolButton:pressed {{
                background-color: {const.DAYU_PRIMARY_7};
            }}
        """
        )

        self.bottom_button.clicked.connect(self._on_bottom_button_clicked)

        self.bottom_button.show()
        QtCore.QTimer.singleShot(0, self._position_button)
        self.bottom_canvas.resizeEvent = self._on_canvas_resize

        return self.bottom_canvas

    def _position_button(self):
        """定位按钮到画布右侧"""
        if hasattr(self, "bottom_button") and hasattr(self, "bottom_canvas"):
            canvas_width = self.bottom_canvas.width()
            canvas_height = self.bottom_canvas.height()
            button_width = self.bottom_button.sizeHint().width()
            button_height = self.bottom_button.sizeHint().height()

            button_x = canvas_width - button_width - 10
            button_y = (canvas_height - button_height) // 2

            self.bottom_button.move(button_x, button_y)

    def _on_canvas_resize(self, event):
        """画布大小变化时调整按钮位置"""
        self._position_button()

    def _create_collapse_widget(self):
        """创建折叠组件"""
        collapse = MCollapse()

        self.model_wrap_widget = ModelWrapWidget()

        sections = [
            {"title": "模型wrap处理", "expand": True, "widget": self.model_wrap_widget},
            {"title": "睫毛匹配", "expand": True, "widget": QtWidgets.QWidget()},
        ]

        collapse.add_section_list(sections)
        return collapse

    def _create_right_panel(self):
        """创建右侧面板"""
        self.bake_preview_widget = BakePreviewWidget()
        self.bake_preview_widget.set_model_wrap_widget(self.model_wrap_widget)
        return self.bake_preview_widget

    def _on_bottom_button_clicked(self):
        """底部按钮点击回调"""
        if cmds.objExists(face_const.TARGET_TOPO_GRP_NAME):
            export_dialog = ExportDialog(self)
            export_dialog.exec_()
