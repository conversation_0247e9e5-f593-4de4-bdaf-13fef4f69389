"""Test Sub-Type Logic with Mock Data.

This module tests the core sub-type logic using mock data instead of real data.
"""

# Import built-in modules
import os
import sys
import unittest
from unittest.mock import MagicMock

# Add the project root to Python path for testing
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


class TestSubTypeLogic(unittest.TestCase):
    """Test cases for sub-type logic with mock data."""

    def setUp(self):
        """Set up test environment with mock data."""
        # Mock function that simulates _determine_sub_asset_type_from_path behavior
        def mock_determine_sub_asset_type(path):
            path_lower = path.lower()
            # Check for specific keywords in order of specificity
            # Use path separators to find the actual folder names
            path_parts = path_lower.replace("\\", "/").split("/")

            # Check for Chinese keywords first
            if "眉毛" in path:
                return "eyebrow"
            elif "胡子" in path:
                return "beard"

            # Check for English keywords in path parts
            for part in path_parts:
                if part == "eyebrow":
                    return "eyebrow"
                elif part == "beard":
                    return "beard"
                elif part == "hair" and "hair_lib" not in part:  # Avoid matching "hair_lib"
                    return "hair"

            # Fallback: check if any part contains the keywords
            if any("eyebrow" in part for part in path_parts):
                return "eyebrow"
            elif any("beard" in part for part in path_parts):
                return "beard"
            elif any("hair" in part and "hair_lib" not in part for part in path_parts):
                return "hair"

            return "hair"  # Default fallback

        self.mock_determine_function = mock_determine_sub_asset_type

    def test_sub_type_path_detection_hair(self):
        """Test hair sub-type detection from file paths using mock function."""
        test_cases = [
            (r"U:\hair_lib\card_lib\hair\long_hair\model.fbx", "hair"),
            (r"D:\personal\hair_lib\hair\short_hair\model.ma", "hair"),
            (r"C:\project\assets\hair\curly\model.obj", "hair"),
            (r"/unix/path/hair/straight/model.fbx", "hair"),
        ]

        for path, expected in test_cases:
            with self.subTest(path=path):
                result = self.mock_determine_function(path)
                self.assertEqual(result, expected, f"Path: {path}")

    def test_sub_type_path_detection_eyebrow(self):
        """Test eyebrow sub-type detection from file paths using mock function."""
        test_cases = [
            (r"U:\hair_lib\card_lib\eyebrow\thick_eyebrow\model.fbx", "eyebrow"),
            (r"D:\personal\hair_lib\eyebrow\thin\model.ma", "eyebrow"),
            (r"C:\project\assets\eyebrow\arched\model.obj", "eyebrow"),
            (r"/unix/path/eyebrow/bushy/model.fbx", "eyebrow"),
            (r"C:\test\眉毛\model.fbx", "eyebrow"),  # Chinese keyword
        ]

        for path, expected in test_cases:
            with self.subTest(path=path):
                result = self.mock_determine_function(path)
                self.assertEqual(result, expected, f"Path: {path}")

    def test_sub_type_path_detection_beard(self):
        """Test beard sub-type detection from file paths using mock function."""
        test_cases = [
            (r"U:\hair_lib\card_lib\beard\full_beard\model.fbx", "beard"),
            (r"D:\personal\hair_lib\beard\goatee\model.ma", "beard"),
            (r"C:\project\assets\beard\mustache\model.obj", "beard"),
            (r"/unix/path/beard/stubble/model.fbx", "beard"),
            (r"C:\test\胡子\model.fbx", "beard"),  # Chinese keyword
        ]

        for path, expected in test_cases:
            with self.subTest(path=path):
                result = self.mock_determine_function(path)
                self.assertEqual(result, expected, f"Path: {path}")

    def test_sub_type_path_detection_default(self):
        """Test default fallback to hair for unknown paths using mock function."""
        test_cases = [
            (r"C:\unknown\path\model.fbx", "hair"),
            (r"D:\random\folder\asset.ma", "hair"),
            (r"/some/random/path/model.obj", "hair"),
            (r"", "hair"),  # Empty path
        ]

        for path, expected in test_cases:
            with self.subTest(path=path):
                result = self.mock_determine_function(path)
                self.assertEqual(result, expected, f"Path: {path}")

    def test_sub_type_path_case_insensitive(self):
        """Test that path detection is case-insensitive using mock function."""
        test_cases = [
            (r"U:\HAIR_LIB\CARD_LIB\HAIR\MODEL.FBX", "hair"),
            (r"u:\hair_lib\card_lib\eyebrow\model.fbx", "eyebrow"),
            (r"U:\Hair_Lib\Card_Lib\Beard\Model.Fbx", "beard"),
        ]

        for path, expected in test_cases:
            with self.subTest(path=path):
                result = self.mock_determine_function(path)
                self.assertEqual(result, expected, f"Path: {path}")

    def test_sub_type_path_mixed_separators(self):
        """Test that both Windows and Unix path separators work using mock function."""
        test_cases = [
            (r"U:\hair_lib\card_lib\hair/long_hair\model.fbx", "hair"),
            (r"U:/hair_lib/card_lib/eyebrow\thick\model.fbx", "eyebrow"),
            (r"U:\hair_lib/card_lib\beard/full/model.fbx", "beard"),
        ]

        for path, expected in test_cases:
            with self.subTest(path=path):
                result = self.mock_determine_function(path)
                self.assertEqual(result, expected, f"Path: {path}")

    def test_asset_data_structure(self):
        """Test that the expected asset data structure is supported using mock function."""
        # This test verifies that our sub-type logic works with the expected asset structure
        sample_asset = {
            "id": "card_001",
            "name": "Test Hair Asset",
            "asset_type": "card",
            "sub_asset_type": "hair",  # This field should be added by our logic
            "file_path": r"U:\hair_lib\card_lib\hair\long_hair\model.fbx",
            "thumbnail": r"U:\hair_lib\card_lib\hair\long_hair\thumbnail.jpg",
            "metadata": {
                "file_path": r"U:\hair_lib\card_lib\hair\long_hair\model.fbx",
                "reference": r"U:\hair_lib\card_lib\hair\reference.fbx",
            },
        }

        # Verify the structure
        self.assertIn("sub_asset_type", sample_asset)
        self.assertEqual(sample_asset["sub_asset_type"], "hair")

        # Verify that our mock function would produce the same result
        detected_type = self.mock_determine_function(sample_asset["file_path"])
        self.assertEqual(detected_type, sample_asset["sub_asset_type"])

    def test_optimized_asset_loading_with_known_subtype(self):
        """Test the optimized asset loading that uses pre-determined sub-types."""
        # Mock asset data with known sub-types (simulating optimized loading)
        mock_assets_with_subtypes = [
            {
                "id": "hair_001",
                "name": "Long Hair",
                "asset_type": "card",
                "sub_asset_type": "hair",  # Pre-determined, no path parsing needed
                "file_path": "/mock/path/hair/long_hair.fbx",
                "thumbnail": "/mock/path/hair/long_hair.jpg",
            },
            {
                "id": "eyebrow_001",
                "name": "Thick Eyebrow",
                "asset_type": "card",
                "sub_asset_type": "eyebrow",  # Pre-determined, no path parsing needed
                "file_path": "/mock/path/eyebrow/thick.fbx",
                "thumbnail": "/mock/path/eyebrow/thick.jpg",
            },
        ]

        # Test that assets already have correct sub-types
        for asset in mock_assets_with_subtypes:
            self.assertIn("sub_asset_type", asset)
            self.assertIsInstance(asset["sub_asset_type"], str)
            self.assertIn(asset["sub_asset_type"], ["hair", "eyebrow", "beard"])

    def test_legacy_path_parsing_fallback(self):
        """Test legacy path parsing as fallback for non-optimized loading."""
        # Test cases for legacy path parsing (when sub-type is not pre-determined)
        test_cases = [
            ("/path/with/hair/in/it/asset.fbx", "hair"),
            ("/path/with/eyebrow/folder/asset.fbx", "eyebrow"),
            ("/path/with/beard/directory/asset.fbx", "beard"),
            ("/path/with/眉毛/chinese/asset.fbx", "eyebrow"),
            ("/path/with/胡子/chinese/asset.fbx", "beard"),
            ("/unknown/random/path/asset.fbx", "hair"),  # Default fallback
        ]

        for path, expected in test_cases:
            with self.subTest(path=path):
                result = self.mock_determine_function(path)
                self.assertEqual(result, expected, f"Path: {path}")

    def test_integration_with_new_config_system(self):
        """Test integration with the new AssetLibConfig system using pure mock."""
        # Mock the entire config system without importing real modules
        mock_config = MagicMock()
        mock_config.get_tab_keys.return_value = ["hair", "eyebrow", "beard"]
        mock_config.get_tab_lib_paths.side_effect = lambda key: {
            "hair": ["/mock/hair/path1", "/mock/hair/path2"],
            "eyebrow": ["/mock/eyebrow/path1"],
            "beard": ["/mock/beard/path1"],
        }.get(key, [])

        # Test that mock config system provides sub-type information
        tab_keys = mock_config.get_tab_keys()
        self.assertIn("hair", tab_keys)
        self.assertIn("eyebrow", tab_keys)
        self.assertIn("beard", tab_keys)

        # Test that each tab has paths (even if mocked)
        for tab_key in tab_keys:
            paths = mock_config.get_tab_lib_paths(tab_key)
            self.assertIsInstance(paths, list)

        # Test that the mock config provides the expected structure
        self.assertEqual(len(tab_keys), 3)

        # Test specific path counts
        hair_paths = mock_config.get_tab_lib_paths("hair")
        self.assertEqual(len(hair_paths), 2)

        eyebrow_paths = mock_config.get_tab_lib_paths("eyebrow")
        self.assertEqual(len(eyebrow_paths), 1)

        beard_paths = mock_config.get_tab_lib_paths("beard")
        self.assertEqual(len(beard_paths), 1)

    def test_new_asset_loading_workflow(self):
        """Test the new optimized asset loading workflow."""
        # Simulate the new loading workflow:
        # 1. AssetLibConfig provides paths organized by sub-type
        # 2. Assets are loaded with pre-determined sub-types
        # 3. No path parsing needed during asset creation

        # Mock the new workflow
        mock_config_data = {
            "hair": {
                "paths": ["/mock/hair/path1", "/mock/hair/path2"],
                "display_text": "头发",
            },
            "eyebrow": {
                "paths": ["/mock/eyebrow/path1"],
                "display_text": "眉毛",
            },
            "beard": {
                "paths": ["/mock/beard/path1"],
                "display_text": "胡子",
            },
        }

        # Simulate loading assets with known sub-types
        def mock_load_assets_with_subtype(path, asset_type, sub_type):
            """Mock function simulating optimized asset loading."""
            return [
                {
                    "id": f"{sub_type}_001",
                    "name": f"Mock {sub_type.title()} Asset",
                    "asset_type": asset_type,
                    "sub_asset_type": sub_type,  # Pre-determined, efficient!
                    "file_path": f"{path}/asset.fbx",
                    "thumbnail": f"{path}/asset.jpg",
                },
            ]

        # Test the workflow
        all_assets = []
        for sub_type, config in mock_config_data.items():
            for path in config["paths"]:
                assets = mock_load_assets_with_subtype(path, "card", sub_type)
                all_assets.extend(assets)

        # Verify results
        self.assertEqual(len(all_assets), 4)  # 2 hair + 1 eyebrow + 1 beard

        # Verify all assets have correct structure
        for asset in all_assets:
            self.assertIn("sub_asset_type", asset)
            self.assertIn(asset["sub_asset_type"], ["hair", "eyebrow", "beard"])
            self.assertEqual(asset["asset_type"], "card")

        # Verify sub-type distribution
        hair_assets = [a for a in all_assets if a["sub_asset_type"] == "hair"]
        eyebrow_assets = [a for a in all_assets if a["sub_asset_type"] == "eyebrow"]
        beard_assets = [a for a in all_assets if a["sub_asset_type"] == "beard"]

        self.assertEqual(len(hair_assets), 2)  # 2 paths for hair
        self.assertEqual(len(eyebrow_assets), 1)  # 1 path for eyebrow
        self.assertEqual(len(beard_assets), 1)  # 1 path for beard


def run_tests():
    """Run the test suite."""
    print("Testing Sub-Type Logic (No Qt Dependencies)...")

    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestSubTypeLogic)

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    if result.wasSuccessful():
        print(f"\n✅ All {result.testsRun} tests passed!")
        print("✅ Sub-type path detection logic is working correctly!")
    else:
        print(f"\n❌ {len(result.failures)} failures, {len(result.errors)} errors")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
            print(f"  {failure[1]}")
        for error in result.errors:
            print(f"ERROR: {error[0]}")
            print(f"  {error[1]}")

    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
