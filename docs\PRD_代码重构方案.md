# 角色工厂代码重构方案

## 文档信息

| 信息 | 详情 |
|------|------|
| 文档类型 | 产品需求文档（PRD） |
| 版本 | 1.0 |
| 创建日期 | 2025-04-02 |
| 状态 | 草稿 |
| 作者 | AI助手 |

## 1. 概述

### 1.1 背景

当前角色工厂项目代码存在多处代码味道，包括职责过多、硬编码、重复代码、紧耦合等问题，影响了代码的可维护性和可扩展性。特别是在处理镜像状态和区域选择时，逻辑分散且缺乏清晰的抽象。

### 1.2 目标

通过重构改善代码质量，提高可维护性和可扩展性，同时保持现有功能不变。重点解决以下问题：

1. 分离UI和业务逻辑
2. 优化类的职责和大小
3. 消除硬编码和魔法常量
4. 减少重复代码
5. 改进错误处理和日志记录
6. 增强文档和注释

## 2. 代码味道分析

### 2.1 职责过多和类过大

**问题**：
- `MainWindow` 和 `BaseMergePage` 类过于庞大，承担了太多职责
- `MainWindow` 包含了UI初始化、状态管理、事件处理等多种职责
- `BaseMergePage` 同时处理UI组件、权重计算、区域选择和镜像逻辑

**影响**：
- 代码难以理解和维护
- 修改一个功能可能影响其他功能
- 测试困难

### 2.2 硬编码和魔法常量

**问题**：
- `constants.py` 中存在大量硬编码常量，特别是UI相关的字符串和配置
- 区域名称和镜像区域配置使用了临时的硬编码列表（`const.MOCK_AREAS_NAME`、`MOCK_MIRROR_AREAS_NAME`）

**影响**：
- 代码灵活性差
- 难以适应需求变化
- 国际化困难

### 2.3 重复代码

**问题**：
- UI组件初始化和样式设置存在重复代码
- 权重更新和区域选择逻辑在多个类中有相似实现

**影响**：
- 维护成本高
- 修改一处需要修改多处
- 容易引入不一致性

### 2.4 紧耦合

**问题**：
- UI层和业务逻辑紧密耦合，如 `BaseMergePage` 直接调用 `SceneCall`
- 镜像状态管理在UI和业务逻辑之间缺乏清晰的分离

**影响**：
- 组件难以单独测试
- 难以替换或修改单个组件
- 代码重用困难

### 2.5 缺乏适当的抽象和接口

**问题**：
- 缺少清晰的接口定义，使得组件之间的交互复杂
- 区域选择和权重更新的逻辑分散在多个类中

**影响**：
- 代码结构不清晰
- 功能扩展困难
- 理解成本高

### 2.6 状态管理混乱

**问题**：
- 镜像状态和区域选择状态在多个类中被管理和修改
- "all"区域的特殊处理逻辑分散在不同位置

**影响**：
- 状态不一致风险高
- 调试困难
- 功能变更容易引入bug

### 2.7 错误处理不足

**问题**：
- 许多方法缺乏适当的错误处理和边界检查
- 日志记录不一致，有些地方详细，有些地方缺失

**影响**：
- 系统健壮性差
- 错误难以定位
- 用户体验不佳

### 2.8 文档不足

**问题**：
- 部分关键方法和类缺乏详细文档
- 特别是关于镜像状态和"all"区域处理的复杂逻辑缺乏清晰说明

**影响**：
- 新开发人员上手困难
- 维护成本高
- 知识传递困难

## 3. 重构方案

### 3.1 架构重构

#### 3.1.1 引入MVC模式

将现有代码重构为更清晰的MVC架构：

**Model（数据模型）**：
- 创建 `MergeModel` 类，负责管理所有与融合相关的数据
- 创建 `AreaModel` 类，封装区域数据和镜像关系
- 创建 `WeightModel` 类，封装权重数据和计算逻辑

**View（视图）**：
- 重构 `MainWindow` 和 `BaseMergePage`，仅负责UI渲染
- 将UI组件抽取为独立的小组件类
- 视图只负责展示数据和捕获用户输入

**Controller（控制器）**：
- 创建 `MergeController` 类，处理融合逻辑
- 创建 `AreaController` 类，处理区域选择和镜像逻辑
- 控制器负责协调模型和视图

#### 3.1.2 引入观察者模式

- 使用信号槽机制实现观察者模式
- 模型状态变化时通知视图更新
- 减少组件间的直接依赖

### 3.2 类职责重构

#### 3.2.1 拆分大类

**MainWindow 拆分**：
- `MainWindowUI`：负责主窗口UI初始化
- `MainWindowController`：负责主窗口逻辑控制
- `SettingsManager`：负责设置管理

**BaseMergePage 拆分**：
- `MergePageUI`：负责融合页面UI
- `MergePageController`：负责融合页面逻辑
- `AreaSelector`：负责区域选择
- `WeightManager`：负责权重管理

#### 3.2.2 提取通用组件

- 创建 `UIComponentFactory` 类，统一创建UI组件
- 创建 `StyleManager` 类，统一管理样式
- 创建 `DialogFactory` 类，统一创建对话框

### 3.3 数据管理重构

#### 3.3.1 区域管理

- 创建 `AreaRegistry` 类，集中管理所有区域定义
- 使用配置文件替代硬编码区域列表
- 实现区域之间的关系映射（如镜像关系）

#### 3.3.2 权重管理

- 创建 `WeightRegistry` 类，集中管理所有权重数据
- 实现权重计算和分发的统一接口
- 为"all"区域实现专门的处理逻辑

#### 3.3.3 状态管理

- 创建 `StateManager` 类，集中管理应用状态
- 使用状态模式处理不同状态下的行为变化
- 提供状态变化的监听机制

### 3.4 接口设计

#### 3.4.1 核心接口

```python
# 区域管理接口
class IAreaManager:
    def get_areas(self, mirror_enabled: bool) -> list:
        """获取区域列表"""
        pass

    def get_mirror_areas(self, area: str) -> list:
        """获取镜像区域"""
        pass

    def is_mirror_area(self, area: str) -> bool:
        """判断是否为镜像区域"""
        pass

# 权重管理接口
class IWeightManager:
    def update_weights(self, areas: list, weights: list, mirror_enabled: bool) -> None:
        """更新权重"""
        pass

    def get_weights(self, area: str) -> list:
        """获取区域权重"""
        pass

    def reset_weights(self) -> None:
        """重置所有权重"""
        pass

# 场景调用接口
class ISceneCall:
    def update_scene(self, area: str, weights: list) -> None:
        """更新场景"""
        pass

    def reset_scene(self) -> None:
        """重置场景"""
        pass
```

#### 3.4.2 事件接口

```python
# 事件定义
class MergeEvents:
    WEIGHTS_CHANGED = "weights_changed"
    AREA_CHANGED = "area_changed"
    MIRROR_CHANGED = "mirror_changed"
    COMPONENTS_CHANGED = "components_changed"

# 事件监听接口
class IEventListener:
    def on_event(self, event_type: str, data: dict) -> None:
        """事件处理"""
        pass
```

### 3.5 错误处理和日志

- 实现统一的异常处理机制
- 定义自定义异常类型
- 统一日志格式和级别
- 增加关键操作的日志记录

### 3.6 配置管理

- 将硬编码常量移至配置文件
- 实现配置加载和验证机制
- 支持运行时配置修改

## 4. 实施计划

### 4.1 阶段划分

**阶段一：基础重构（2周）**
- 设计新架构和接口
- 创建核心模型类
- 实现基本的MVC分离

**阶段二：组件重构（3周）**
- 重构UI组件
- 实现新的控制器类
- 优化数据流和状态管理

**阶段三：功能优化（2周）**
- 完善错误处理
- 增强日志记录
- 改进配置管理

**阶段四：测试和文档（1周）**
- 编写单元测试
- 完善文档
- 性能测试和优化

### 4.2 优先级

1. **高优先级**
   - 分离UI和业务逻辑
   - 重构区域和权重管理
   - 改进错误处理

2. **中优先级**
   - 优化类结构
   - 实现配置管理
   - 增强日志记录

3. **低优先级**
   - 代码风格统一
   - 性能优化
   - 文档完善

### 4.3 风险管理

| 风险 | 影响 | 缓解措施 |
|------|------|---------|
| 功能回归 | 高 | 增加单元测试覆盖率，实施渐进式重构 |
| 性能下降 | 中 | 进行性能基准测试，优化关键路径 |
| 开发延期 | 中 | 合理规划里程碑，预留缓冲时间 |
| 团队适应 | 低 | 提供培训和文档，进行代码评审 |

## 5. 测试策略

### 5.1 单元测试

- 为所有新类和方法编写单元测试
- 使用模拟对象隔离依赖
- 测试覆盖率目标：80%以上

### 5.2 集成测试

- 测试组件间交互
- 测试数据流和状态变化
- 测试边界条件和错误处理

### 5.3 UI测试

- 测试UI响应和渲染
- 测试用户交互流程
- 测试不同分辨率和DPI

## 6. 文档计划

### 6.1 代码文档

- 为所有类和公共方法添加文档字符串
- 说明参数、返回值和异常
- 提供使用示例

### 6.2 架构文档

- 描述整体架构和设计原则
- 说明组件职责和交互
- 提供类图和序列图

### 6.3 开发指南

- 编码规范和最佳实践
- 常见问题解答
- 调试和测试指南

## 7. 附录

### 7.1 术语表

| 术语 | 定义 |
|------|------|
| 区域（Area） | 角色面部的可编辑区域，如鼻子、眼睛等 |
| 权重（Weight） | 控制融合程度的数值，范围0-1 |
| 镜像（Mirror） | 左右对称区域同步更新的功能 |
| 融合（Merge） | 将多个DNA特征按权重混合的过程 |

### 7.2 参考资料

- 设计模式：《Head First设计模式》
- Python最佳实践：《Fluent Python》
- Qt开发：《PyQt5 Programming》
- Maya开发：《Maya Python API文档》
