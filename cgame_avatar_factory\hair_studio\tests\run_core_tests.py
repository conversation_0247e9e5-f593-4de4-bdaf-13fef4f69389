#!/usr/bin/env python
"""Core Test Runner for Hair Studio.

Runs all core functionality tests to ensure the system works correctly
after configuration changes and optimizations.
"""

# Import built-in modules
import os
import sys
import time
import unittest

# Add the project root to Python path for testing
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def run_all_core_tests():
    """Run all core functionality tests."""
    print("=" * 80)
    print("HAIR STUDIO CORE FUNCTIONALITY TESTS")
    print("=" * 80)
    print()

    start_time = time.time()
    all_results = []

    # Test modules to run
    test_modules = [
        ("Constants Configuration", "test_constants_config"),
        ("AssetLibConfig Core", "test_asset_lib_config_core"),
        ("Data Manager Core", "test_data_manager_core"),
        ("Sub-Tab Switching", "test_sub_tab_switching"),
        ("Sub-Type Logic", "test_sub_type_logic"),
    ]

    for test_name, module_name in test_modules:
        print(f"Running {test_name} Tests...")
        print("-" * 60)

        try:
            # Import and run the test module
            module = __import__(
                f"cgame_avatar_factory.hair_studio.tests.{module_name}",
                fromlist=[module_name],
            )

            if hasattr(module, "run_tests"):
                success = module.run_tests()
                all_results.append((test_name, success))
            else:
                # Fallback to standard unittest discovery
                loader = unittest.TestLoader()
                suite = loader.loadTestsFromModule(module)
                runner = unittest.TextTestRunner(verbosity=1)
                result = runner.run(suite)
                success = result.wasSuccessful()
                all_results.append((test_name, success))

        except Exception as e:
            print(f"❌ Error running {test_name} tests: {e}")
            all_results.append((test_name, False))

        print()

    # Print summary
    end_time = time.time()
    duration = end_time - start_time

    print("=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)

    passed = 0
    failed = 0

    for test_name, success in all_results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:<30} {status}")
        if success:
            passed += 1
        else:
            failed += 1

    print()
    print(f"Total Tests: {len(all_results)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Duration: {duration:.2f} seconds")

    if failed == 0:
        print()
        print("🎉 ALL CORE FUNCTIONALITY TESTS PASSED!")
        print("✅ The Hair Studio system is working correctly.")
        return True
    else:
        print()
        print("⚠️  SOME TESTS FAILED!")
        print("❌ Please check the failed tests and fix any issues.")
        return False


def run_specific_test(test_name):
    """Run a specific test module."""
    test_modules = {
        "constants": "test_constants_config",
        "config": "test_asset_lib_config_core",
        "data": "test_data_manager_core",
        "subtab": "test_sub_tab_switching",
        "subtype": "test_sub_type_logic",
    }

    if test_name not in test_modules:
        print(f"Unknown test: {test_name}")
        print(f"Available tests: {', '.join(test_modules.keys())}")
        return False

    module_name = test_modules[test_name]

    try:
        print(f"Running {test_name} tests...")
        module = __import__(
            f"cgame_avatar_factory.hair_studio.tests.{module_name}",
            fromlist=[module_name],
        )

        if hasattr(module, "run_tests"):
            return module.run_tests()
        else:
            loader = unittest.TestLoader()
            suite = loader.loadTestsFromModule(module)
            runner = unittest.TextTestRunner(verbosity=2)
            result = runner.run(suite)
            return result.wasSuccessful()

    except Exception as e:
        print(f"Error running {test_name} tests: {e}")
        return False


def main():
    """Main entry point."""
    if len(sys.argv) > 1:
        # Run specific test
        test_name = sys.argv[1].lower()
        success = run_specific_test(test_name)
    else:
        # Run all tests
        success = run_all_core_tests()

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
