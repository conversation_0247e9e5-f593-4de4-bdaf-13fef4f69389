# cgame_avatar_factory\hair_studio\maya_api\utils.py

"""Maya API Utilities.

This module provides common utility functions for Maya API operations,
including unified Maya import and mock management.
"""

# Import built-in modules
import logging
import os
import re


class MayaMockCmds(object):
    """
    Unified mock class for maya.cmds when Maya is not available.

    This provides consistent mock behavior across all modules and
    centralizes the mock implementation for easier maintenance.
    """

    def __init__(self, logger_name=None):
        """
        Initialize the mock with optional logger name.

        Args:
            logger_name (str, optional): Logger name for mock messages
        """
        self.logger = logging.getLogger(logger_name or __name__)
        self._call_count = 0

        # Mock node storage for testing
        self._mock_nodes = set()

    def objExists(self, node_name):
        """Mock objExists command that returns boolean."""
        self._call_count += 1
        exists = node_name in self._mock_nodes
        self.logger.debug(
            "Maya mock: objExists('%s') -> %s (call #%d)",
            node_name, exists, self._call_count
        )
        return exists

    def select(self, *args, **kwargs):
        """Mock select command."""
        self._call_count += 1
        self.logger.debug(
            "Maya mock: select(%s, %s) (call #%d)",
            args, kwargs, self._call_count
        )
        return True

    def delete(self, node_name):
        """Mock delete command."""
        self._call_count += 1
        if node_name in self._mock_nodes:
            self._mock_nodes.remove(node_name)
            self.logger.debug(
                "Maya mock: delete('%s') - removed (call #%d)",
                node_name, self._call_count
            )
        else:
            self.logger.debug(
                "Maya mock: delete('%s') - not found (call #%d)",
                node_name, self._call_count
            )
        return True

    def listRelatives(self, node_name, **kwargs):
        """Mock listRelatives command."""
        self._call_count += 1
        self.logger.debug(
            "Maya mock: listRelatives('%s', %s) (call #%d)",
            node_name, kwargs, self._call_count
        )
        return []

    def setAttr(self, attr_name, value, **kwargs):
        """Mock setAttr command."""
        self._call_count += 1
        self.logger.debug(
            "Maya mock: setAttr('%s', %s, %s) (call #%d)",
            attr_name, value, kwargs, self._call_count
        )
        return True

    def add_mock_node(self, node_name):
        """Add a node to mock storage for testing."""
        self._mock_nodes.add(node_name)
        self.logger.debug("Maya mock: Added node '%s' to mock storage", node_name)

    def remove_mock_node(self, node_name):
        """Remove a node from mock storage for testing."""
        self._mock_nodes.discard(node_name)
        self.logger.debug("Maya mock: Removed node '%s' from mock storage", node_name)

    def clear_mock_nodes(self):
        """Clear all mock nodes for testing."""
        self._mock_nodes.clear()
        self.logger.debug("Maya mock: Cleared all mock nodes")

    def __getattr__(self, item):
        """
        Mock any Maya command call.

        Args:
            item (str): Maya command name

        Returns:
            function: Mock function that logs the call
        """

        def wrapper(*args, **kwargs):
            self._call_count += 1
            self.logger.warning(
                "Maya command called outside of Maya: {}.{} {} {}".format(
                    item,
                    args,
                    kwargs,
                    f"(call #{self._call_count})",
                ),
            )
            return f"mock_{item}_{self._call_count}"

        return wrapper

    def get_call_count(self):
        """Get the total number of mock calls made."""
        return self._call_count

    def reset_call_count(self):
        """Reset the call counter."""
        self._call_count = 0


# Global Maya availability check and import
_maya_cmds = None
_maya_available = None

# Sentinel object to distinguish between "not initialized" and "cached as None"
_UNINITIALIZED = object()
_maya_version = _UNINITIALIZED


def get_maya_cmds(logger_name=None):
    """
    Get maya.cmds with unified mock fallback.

    This function provides a centralized way to import maya.cmds
    with consistent mock behavior when Maya is not available.

    Args:
        logger_name (str, optional): Logger name for mock messages

    Returns:
        maya.cmds or MayaMockCmds: Real or mock Maya commands
    """
    global _maya_cmds, _maya_available

    if _maya_cmds is None:
        try:
            # Import third-party modules
            import maya.cmds as cmds

            # Test if Maya commands are actually available
            # Some test environments may have maya module but not functional commands
            try:
                # Try a simple Maya command to verify it's working
                hasattr(cmds, 'objExists')
                _maya_cmds = cmds
                _maya_available = True
            except Exception:
                # Maya module exists but commands don't work, use mock
                _maya_cmds = MayaMockCmds(logger_name)
                _maya_available = False

        except ImportError:
            _maya_cmds = MayaMockCmds(logger_name)
            _maya_available = False

    # Ensure mock has required methods for compatibility
    if not _maya_available and hasattr(_maya_cmds, '__class__'):
        if _maya_cmds.__class__.__name__ == 'MayaMockCmds':
            # Ensure our enhanced mock is being used
            if not hasattr(_maya_cmds, 'objExists'):
                # Fallback: create a new enhanced mock
                _maya_cmds = MayaMockCmds(logger_name)

    return _maya_cmds


def is_maya_available():
    """
    Check if Maya is available.

    Returns:
        bool: True if Maya is available, False otherwise
    """
    global _maya_available

    if _maya_available is None:
        # Trigger the import check
        get_maya_cmds()

    return _maya_available


MAYA_VERSION_PATTERN = re.compile(r"20\d{2}")


def get_maya_version():
    """
    Get Maya version number.

    This function caches the version after first detection to avoid
    repeated Maya API calls.

    Returns:
        int or None: Maya version as year (e.g., 2022, 2023) or None if not available
    """
    global _maya_version
    logger = logging.getLogger(__name__)

    # Return cached version if already determined (including None)
    if _maya_version is not _UNINITIALIZED:
        return _maya_version

    if not is_maya_available():
        logger.debug("Maya not available, cannot determine version")
        _maya_version = None
        return None

    try:
        cmds = get_maya_cmds(__name__)
        if hasattr(cmds, "about"):
            version_string = cmds.about(version=True)
            # Extract year from version string (e.g., "2022", "2023")
            match = MAYA_VERSION_PATTERN.search(version_string)
            if match:
                version = int(match.group())
                logger.debug(f"Detected Maya version: {version}")
                _maya_version = version
                return version
            else:
                logger.warning(f"Could not parse Maya version from: {version_string}")
        else:
            logger.warning("cmds.about not available")

        _maya_version = None
        return None
    except Exception as e:
        logger.error(f"Error determining Maya version: {e}")
        _maya_version = None
        return None


################################################################# import asset utils##############################################################


def get_asset_file_type(file_path):
    """
    Determine asset file type based on file extension.

    Args:
        file_path (str): Path to the asset file

    Returns:
        str: "FBX" for .fbx files, "OBJ" for .obj files, None for unsupported types
    """
    logger = logging.getLogger(__name__)

    if not os.path.exists(file_path):
        logger.error(f"File does not exist: {file_path}")
        return None

    _, ext = os.path.splitext(file_path)
    ext_lower = ext.lower()

    if ext_lower == ".fbx":
        return "FBX"
    elif ext_lower == ".obj":
        return "OBJ"
    else:
        logger.warning(f"Unsupported file type: {ext}")
        return None


def guess_imported_obj_up_axis(obj_name):
    """guess object up axis by box."""
    logger = logging.getLogger(__name__)
    logger.debug(f"guess_imported_obj_up_axis: {obj_name}")
    try:
        cmds = get_maya_cmds(__name__)
        bbox = cmds.exactWorldBoundingBox(obj_name)
        _, min_y, min_z, _, max_y, max_z = bbox
        sum_y = max_y + min_y  # Y
        sum_z = max_z + min_z  # Z
        if sum_z > sum_y:
            return "z"
        else:
            return "y"
    except Exception as e:
        logger.error(f"Error guess_imported_obj_up_axis: {e}")

    logger.debug(f"guess_imported_obj_up_axis: {obj_name} failed, use default y up")
    return "y"


def get_scene_up_axis():
    """
    Get the current Maya scene's up axis.

    Returns:
        str: "Y" for Y-up, "Z" for Z-up, None if Maya not available
    """
    logger = logging.getLogger(__name__)

    if not is_maya_available():
        logger.warning("Maya not available, cannot determine scene up axis")
        return None

    try:
        cmds = get_maya_cmds(__name__)
        scene_up = cmds.upAxis(q=True, axis=True)
        return scene_up.lower()
    except Exception as e:
        logger.error(f"Error getting scene up axis: {e}")
        return None


def get_rotate_by_axis_up(object_name):
    logger = logging.getLogger(__name__)
    scene_up_axis = get_scene_up_axis()
    asset_up_axis = guess_imported_obj_up_axis(object_name)
    ROTATE_MAP = {
        "yz": [90, 0, 0],
        "zy": [-90, 0, 0],
    }
    key = str(asset_up_axis.lower() + scene_up_axis.lower())
    rotate = ROTATE_MAP[key] if key in ROTATE_MAP else [0, 0, 0]
    logger.debug(f"Aligned asset axis from {asset_up_axis} to scene {scene_up_axis}, set rotate {rotate}")

    return rotate


################################################################# import asset utils##############################################################
