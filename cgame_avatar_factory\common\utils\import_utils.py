# Import built-in modules
from logging import getLogger
import os

# Import third-party modules
from maya import cmds

FBX_EXTENSION = ".fbx"
logger = getLogger(__name__)


class FBXImportMode(object):
    """FBX import mode"""

    Add = "Add"
    Merge = "Merge"
    ExMerge = "Exmerge"


def load_fbx_plugin():
    """load plugin"""
    if not cmds.pluginInfo("fbxmaya", q=True, loaded=True):
        cmds.loadPlugin("fbxmaya")
        cmds.pluginInfo("fbxmaya", e=True, autoload=True)


def import_fbx(path, mode=FBXImportMode.Merge):
    """import FBX

    Args:
        path (unicode): path
        mode (string): import mode
    """
    # Check if path exists
    if not os.path.exists(path):
        logger.error("File not exists. path={0}".format(path))
        return

    # Convert to lowercase for comparison (resolves case sensitivity issues on Windows/MacOS)
    _, ext = os.path.splitext(path)
    if ext.lower() != FBX_EXTENSION:
        logger.error("Invalid extension. path={0}".format(path))
        return

    load_fbx_plugin()
    cmds.FBXImportMode("-v", mode)
    cmds.FBXImportMergeAnimationLayers("-v", True)
    cmds.FBXImportLights("-v", False)
    cmds.FBXImportConstraints("-v", False)
    cmds.FBXImport("-f", path)
