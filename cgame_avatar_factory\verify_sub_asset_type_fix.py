#!/usr/bin/env python
"""Verify that sub_asset_type fix is working correctly."""

import os
import sys

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_hair_asset_model():
    """Test HairAsset model changes."""
    print("Testing HairAsset model...")
    
    try:
        from cgame_avatar_factory.hair_studio.data.models import HairAsset
        
        # Test 1: Create HairAsset with sub_asset_type
        asset = HairAsset(
            id="test_001",
            name="Test Hair",
            asset_type="card",
            sub_asset_type="scalp",
            thumbnail="/path/to/thumb.jpg",
            metadata={"file_path": "/path/to/asset.fbx"}
        )
        
        print(f"✓ Created HairAsset with sub_asset_type: {asset.sub_asset_type}")
        
        # Test 2: to_dict includes sub_asset_type
        asset_dict = asset.to_dict()
        if "sub_asset_type" in asset_dict and asset_dict["sub_asset_type"] == "scalp":
            print("✓ to_dict() includes sub_asset_type correctly")
        else:
            print("✗ to_dict() missing or incorrect sub_asset_type")
            return False
            
        # Test 3: from_dict preserves sub_asset_type
        recreated_asset = HairAsset.from_dict(asset_dict)
        if recreated_asset.sub_asset_type == "scalp":
            print("✓ from_dict() preserves sub_asset_type correctly")
        else:
            print("✗ from_dict() does not preserve sub_asset_type")
            return False
            
        # Test 4: Handle None sub_asset_type
        asset_none = HairAsset(
            id="test_002",
            name="Test Hair None",
            asset_type="card",
            sub_asset_type=None
        )
        
        if asset_none.sub_asset_type is None:
            print("✓ Handles None sub_asset_type correctly")
        else:
            print("✗ Does not handle None sub_asset_type correctly")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Error testing HairAsset model: {e}")
        return False

def test_hair_operations_logic():
    """Test hair_operations reading logic."""
    print("\nTesting hair_operations reading logic...")
    
    try:
        # Read the file content to verify the change
        operations_file = os.path.join(project_root, "cgame_avatar_factory", "hair_studio", "maya_api", "hair_operations.py")
        
        with open(operations_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check if the correct line exists
        if 'sub_asset_type = asset_data.get("sub_asset_type", None)' in content:
            print("✓ hair_operations.py reads sub_asset_type from asset_data top level")
        else:
            print("✗ hair_operations.py does not read sub_asset_type from correct location")
            return False
            
        # Check if the old incorrect line is removed
        if 'sub_asset_type = meta_data.get("sub_asset_type", None)' in content:
            print("✗ hair_operations.py still has old incorrect reading logic")
            return False
        else:
            print("✓ Old incorrect reading logic removed")
            
        return True
        
    except Exception as e:
        print(f"✗ Error testing hair_operations logic: {e}")
        return False

def test_data_flow_simulation():
    """Simulate the complete data flow."""
    print("\nTesting complete data flow simulation...")
    
    try:
        from cgame_avatar_factory.hair_studio.data.models import HairAsset
        
        # Simulate data manager creating asset dict
        data_manager_asset = {
            "id": "card_12345678",
            "name": "Test Hair Asset",
            "asset_type": "card",
            "sub_asset_type": "scalp",  # This is stored at top level
            "file_path": "/path/to/hair.fbx",
            "thumbnail": "/path/to/thumb.jpg",
            "metadata": {
                "file_path": "/path/to/hair.fbx",
                "reference": "/path/to/head.obj",
            }
        }
        
        print("✓ Data manager creates asset with sub_asset_type at top level")
        
        # Simulate hair_manager converting to HairAsset
        hair_asset = HairAsset.from_dict(data_manager_asset)
        print(f"✓ HairAsset created with sub_asset_type: {hair_asset.sub_asset_type}")
        
        # Simulate hair_manager calling to_dict for Maya API
        asset_data_for_maya = hair_asset.to_dict()
        print(f"✓ Asset data for Maya includes sub_asset_type: {asset_data_for_maya.get('sub_asset_type')}")
        
        # Simulate Maya API reading the data
        sub_asset_type_from_maya_api = asset_data_for_maya.get("sub_asset_type", None)
        if sub_asset_type_from_maya_api == "scalp":
            print("✓ Maya API can read sub_asset_type correctly")
        else:
            print("✗ Maya API cannot read sub_asset_type correctly")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Error in data flow simulation: {e}")
        return False

def main():
    """Run all verification tests."""
    print("=" * 60)
    print("VERIFYING SUB_ASSET_TYPE FIX")
    print("=" * 60)
    
    all_passed = True
    
    # Test 1: HairAsset model
    if not test_hair_asset_model():
        all_passed = False
    
    # Test 2: hair_operations logic
    if not test_hair_operations_logic():
        all_passed = False
        
    # Test 3: Complete data flow
    if not test_data_flow_simulation():
        all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✓ ALL TESTS PASSED - sub_asset_type fix is working correctly!")
    else:
        print("✗ SOME TESTS FAILED - please check the issues above")
    print("=" * 60)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
