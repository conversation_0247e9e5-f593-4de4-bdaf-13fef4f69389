"""Tests for AssetListModel.

This module contains comprehensive tests for the AssetListModel class,
ensuring all functionality works correctly before integration.
"""

# Import built-in modules
import os
import sys

# Import third-party modules
import pytest

# Add project root to path for imports
project_root = os.path.dirname(
    os.path.dirname(
        os.path.dirname(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
        ),
    ),
)
sys.path.insert(0, project_root)

# Import third-party modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_list_model import AssetListModel


@pytest.fixture
def app():
    """Create QApplication for tests."""
    app = QtWidgets.QApplication.instance()
    if app is None:
        app = QtWidgets.QApplication([])
    return app


@pytest.fixture
def sample_assets():
    """Create sample asset data for testing."""
    return [
        {
            "id": "card_1",
            "name": "Hair Card 1",
            "asset_type": "card",
            "thumbnail": "/path/to/card1.jpg",
            "metadata": {"category": "front_bangs"},
        },
        {
            "id": "card_2",
            "name": "Hair Card 2",
            "asset_type": "card",
            "thumbnail": "/path/to/card2.jpg",
            "metadata": {"category": "side_hair"},
        },
        {
            "id": "xgen_1",
            "name": "XGen Hair 1",
            "asset_type": "xgen",
            "thumbnail": "/path/to/xgen1.jpg",
            "metadata": {"strand_count": 1000},
        },
        {
            "id": "curve_1",
            "name": "Curve Hair 1",
            "asset_type": "curve",
            "thumbnail": "/path/to/curve1.jpg",
            "metadata": {"curve_type": "bezier"},
        },
    ]


@pytest.fixture
def model(app):
    """Create AssetListModel instance for testing."""
    return AssetListModel()


class TestAssetListModelBasics:
    """Test basic functionality of AssetListModel."""

    def test_initialization(self, model):
        """Test model initialization."""
        assert model.rowCount() == 0
        assert model.getAssets() == []
        assert model.getFilteredAssets() == []

    def test_set_assets(self, model, sample_assets):
        """Test setting assets."""
        model.setAssets(sample_assets)

        assert model.rowCount() == len(sample_assets)
        assert len(model.getAssets()) == len(sample_assets)
        assert len(model.getFilteredAssets()) == len(sample_assets)

    def test_data_roles(self, model, sample_assets):
        """Test data retrieval with different roles."""
        model.setAssets(sample_assets)

        index = model.createIndex(0, 0)

        # Test DisplayRole
        display_data = model.data(index, QtCore.Qt.DisplayRole)
        assert display_data == sample_assets[0]["name"]

        # Test custom roles
        asset_data = model.data(index, AssetListModel.AssetDataRole)
        assert asset_data == sample_assets[0]

        asset_id = model.data(index, AssetListModel.AssetIdRole)
        assert asset_id == sample_assets[0]["id"]

        asset_type = model.data(index, AssetListModel.AssetTypeRole)
        assert asset_type == sample_assets[0]["asset_type"]

        thumbnail = model.data(index, AssetListModel.ThumbnailPathRole)
        assert thumbnail == sample_assets[0]["thumbnail"]


class TestAssetListModelFiltering:
    """Test filtering functionality."""

    def test_text_filter(self, model, sample_assets):
        """Test text-based filtering."""
        model.setAssets(sample_assets)

        # Filter by "Card" - should match 2 items
        model.setFilterText("Card")
        assert model.rowCount() == 2

        # Filter by "XGen" - should match 1 item
        model.setFilterText("XGen")
        assert model.rowCount() == 1

        # Filter by non-existent text - should match 0 items
        model.setFilterText("NonExistent")
        assert model.rowCount() == 0

        # Clear filter - should show all items
        model.setFilterText("")
        assert model.rowCount() == len(sample_assets)

    def test_type_filter(self, model, sample_assets):
        """Test type-based filtering."""
        model.setAssets(sample_assets)

        # Filter by "card" type - should match 2 items
        model.setFilterType("card")
        assert model.rowCount() == 2

        # Filter by "xgen" type - should match 1 item
        model.setFilterType("xgen")
        assert model.rowCount() == 1

        # Filter by non-existent type - should match 0 items
        model.setFilterType("nonexistent")
        assert model.rowCount() == 0

        # Clear type filter
        model.setFilterType(None)
        assert model.rowCount() == len(sample_assets)

    def test_combined_filters(self, model, sample_assets):
        """Test combining text and type filters."""
        model.setAssets(sample_assets)

        # Set type filter to "card" and text filter to "Hair Card 1"
        model.setFilterType("card")
        model.setFilterText("Hair Card 1")
        assert model.rowCount() == 1

        # Change text filter to something that doesn't match card type
        model.setFilterText("XGen")
        assert model.rowCount() == 0

        # Clear filters
        model.clearFilters()
        assert model.rowCount() == len(sample_assets)


class TestAssetListModelDynamicUpdates:
    """Test dynamic asset updates."""

    def test_add_asset(self, model, sample_assets):
        """Test adding assets dynamically."""
        model.setAssets(sample_assets[:2])  # Start with 2 assets
        assert model.rowCount() == 2

        # Add a new asset
        new_asset = {
            "id": "new_asset",
            "name": "New Asset",
            "asset_type": "card",
            "thumbnail": "/path/to/new.jpg",
        }

        result = model.addAsset(new_asset)
        assert result is True
        assert model.rowCount() == 3

        # Verify the asset was added
        added_asset = model.getAssetById("new_asset")
        assert added_asset == new_asset

    def test_add_duplicate_asset(self, model, sample_assets):
        """Test adding asset with duplicate ID."""
        model.setAssets(sample_assets)

        # Try to add asset with existing ID
        duplicate_asset = {
            "id": "card_1",  # This ID already exists
            "name": "Duplicate Asset",
            "asset_type": "card",
        }

        result = model.addAsset(duplicate_asset)
        assert result is False
        assert model.rowCount() == len(sample_assets)  # Count unchanged

    def test_remove_asset(self, model, sample_assets):
        """Test removing assets."""
        model.setAssets(sample_assets)
        original_count = model.rowCount()

        # Remove an existing asset
        result = model.removeAsset("card_1")
        assert result is True
        assert model.rowCount() == original_count - 1

        # Verify asset was removed
        removed_asset = model.getAssetById("card_1")
        assert removed_asset is None

    def test_remove_nonexistent_asset(self, model, sample_assets):
        """Test removing non-existent asset."""
        model.setAssets(sample_assets)
        original_count = model.rowCount()

        # Try to remove non-existent asset
        result = model.removeAsset("nonexistent_id")
        assert result is False
        assert model.rowCount() == original_count  # Count unchanged

    def test_update_asset(self, model, sample_assets):
        """Test updating existing assets."""
        model.setAssets(sample_assets)

        # Update an existing asset
        updated_asset = {
            "id": "card_1",
            "name": "Updated Hair Card 1",
            "asset_type": "card",
            "thumbnail": "/path/to/updated_card1.jpg",
            "metadata": {"category": "updated_category"},
        }

        result = model.updateAsset("card_1", updated_asset)
        assert result is True

        # Verify the asset was updated
        retrieved_asset = model.getAssetById("card_1")
        assert retrieved_asset["name"] == "Updated Hair Card 1"
        assert retrieved_asset["metadata"]["category"] == "updated_category"

    def test_update_nonexistent_asset(self, model, sample_assets):
        """Test updating non-existent asset."""
        model.setAssets(sample_assets)

        updated_asset = {"id": "nonexistent_id", "name": "Updated Asset"}

        result = model.updateAsset("nonexistent_id", updated_asset)
        assert result is False

    def test_clear_assets(self, model, sample_assets):
        """Test clearing all assets."""
        model.setAssets(sample_assets)
        assert model.rowCount() > 0

        model.clear()
        assert model.rowCount() == 0
        assert model.getAssets() == []
        assert model.getFilteredAssets() == []


class TestAssetListModelSignals:
    """Test signal emission."""

    def test_data_updated_signal(self, model, sample_assets):
        """Test dataUpdated signal emission."""
        signal_received = []
        model.dataUpdated.connect(lambda: signal_received.append(True))

        # Signal should be emitted when setting assets
        model.setAssets(sample_assets)
        assert len(signal_received) == 1

        # Signal should be emitted when adding asset
        new_asset = {"id": "new", "name": "New", "asset_type": "card"}
        model.addAsset(new_asset)
        assert len(signal_received) == 2

        # Signal should be emitted when removing asset
        model.removeAsset("new")
        assert len(signal_received) == 3

        # Signal should be emitted when clearing
        model.clear()
        assert len(signal_received) == 4

    def test_filter_changed_signal(self, model, sample_assets):
        """Test filterChanged signal emission."""
        signal_received = []
        model.filterChanged.connect(lambda text: signal_received.append(text))

        model.setAssets(sample_assets)

        # Signal should be emitted when filter text changes
        model.setFilterText("test")
        assert len(signal_received) == 1
        assert signal_received[0] == "test"

        # Signal should not be emitted when setting same filter
        model.setFilterText("test")
        assert len(signal_received) == 1  # No new signal


if __name__ == "__main__":
    pytest.main([__file__])
