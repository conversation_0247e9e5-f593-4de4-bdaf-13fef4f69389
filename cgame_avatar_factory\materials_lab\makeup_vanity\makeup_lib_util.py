"""
Makeup Library Utility Module

This module provides utility functions for the makeup library.
"""

# Import built-in modules
import logging
import os

# Import local modules
from cgame_avatar_factory.common import constants as const
from cgame_avatar_factory.materials_lab.makeup_vanity.makeup_item import MakeupItem

logger = logging.getLogger(__name__)


def read_makeup_paths():
    """
    Read all makeup items from the makeup paths.

    Returns:
        list: List of makeup items
    """
    makeup_items = []
    item_id = 0

    # List of resource paths to check
    resource_paths = []

    # Add project makeup path if it exists
    if const.PROJECT_MAKEUP_PATH and os.path.exists(const.PROJECT_MAKEUP_PATH):
        resource_paths.append(const.PROJECT_MAKEUP_PATH)

    # Add public makeup path if it exists
    if const.PUBLIC_MAKEUP_PATH and os.path.exists(const.PUBLIC_MAKEUP_PATH) and const.IS_METAHUMAN_SPEC:
        resource_paths.append(const.PUBLIC_MAKEUP_PATH)

    for resource_path in resource_paths:
        # Get categories (subdirectories)
        try:
            categories = [d for d in os.listdir(resource_path) if os.path.isdir(os.path.join(resource_path, d))]
        except (FileNotFoundError, PermissionError) as e:
            logger.warning(f"Could not read makeup path {resource_path}: {e}")
            continue

        for category in categories:
            category_path = os.path.join(resource_path, category)

            # Get items (subdirectories within category)
            try:
                items = [d for d in os.listdir(category_path) if os.path.isdir(os.path.join(category_path, d))]
            except (FileNotFoundError, PermissionError) as e:
                logger.warning(f"Could not read category path {category_path}: {e}")
                continue

            for item_name in items:
                item_path = os.path.join(category_path, item_name)

                # Look for thumbnail
                thumbnail_path = None
                for ext in const.SUPPORTED_THUMBNAIL_FILE_EXTENSIONS:
                    possible_thumbnail = os.path.join(item_path, f"{item_name}{ext}")
                    if os.path.exists(possible_thumbnail):
                        thumbnail_path = possible_thumbnail
                        break

                # Create makeup item
                try:
                    makeup_item = MakeupItem(
                        item_id=item_id,
                        item_path=item_path,
                        category=category,
                        name=item_name,
                        thumbnail_path=thumbnail_path,
                    )
                    makeup_items.append(makeup_item)
                    item_id += 1
                except ValueError as e:
                    logger.warning(f"Could not create makeup item for {item_path}: {e}")

    return makeup_items


def get_all_directory_categories():
    """
    Get all categories from the makeup directories, regardless of whether they contain items.

    Returns:
        list: List of all category names (directory names) from PUBLIC_MAKEUP_PATH and PROJECT_MAKEUP_PATH
    """
    categories = set()

    # Check PUBLIC_MAKEUP_PATH
    if hasattr(const, "PUBLIC_MAKEUP_PATH") and const.PUBLIC_MAKEUP_PATH and os.path.exists(const.PUBLIC_MAKEUP_PATH):
        try:
            public_categories = [
                d
                for d in os.listdir(const.PUBLIC_MAKEUP_PATH)
                if os.path.isdir(os.path.join(const.PUBLIC_MAKEUP_PATH, d))
            ]
            categories.update(public_categories)
        except (FileNotFoundError, PermissionError) as e:
            logger.warning(f"Could not read public makeup path {const.PUBLIC_MAKEUP_PATH}: {e}")

    # Check PROJECT_MAKEUP_PATH
    if (
        hasattr(const, "PROJECT_MAKEUP_PATH")
        and const.PROJECT_MAKEUP_PATH
        and os.path.exists(const.PROJECT_MAKEUP_PATH)
    ):
        try:
            project_categories = [
                d
                for d in os.listdir(const.PROJECT_MAKEUP_PATH)
                if os.path.isdir(os.path.join(const.PROJECT_MAKEUP_PATH, d))
            ]
            categories.update(project_categories)
        except (FileNotFoundError, PermissionError) as e:
            logger.warning(f"Could not read project makeup path {const.PROJECT_MAKEUP_PATH}: {e}")

    return ["全部"] + sorted(list(categories))


def get_categories(makeup_items):
    """
    Get unique categories from makeup items.

    Args:
        makeup_items (list): List of MakeupItem objects

    Returns:
        list: List of unique categories
    """
    categories = set()
    for item in makeup_items:
        categories.add(item.category)
    return ["全部"] + sorted(list(categories))


def filter_by_category(makeup_items, category):
    """
    Filter makeup items by category.

    Args:
        makeup_items (list): List of MakeupItem objects
        category (str): Category to filter by

    Returns:
        list: Filtered list of MakeupItem objects
    """
    if category == "全部":
        return makeup_items

    # Filter items by category
    filtered_items = [item for item in makeup_items if item.category == category]

    # Return filtered items (may be empty for categories with no items)
    return filtered_items
