#!/usr/bin/env python
"""Test runner for AssetListModel.

This script runs the tests for AssetListModel to verify the first module
of our QListView replacement implementation works correctly.
"""

# Import built-in modules
import os
import sys

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def run_basic_functionality_test():
    """Run basic functionality test without pytest."""
    print("=== Testing AssetListModel Basic Functionality ===")

    try:
        # Import Qt modules first
        # Import third-party modules
        from qtpy import QtCore
        from qtpy import QtWidgets

        print("✓ QtPy imported successfully")

        # Import our model
        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.asset_list_model import (
            AssetListModel,
        )

        print("✓ AssetListModel imported successfully")

        # Create QApplication
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication([])

        # Create test data
        sample_assets = [
            {
                "id": "card_1",
                "name": "Hair Card 1",
                "asset_type": "card",
                "thumbnail": "/path/to/card1.jpg",
            },
            {
                "id": "card_2",
                "name": "Hair Card 2",
                "asset_type": "card",
                "thumbnail": "/path/to/card2.jpg",
            },
            {
                "id": "xgen_1",
                "name": "XGen Hair 1",
                "asset_type": "xgen",
                "thumbnail": "/path/to/xgen1.jpg",
            },
        ]

        # Test 1: Model initialization
        print("✓ Testing model initialization...")
        model = AssetListModel()
        assert model.rowCount() == 0, "Initial row count should be 0"
        assert model.getAssets() == [], "Initial assets should be empty"
        print("  ✓ Model initialized correctly")

        # Test 2: Setting assets
        print("✓ Testing asset setting...")
        model.setAssets(sample_assets)
        assert model.rowCount() == 3, f"Row count should be 3, got {model.rowCount()}"
        assert len(model.getAssets()) == 3, "Assets count should be 3"
        print("  ✓ Assets set correctly")

        # Test 3: Data retrieval
        print("✓ Testing data retrieval...")
        index = model.createIndex(0, 0)
        display_data = model.data(index, QtCore.Qt.DisplayRole)
        assert display_data == "Hair Card 1", f"Display data should be 'Hair Card 1', got '{display_data}'"

        asset_data = model.data(index, AssetListModel.AssetDataRole)
        assert asset_data["id"] == "card_1", "Asset data should match first asset"
        print("  ✓ Data retrieval works correctly")

        # Test 4: Text filtering
        print("✓ Testing text filtering...")
        model.setFilterText("Card")
        assert model.rowCount() == 2, f"Filtered count should be 2, got {model.rowCount()}"

        model.setFilterText("XGen")
        assert model.rowCount() == 1, f"Filtered count should be 1, got {model.rowCount()}"

        model.setFilterText("")
        assert model.rowCount() == 3, f"Unfiltered count should be 3, got {model.rowCount()}"
        print("  ✓ Text filtering works correctly")

        # Test 5: Type filtering
        print("✓ Testing type filtering...")
        model.setFilterType("card")
        assert model.rowCount() == 2, f"Card type filter should show 2 items, got {model.rowCount()}"

        model.setFilterType("xgen")
        assert model.rowCount() == 1, f"XGen type filter should show 1 item, got {model.rowCount()}"

        model.clearFilters()
        assert model.rowCount() == 3, f"Cleared filters should show 3 items, got {model.rowCount()}"
        print("  ✓ Type filtering works correctly")

        # Test 6: Dynamic updates
        print("✓ Testing dynamic updates...")
        new_asset = {
            "id": "new_asset",
            "name": "New Asset",
            "asset_type": "card",
            "thumbnail": "/path/to/new.jpg",
        }

        result = model.addAsset(new_asset)
        assert result is True, "Adding new asset should succeed"
        assert model.rowCount() == 4, f"Count after adding should be 4, got {model.rowCount()}"

        result = model.removeAsset("new_asset")
        assert result is True, "Removing asset should succeed"
        assert model.rowCount() == 3, f"Count after removing should be 3, got {model.rowCount()}"
        print("  ✓ Dynamic updates work correctly")

        # Test 7: Signal emission
        print("✓ Testing signal emission...")
        signal_count = [0]

        def on_data_updated():
            signal_count[0] += 1

        model.dataUpdated.connect(on_data_updated)
        model.setAssets(sample_assets)
        assert signal_count[0] > 0, "dataUpdated signal should be emitted"
        print("  ✓ Signals work correctly")

        print("\n🎉 All AssetListModel tests passed!")
        return True

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure qtpy and the project modules are available")
        return False

    except AssertionError as e:
        print(f"❌ Test assertion failed: {e}")
        return False

    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        # Import built-in modules
        import traceback

        traceback.print_exc()
        return False


def run_pytest_tests():
    """Run pytest tests if available."""
    print("\n=== Running pytest tests ===")

    try:
        # Import third-party modules
        import pytest

        test_file = (
            "cgame_avatar_factory/hair_studio/ui/asset_library/listview_implementation/tests/test_asset_list_model.py"
        )

        try:
            if os.path.exists(test_file):
                result = pytest.main([test_file, "-v"])
                return result == 0
            else:
                print(f"❌ Test file not found: {test_file}")
                return False
        except OSError:
            print(f"❌ Unable to access test file due to an OS error: {test_file}")
            return False

    except ImportError:
        print("⚠️  pytest not available, skipping pytest tests")
        return True


if __name__ == "__main__":
    print("AssetListModel Test Runner")
    print("=" * 50)

    # Run basic functionality test
    basic_test_passed = run_basic_functionality_test()

    # Run pytest tests if available
    pytest_passed = run_pytest_tests()

    # Summary
    print("\n" + "=" * 50)
    print("Test Summary:")
    print(f"Basic functionality: {'✅ PASSED' if basic_test_passed else '❌ FAILED'}")
    print(f"Pytest tests: {'✅ PASSED' if pytest_passed else '❌ FAILED'}")

    if basic_test_passed and pytest_passed:
        print("\n🎉 AssetListModel implementation is ready!")
        print("✅ All tests passed - ready to proceed to next module")
    else:
        print("\n❌ Some tests failed - please fix issues before proceeding")

    sys.exit(0 if (basic_test_passed and pytest_passed) else 1)
