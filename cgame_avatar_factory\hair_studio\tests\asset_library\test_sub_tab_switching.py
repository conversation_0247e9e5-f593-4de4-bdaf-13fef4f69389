"""Core Unit Tests for Sub-Tab Switching.

Tests the core functionality of sub-tab switching and filtering logic
without Qt dependencies, focusing on the business logic.
"""

# Import built-in modules
import os
import sys
import unittest

# Add the project root to Python path for testing
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


class TestSubTabSwitchingLogic(unittest.TestCase):
    """Test sub-tab switching logic without UI dependencies."""

    def setUp(self):
        """Set up test environment."""
        # Create mock assets for testing
        self.mock_assets = [
            {
                "id": "hair_001",
                "name": "Long Hair",
                "asset_type": "card",
                "sub_asset_type": "hair",
                "file_path": "/path/hair/long_hair.fbx",
                "thumbnail": "/path/hair/long_hair.jpg",
            },
            {
                "id": "hair_002",
                "name": "Short Hair",
                "asset_type": "card",
                "sub_asset_type": "hair",
                "file_path": "/path/hair/short_hair.fbx",
                "thumbnail": "/path/hair/short_hair.jpg",
            },
            {
                "id": "eyebrow_001",
                "name": "Thick Eyebrow",
                "asset_type": "card",
                "sub_asset_type": "eyebrow",
                "file_path": "/path/eyebrow/thick.fbx",
                "thumbnail": "/path/eyebrow/thick.jpg",
            },
            {
                "id": "beard_001",
                "name": "Full Beard",
                "asset_type": "card",
                "sub_asset_type": "beard",
                "file_path": "/path/beard/full.fbx",
                "thumbnail": "/path/beard/full.jpg",
            },
        ]

    def test_asset_filtering_by_sub_type(self):
        """Test filtering assets by sub-type."""
        # Test filtering logic
        def filter_assets_by_sub_type(assets, sub_type):
            return [asset for asset in assets if asset.get("sub_asset_type") == sub_type]

        # Test hair filtering
        hair_assets = filter_assets_by_sub_type(self.mock_assets, "hair")
        self.assertEqual(len(hair_assets), 2)
        for asset in hair_assets:
            self.assertEqual(asset["sub_asset_type"], "hair")

        # Test eyebrow filtering
        eyebrow_assets = filter_assets_by_sub_type(self.mock_assets, "eyebrow")
        self.assertEqual(len(eyebrow_assets), 1)
        self.assertEqual(eyebrow_assets[0]["sub_asset_type"], "eyebrow")

        # Test beard filtering
        beard_assets = filter_assets_by_sub_type(self.mock_assets, "beard")
        self.assertEqual(len(beard_assets), 1)
        self.assertEqual(beard_assets[0]["sub_asset_type"], "beard")

        # Test non-existent sub-type
        unknown_assets = filter_assets_by_sub_type(self.mock_assets, "unknown")
        self.assertEqual(len(unknown_assets), 0)

    def test_sub_tab_configuration(self):
        """Test sub-tab configuration using mock data."""
        # Mock tab map data
        mock_tab_map = {
            "hair": {"show_text": "头发", "lib_path": []},
            "eyebrow": {"show_text": "眉毛", "lib_path": []},
            "beard": {"show_text": "胡子", "lib_path": []},
        }
        mock_default_key = "hair"

        # Test tab map structure
        self.assertIsInstance(mock_tab_map, dict)
        self.assertGreater(len(mock_tab_map), 0)

        # Test expected tabs exist
        expected_tabs = ["hair", "eyebrow", "beard"]
        for tab in expected_tabs:
            self.assertIn(tab, mock_tab_map)
            self.assertIn("show_text", mock_tab_map[tab])

        # Test default tab
        self.assertIn(mock_default_key, mock_tab_map)

    def test_sub_tab_display_logic(self):
        """Test when sub-tabs should be displayed using mock logic."""
        # Mock the should_show_sub_tabs logic
        def mock_should_show_sub_tabs(asset_type):
            # Only card type should show sub-tabs
            return asset_type == "card"

        # Test card type should show sub-tabs
        self.assertTrue(mock_should_show_sub_tabs("card"))

        # Test other types should not show sub-tabs
        self.assertFalse(mock_should_show_sub_tabs("xgen"))
        self.assertFalse(mock_should_show_sub_tabs("curve"))
        self.assertFalse(mock_should_show_sub_tabs("unknown"))

    def test_tab_index_mapping(self):
        """Test tab index and key mapping logic using mock data."""
        # Mock config with tab keys
        mock_tab_keys = ["hair", "eyebrow", "beard"]

        # Mock config object
        class MockConfig:
            def get_tab_keys(self):
                return mock_tab_keys

            def get_tab_key_by_index(self, index):
                if 0 <= index < len(mock_tab_keys):
                    return mock_tab_keys[index]
                return None

            def get_tab_index_by_key(self, key):
                try:
                    return mock_tab_keys.index(key)
                except ValueError:
                    return -1

        config = MockConfig()
        tab_keys = config.get_tab_keys()

        # Test index to key mapping
        for i, expected_key in enumerate(tab_keys):
            actual_key = config.get_tab_key_by_index(i)
            self.assertEqual(actual_key, expected_key)

        # Test key to index mapping
        for expected_index, key in enumerate(tab_keys):
            actual_index = config.get_tab_index_by_key(key)
            self.assertEqual(actual_index, expected_index)

        # Test invalid mappings
        self.assertIsNone(config.get_tab_key_by_index(999))
        self.assertEqual(config.get_tab_index_by_key("invalid"), -1)

    def test_default_tab_selection(self):
        """Test default tab selection logic using mock data."""
        # Mock config with default tab logic
        class MockConfig:
            def __init__(self):
                self.tab_keys = ["hair", "eyebrow", "beard"]
                self.default_key = "hair"

            def get_default_tab_key(self):
                return self.default_key

            def get_default_tab_index(self):
                return self.tab_keys.index(self.default_key)

            def get_tab_keys(self):
                return self.tab_keys

        config = MockConfig()

        # Test default tab key
        default_key = config.get_default_tab_key()
        self.assertEqual(default_key, "hair")

        # Test default tab index
        default_index = config.get_default_tab_index()
        tab_keys = config.get_tab_keys()
        self.assertEqual(tab_keys[default_index], default_key)

    def test_tab_switching_state_management(self):
        """Test tab switching state management logic."""
        # Simulate tab switching state
        class MockTabState:
            def __init__(self):
                self.current_tab = "hair"
                self.current_filter = None
                self.pending_filter = None

            def switch_tab(self, new_tab):
                self.current_tab = new_tab
                self.current_filter = new_tab
                self.pending_filter = None

            def set_pending_filter(self, filter_type):
                self.pending_filter = filter_type

            def apply_pending_filter(self):
                if self.pending_filter:
                    self.current_filter = self.pending_filter
                    self.pending_filter = None

        # Test state management
        state = MockTabState()

        # Test initial state
        self.assertEqual(state.current_tab, "hair")
        self.assertIsNone(state.current_filter)

        # Test tab switching
        state.switch_tab("eyebrow")
        self.assertEqual(state.current_tab, "eyebrow")
        self.assertEqual(state.current_filter, "eyebrow")

        # Test pending filter
        state.set_pending_filter("beard")
        self.assertEqual(state.pending_filter, "beard")

        state.apply_pending_filter()
        self.assertEqual(state.current_filter, "beard")
        self.assertIsNone(state.pending_filter)

    def test_asset_count_by_sub_type(self):
        """Test counting assets by sub-type."""

        def count_assets_by_sub_type(assets):
            counts = {}
            for asset in assets:
                sub_type = asset.get("sub_asset_type", "unknown")
                counts[sub_type] = counts.get(sub_type, 0) + 1
            return counts

        counts = count_assets_by_sub_type(self.mock_assets)

        # Test expected counts
        self.assertEqual(counts["hair"], 2)
        self.assertEqual(counts["eyebrow"], 1)
        self.assertEqual(counts["beard"], 1)
        self.assertNotIn("unknown", counts)

    def test_sub_type_validation(self):
        """Test sub-type validation logic using mock data."""
        # Mock valid sub-types
        mock_valid_sub_types = {"hair", "eyebrow", "beard"}

        def is_valid_sub_type(sub_type):
            return sub_type in mock_valid_sub_types

        # Test valid sub-types
        self.assertTrue(is_valid_sub_type("hair"))
        self.assertTrue(is_valid_sub_type("eyebrow"))
        self.assertTrue(is_valid_sub_type("beard"))

        # Test invalid sub-types
        self.assertFalse(is_valid_sub_type("unknown"))
        self.assertFalse(is_valid_sub_type(""))
        self.assertFalse(is_valid_sub_type(None))


class TestSubTabIntegration(unittest.TestCase):
    """Test integration between sub-tab components using mock data."""

    def test_config_and_constants_integration(self):
        """Test integration between AssetLibConfig and constants using mock data."""
        # Mock constants
        mock_tab_map = {
            "hair": {"show_text": "头发", "lib_path": []},
            "eyebrow": {"show_text": "眉毛", "lib_path": []},
            "beard": {"show_text": "胡子", "lib_path": []},
        }
        mock_default_key = "hair"

        # Mock config
        class MockConfig:
            def get_default_tab_key(self):
                return mock_default_key

            def get_tab_keys(self):
                return list(mock_tab_map.keys())

        config = MockConfig()

        # Test that config uses constants correctly
        self.assertEqual(config.get_default_tab_key(), mock_default_key)
        self.assertIn(mock_default_key, config.get_tab_keys())

        # Test that all mock tabs are available in config
        for tab_key in mock_tab_map.keys():
            self.assertIn(tab_key, config.get_tab_keys())

            # Test display text matches (mock implementation)
            expected_text = mock_tab_map[tab_key]["show_text"]
            # Since we don't have get_tab_display_text in mock, just verify structure
            self.assertIsInstance(expected_text, str)
            self.assertGreater(len(expected_text), 0)

    def test_data_manager_sub_type_integration(self):
        """Test integration between data manager and sub-type logic using mock data."""
        # Mock data manager with assets that have sub-types
        mock_assets = [
            {"id": "hair_001", "sub_asset_type": "hair", "name": "Long Hair"},
            {"id": "eyebrow_001", "sub_asset_type": "eyebrow", "name": "Thick Eyebrow"},
            {"id": "beard_001", "sub_asset_type": "beard", "name": "Full Beard"},
        ]

        # Mock manager
        class MockDataManager:
            def get_assets(self, asset_type=None):
                if asset_type == "card":
                    return mock_assets
                return []

        manager = MockDataManager()

        # Test that manager can handle sub-type filtering
        all_assets = manager.get_assets("card")

        # Test asset structure includes sub_asset_type
        for asset in all_assets:
            if asset:  # Skip None assets
                # Should have sub_asset_type field
                self.assertIn("sub_asset_type", asset)
                self.assertIsInstance(asset["sub_asset_type"], str)

                # Sub-type should be valid
                valid_types = ["hair", "eyebrow", "beard"]  # Mock valid types
                self.assertIn(asset["sub_asset_type"], valid_types)


def run_tests():
    """Run the test suite."""
    print("Testing Sub-Tab Switching Core Logic...")

    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestSubTabSwitchingLogic))
    suite.addTests(loader.loadTestsFromTestCase(TestSubTabIntegration))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    if result.wasSuccessful():
        print(f"\n✅ All {result.testsRun} sub-tab switching tests passed!")
    else:
        print(f"\n❌ {len(result.failures)} failures, {len(result.errors)} errors")

    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
