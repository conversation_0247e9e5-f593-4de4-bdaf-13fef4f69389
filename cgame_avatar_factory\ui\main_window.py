# Import built-in modules
import logging
import os

# Import third-party modules
import dayu_widgets
from dayu_widgets import dayu_theme
import lightbox_ui.log
from maya.app.general.mayaMixin import MayaQWidgetDockableMixin
import maya.utils as maya_utils
from pymel import core as pm
from qtpy import QtCore
from qtpy import QtWidgets
from qtpy.QtCore import QAbstractAnimation

# Import local modules
from cgame_avatar_factory.common import constants as const
import cgame_avatar_factory.common.state.scene_state_manager as scene_state_mgr
from cgame_avatar_factory.common.ui.layout import FramelessHLayout
from cgame_avatar_factory.common.ui.layout import FramelessVLayout
import cgame_avatar_factory.face_sculpting_center.build_face.face_controllers as customization
from cgame_avatar_factory.face_sculpting_center.merge.scene_weight_manager import SceneWeightManager
from cgame_avatar_factory.face_sculpting_center.ui.pages.face_area_layout import <PERSON><PERSON><PERSON><PERSON>


def hide_controllers_decorator(func):
    """Decorator to hide all controllers before executing the function"""

    def wrapper(self, *args, **kwargs):
        try:
            customization.hide_all_ctrl()
        except Exception as e:
            if hasattr(self, "logger"):
                self.logger.warning(f"Failed to hide controllers in decorator: {e}")
        return func(self, *args, **kwargs)

    return wrapper


@dayu_widgets.utils.add_settings(const.ORGANIZATION_NAME, const.PACKAGE_NAME, event_name="closeEvent")
class MainWidget(QtWidgets.QWidget, MayaQWidgetDockableMixin):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(
            const.APP_TITLE_TEMPLATE.format(
                app_name=const.APP_NAME,
                app_version=const.APP_VERSION,
            ),
        )
        self.setObjectName(self.__class__.__name__)
        cid = "%sWorkspaceControl" % self.objectName()
        if pm.workspaceControl(cid, q=1, ex=1):
            pm.workspaceControl(cid, e=1, close=1)
            pm.deleteUI(cid)
        self.setDockableParameters(True)
        self.logger = logging.getLogger(__name__)

        self.scene_weight_mgr = SceneWeightManager()
        self.is_mirror = True
        self._dna_components = []
        self._dna_paths = []

        self.setup_ui()
        self.setup_config()
        self.setup_logger()

        self.setWindowTitle(
            "Adam DNA Creator {}".format(
                "for {}".format(const.PROJECT_ABBR) if const.PROJECT_ABBR else "",
            ),
        )

        MaskButton.set_main_window(self)

        QtCore.QTimer.singleShot(100, self.check_lib_path)

        self._is_closing = False
        self._cleanup_done = False
        self._is_closing = False
        self.installEventFilter(self)

    def check_lib_path(self):
        """Check if the project resource path exists.

        Displays a warning message box if the resource path does not exist,
        prompting the user to check the path configuration.
        """
        path = const.PROJECT_RESOURCES_PATH
        if not os.path.exists(path):
            msg_box = QtWidgets.QMessageBox(
                QtWidgets.QMessageBox.Warning,
                "警告",
                f"资源路径不存在：\n{path}\n请检查路径是否正确。",
                QtWidgets.QMessageBox.Ok,
                self,
            )
            msg_box.setWindowFlags(QtCore.Qt.Tool | QtCore.Qt.WindowStaysOnTopHint)
            maya_window = self.window()
            maya_geometry = maya_window.geometry()

            msg_box.setFixedSize(msg_box.sizeHint())
            x = maya_geometry.x() + (maya_geometry.width() - msg_box.width()) // 2
            y = maya_geometry.y() + (maya_geometry.height() - msg_box.height()) // 2
            msg_box.move(x, y)
            msg_box.exec_()
            return

    def setup_logger(self):
        """Set up the logging system for the application.

        Configures log handlers, sets the appropriate log level based on configuration,
        and initializes the log controller for displaying logs in the UI.
        """
        log_level = const.LOG_LEVEL
        is_debug_mode = log_level == "DEBUG"
        while self.logger.handlers:
            self.logger.removeHandler(self.logger.handlers[0])

        self.log_handler = logging.StreamHandler()
        self.logger.addHandler(self.log_handler)

        self.log_handler.setLevel(logging._nameToLevel[log_level])

        if is_debug_mode:
            logging.getLogger().setLevel(logging.DEBUG)
            self.log_controller = lightbox_ui.log.setup_log(parent=self)
        else:
            self.log_controller = lightbox_ui.log.setup_log(parent=self.log_widget)

    def settings_config_formatter(self, config):
        """Format and validate the settings configuration.

        Converts the input configuration to a dictionary format and updates
        the internal settings configuration.

        Args:
            config: The configuration data to format

        Returns:
            dict: The formatted settings configuration
        """
        settings_config = {}
        if not config:
            return settings_config

        if not isinstance(config, dict):
            settings_config = dict(config)
        else:
            settings_config = config

        # 更新内部配置字典
        self._settings_config = settings_config

        return settings_config

    def set_settings_config(self, config):
        self._settings_config = config

    def get_settings_config(self):
        return self._settings_config

    @property
    def export_directory(self):
        return self._settings_config.get("export_directory", "")

    @property
    def texture_directory(self):
        return self._settings_config.get("texture_directory", "")

    @property
    def custom_dna_settings(self):
        return self._settings_config.get("custom_dna_settings", {})

    def setup_config(self):
        """Initialize the application configuration.

        Sets up the default settings configuration and binds it to the application.
        """
        self._settings_config = {}
        self.bind(
            "settings_config",
            self,
            "_settings_config",
            default={},
            formatter=self.settings_config_formatter,
        )

    @property
    def anim_config(self):
        return self.base_merge_page.anim_config

    def setup_ui(self):
        """Set up the main user interface components.

        Creates the main layout and initializes all UI components including
        the menu bar, central area, and log widget.
        """
        self.main_layout = FramelessVLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        self.setLayout(self.main_layout)

        self.setup_menu_bar(self.main_layout)
        self.setup_central_area(self.main_layout)

        self.setup_log_widget()

    def setup_menu_bar(self, parent_layout):
        """Set up the application menu bar.

        Creates the menu bar with file, edit, and settings menus and adds it to the parent layout.

        Args:
            parent_layout: The parent layout to add the menu bar to
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.components.menu_tab import DNAMenuTabWidget

        self.menu_bar = DNAMenuTabWidget()
        self.menu_bar.frameless().background_color(const.DAYU_BG_IN_COLOR)

        self.file_menu = dayu_widgets.MMenu(parent=self.menu_bar)
        self.edit_menu = dayu_widgets.MMenu(parent=self.menu_bar)
        self.settings_menu = dayu_widgets.MMenu(parent=self.menu_bar)

        self.menu_bar.set_dayu_size(dayu_theme.small)

        item_list = [
            {"text": "文件", "menu": self.file_menu},
            {"text": "编辑", "menu": self.edit_menu},
            {"text": "设置", "menu": self.settings_menu},
        ]
        for index, data_dict in enumerate(item_list):
            self.menu_bar.add_menu(data_dict, index)

        parent_layout.addWidget(self.menu_bar)

    def setup_central_area(self, parent_layout):
        """Set up the central area of the application.

        Creates the central container and layout, initializes the base merge page,
        materials lab page, and left area components.

        Args:
            parent_layout: The parent layout to add the central area to
        """
        self.central_area_container = QtWidgets.QFrame()
        self.central_area_layout = FramelessHLayout()
        self.central_area_container.setLayout(self.central_area_layout)
        self.setup_base_merge_page()
        self.setup_materials_lab_page()
        self.setup_animation_theater_page()
        self.setup_left_area(self.central_area_layout)

        parent_layout.addWidget(self.central_area_container)

    def setup_left_area(self, parent_layout):
        """Set up the left area of the application.

        Creates the left area container and layout, and initializes the side tab bar.

        Args:
            parent_layout: The parent layout to add the left area to
        """
        self.left_area_container = QtWidgets.QFrame()
        self.left_area_container.setFrameShape(QtWidgets.QFrame.NoFrame)

        self.left_area_layout = FramelessHLayout()
        self.left_area_container.setLayout(self.left_area_layout)

        self.setup_side_tab_bar(self.left_area_layout)

        parent_layout.addWidget(self.left_area_container)

    def setup_side_tab_bar(self, parent_layout):
        """Set up the side tab bar for navigation.

        Creates a vertical tab bar with various functional pages including face morphing center,
        body workshop, materials lab, hair studio, animation theater, costume workshop, and export center.

        Args:
            parent_layout: The parent layout to add the side tab bar to
        """
        # Import third-party modules
        from dayu_widgets.loading import MLoadingWrapper

        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.components.menu_tab import MMenuTabWidget
        from cgame_avatar_factory.hair_studio import HairStudioTab

        self.side_tab_bar = MMenuTabWidget(orientation=QtCore.Qt.Vertical, parent=self)
        self.side_tab_bar.set_dayu_size(dayu_theme.large)
        self.stack_widget = QtWidgets.QStackedWidget()

        self.hair_studio_tab = HairStudioTab(
            parent=self.stack_widget,
        )
        tab_menu_list = [
            {
                "text": "面部整形中心",
                "widget": self.base_merge_page,
                "clicked": self.show_base_merge_page,
                "checked": True,
            },
            {
                "text": "身体工坊",
                "widget": dayu_widgets.MLabel("身体工坊"),
                "clicked": self.show_body_workshop_page,
            },
            {
                "text": "材质实验室",
                "widget": self.materials_lab_page,
                "clicked": self.show_materials_lab_page,
            },
            {
                "text": "毛发工作室",
                "widget": self.hair_studio_tab,
                "clicked": self.show_hair_studio_page,
            },
            {
                "text": "动画剧场",
                "widget": self.animation_theater_page,
                "clicked": self.show_animation_theater_page,
            },
            {
                "text": "服装工房",
                "widget": dayu_widgets.MLabel("服装工房"),
                "clicked": self.show_costume_workshop_page,
            },
            {
                "text": "导出中心",
                "widget": dayu_widgets.MLabel("导出中心"),
                "clicked": self.show_export_center_page,
            },
        ]

        for id, menu in enumerate(tab_menu_list):
            self.side_tab_bar.add_menu(menu, id)
            self.stack_widget.addWidget(menu.get("widget"))

        self.loading_wrapper = MLoadingWrapper(widget=self.stack_widget, loading=False)
        maya_utils.executeDeferred(self.show_base_merge_page)

        parent_layout.setSpacing(0)
        parent_layout.setContentsMargins(0, 0, 0, 0)
        parent_layout.addWidget(self.side_tab_bar)
        parent_layout.addWidget(self.loading_wrapper)

    @hide_controllers_decorator
    def show_body_workshop_page(self):
        """Display the body workshop page."""
        self.stack_widget.setCurrentIndex(1)
        # TODO: Add body workshop specific logic here

    @hide_controllers_decorator
    def show_materials_lab_page(self):
        """Display the materials lab page."""
        self.stack_widget.setCurrentIndex(2)
        # TODO: Add materials lab specific logic here

    @hide_controllers_decorator
    def show_hair_studio_page(self):
        """Display the hair studio page."""
        self.stack_widget.setCurrentIndex(3)
        # TODO: Add hair studio specific logic here

    @hide_controllers_decorator
    def show_animation_theater_page(self):
        """Display the animation theater page."""
        self.stack_widget.setCurrentIndex(4)
        # TODO: Add animation theater specific logic here

    @hide_controllers_decorator
    def show_costume_workshop_page(self):
        """Display the costume workshop page."""
        self.stack_widget.setCurrentIndex(5)
        # TODO: Add costume workshop specific logic here

    @hide_controllers_decorator
    def show_export_center_page(self):
        """Display the export center page."""
        self.stack_widget.setCurrentIndex(6)
        # TODO: Add export center specific logic here

    def show_base_merge_page(self):
        """Display the base merge page.

        Sets up the DNA library and shows the base merge page in the stack widget.
        """
        self.base_merge_page.setup_dna_lib()
        self.stack_widget.setCurrentIndex(0)

    def setup_log_widget(self):
        """Set up the log widget dialog.

        Creates a dialog for displaying application logs with appropriate layout and title.
        """
        self.log_widget = QtWidgets.QDialog(parent=self)
        self.log_widget_layout = FramelessVLayout()
        self.log_widget.setLayout(self.log_widget_layout)
        self.log_widget.setWindowTitle("日志")

    def setup_base_merge_page(self):
        """Set up the base merge page.

        Initializes the base merge page and connects all necessary signals to their respective slots
        for handling DNA operations, area selection, mirror mode, and other UI interactions.
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.pages.base_merge import BaseMergePage

        self.base_merge_page = BaseMergePage(self)
        maya_utils.executeDeferred(self.defer_execute_setup_base_merge_page)

    def defer_execute_setup_base_merge_page(self):
        self.base_merge_page.ring.sig_dna_added.connect(self.slot_dna_added_to_ring)
        self.base_merge_page.ring.sig_dna_removed.connect(
            self.slot_dna_removed_from_ring,
        )
        self.base_merge_page.ring.sig_dna_clicked.connect(self.slot_dna_clicked_in_ring)
        self.base_merge_page.ring.sig_dna_changed.connect(self.slot_dna_changed_in_ring)

        self.base_merge_page.status_bar.sig_progress_bar_clicked.connect(
            self.slot_progress_bar_clicked,
        )
        self.base_merge_page.dna_lib.sig_dna_start_merge.connect(
            self.slot_dna_about_to_merge,
        )
        self.base_merge_page.area_select_widget.selected_buttons_changed.connect(
            self.slot_merge_area_select_changed,
        )
        self.base_merge_page.sig_weights_changed.connect(self._on_weights_changed)

    def setup_materials_lab_page(self):
        """Set up the materials laboratory page.

        Initializes the materials lab widget using the singleton pattern.
        """
        # Import local modules
        from cgame_avatar_factory.materials_lab.ui.materials_lab_widget import MaterialsLabWidget

        self.materials_lab_page = MaterialsLabWidget.instance()

    def setup_animation_theater_page(self):
        """Set up the animation theater page.

        Initializes the animation theater page widget using the singleton pattern.
        """
        # Import local modules
        from cgame_avatar_factory.animation_theater.ui.animation_theater_page import AnimationTheaterPage

        self.animation_theater_page = AnimationTheaterPage.instance()

    def _on_weights_changed(self, weights):
        """Handle changes to blend shape weights.

        Updates the merge data with new weight values when the user adjusts weights in the UI.

        Args:
            weights: Dictionary containing the updated weight values
        """
        if not self.scene_weight_mgr:
            return

        self.scene_weight_mgr.update_weights(weights)

    @QtCore.Slot(dict)
    def slot_dna_about_to_merge(self, dna_info):
        """Handle DNA about to be merged.

        Processes a DNA that is about to be added to the merge operation,
        checking limits and adding it to the appropriate component based on current tab.

        Args:
            dna_info: Dictionary containing information about the DNA to be merged
        """
        cur_tab = self.stack_widget.currentIndex()

        if cur_tab == 0:
            if len(self._dna_components) + 1 > const.DEFAULT_DNA_NUM:
                dayu_widgets.MToast.error(
                    "最多仅支持\n{}个DNA融合".format(const.DEFAULT_DNA_NUM),
                    self,
                )
                return

            dna_info["dna_order"] = len(self._dna_components) + 1
            widget = self.base_merge_page.ring.create_component(dna_info)
            self.base_merge_page.ring.add_component_to_center(widget)

        elif cur_tab == 1:
            self.logger.debug("dna added to processing, dna_data: {}".format(dna_info))

    @QtCore.Slot(dict)
    def slot_dna_added_to_ring(self, dna_data):
        """Handle DNA added to the ring.

        Processes a DNA that has been added to the DNA ring component,
        logging the event and triggering DNA settings changes.

        Args:
            dna_data: Dictionary containing information about the added DNA
        """
        self.logger.debug("DNA added to ring, dna_data: {}".format(dna_data))

        dna_path = dna_data["dna_file_path"]
        self.logger.debug(dna_path)
        self.slot_dna_settings_changed()
        self.base_merge_page.save_scene_state()
        scene_state_mgr.save()

    @QtCore.Slot(dict)
    def slot_dna_removed_from_ring(self, dna_data):
        """Handle DNA removed from the ring.

        Processes a DNA that has been removed from the DNA ring component,
        logging the event for tracking purposes.

        Args:
            dna_data: Dictionary containing information about the removed DNA
        """
        # Import local modules
        import cgame_avatar_factory.face_sculpting_center.utils.collapse_utils as collapse_utils

        self.logger.debug("DNA removed from ring, dna_data: {}".format(dna_data))
        self._dna_components = self.base_merge_page.ring.components
        if len(self._dna_components) == const.DEFAULT_DNA_NUM - 1:
            collapse_utils.delete_mesh_history_with_restore()
            collapse_utils.delete_nodes()
            self.base_merge_page.ring.setAcceptDrops(True)
            self.base_merge_page.disable_face_tabs()
        self.base_merge_page.save_scene_state()
        scene_state_mgr.save()

    @QtCore.Slot(dict)
    def slot_dna_clicked_in_ring(self, dna_data):
        """Handle DNA clicked in the ring.

        Processes a click event on a DNA in the ring component,
        logging the event for tracking purposes.

        Args:
            dna_data: Dictionary containing information about the clicked DNA
        """
        self.logger.debug("DNA clicked in ring, dna_data: {}".format(dna_data))

    @QtCore.Slot(dict)
    def slot_dna_changed_in_ring(self, dna_data):
        """Handle DNA changed in the ring.

        Processes changes to a DNA in the ring component, updating the component
        with new data and triggering DNA settings changes. Prevents changes to base DNA.

        Args:
            dna_data: Dictionary containing information about the changed DNA
        """
        order = dna_data.get("dna_order")
        if order == 0:
            dayu_widgets.MToast.warning("基础DNA\n不允许更改！", parent=self)
            return
        component = self.base_merge_page.ring.components[order]
        component.update_dna_data(dna_data)
        self.slot_dna_settings_changed()
        self.logger.debug("DNA changed in ring, dna_data: {}".format(dna_data))

    @QtCore.Slot(list)
    def slot_merge_area_select_changed(self, values):
        """Handle changes to the merge area selection.

        Updates the merge range based on selected areas and adjusts the pivot position
        based on normalized weights for the selected area.

        Args:
            values: List of selected merge area values
        """
        self.logger.debug("Merge area select changed, values: %s", values)
        if self.scene_weight_mgr:
            self.scene_weight_mgr.update_range(values)
        # Only auto-adjust pivot when a single area is selected.
        # For multiple selections, pivot adjustment is ambiguous and thus skipped.
        if len(values):
            weights = self.scene_weight_mgr.get_weights_from_scene()[values[0]]
            total = sum(weights)
            if total > 0:
                normalized_weights = [w / total for w in weights]
                self.base_merge_page.pivot_move.set_pos_by_weight(normalized_weights, block_signal=True)

    @QtCore.Slot(float)
    def slot_random_merge_factor_changed(self, factor):
        """Handle changes to the random merge factor.

        Updates the random merge values based on the provided factor.

        Args:
            factor: Float value representing the random merge factor
        """
        self.logger.debug("random merge factor changed, factor: {}".format(factor))
        self.scene_weight_mgr.set_random_merge_values(factor)

    @QtCore.Slot()
    def slot_progress_bar_clicked(self):
        """Handle progress bar click.

        Toggles the visibility of the log widget when the progress bar is clicked,
        allowing users to view or hide application logs.
        """
        self.logger.debug("progress bar clicked")
        if self.log_widget.isVisible():
            self.log_widget.hide()
        else:
            self.log_widget.show()
            self.log_widget.raise_()

    @QtCore.Slot()
    def slot_finished_dna_settings(self):
        """Handle completion of DNA settings.

        Disables drag and drop for the DNA ring, sets merge data, enables functional areas,
        and starts a timer to check animation status for building the mesh.
        """
        self.logger.debug("finished dna settings")
        self.base_merge_page.ring.setAcceptDrops(False)
        self.slot_set_merge_data()
        self.slot_enable_functional_area()

        self.check_animation_timer = QtCore.QTimer()
        self.check_animation_timer.setInterval(10)
        self.check_animation_timer.timeout.connect(self._check_animation_status)
        self.check_animation_timer.start()

    def _check_animation_status(self):
        """Check the status of DNA ring animations.

        Monitors whether the DNA ring is ready for overlay operations and
        starts a background thread to execute mesh building when ready.
        """
        if self.base_merge_page.ring.is_ready_for_overlay():
            self.check_animation_timer.stop()
            self._execute_build_mesh()

    def _execute_build_mesh(self):
        """Execute the mesh building operation.

        Builds the mesh using the current DNA file paths, disables loading indication,
        initializes the scene call, and sets default widget values.
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.build_face.customization_rig_builder import (
            CustomizationRigBuilder,
        )
        from cgame_avatar_factory.face_sculpting_center.build_face.customization_rig_builder import (
            get_build_signal_manager,
        )
        from cgame_avatar_factory.face_sculpting_center.merge.merge_data import get_merge_data_context

        signal_manager = get_build_signal_manager()
        signal_manager.progress_signal.connect(self.update_build_progress)

        try:
            # Get collapsed mode from first DNA if available
            is_collapsed_mode = False
            if self._dna_components and len(self._dna_components) > 0:
                is_collapsed_mode = self._dna_components[0].get_collapsed_model()

            builder = CustomizationRigBuilder(
                dna_paths=get_merge_data_context().dna_file_paths,
                collapsed_mode=is_collapsed_mode,
            )
            builder.build()
            self.logger.info("Build completed successfully")

        except Exception as e:
            self.logger.error(f"Build mesh failed: {e}")
            dayu_widgets.MToast.error(f"Build mesh failed: {str(e)}", self)
        finally:
            self.loading_wrapper.set_dayu_loading(False)
            signal_manager.progress_signal.disconnect(self.update_build_progress)

        self.set_widgets_default_value()

    def update_build_progress(self, progress):
        """更新BuildMesh进度条

        Args:
            progress (dict): 包含进度信息的字典，格式为 {"value": int, "text": str}
                value: 进度值，范围0-100
                text: 进度文本描述
        """
        self.logger.debug("更新BuildMesh进度: {}".format(progress))
        if hasattr(self, "base_merge_page") and hasattr(
            self.base_merge_page,
            "status_bar",
        ):
            self.base_merge_page.status_bar.progress_bar.setValue(
                progress.get("value", 0),
            )
            if "text" in progress:
                self.base_merge_page.status_bar.success_label.setText(
                    progress.get("text", ""),
                )

    @QtCore.Slot()
    def slot_set_merge_data(self):
        """Set the merge data for DNA blending.

        Initializes area weights with default values (1.0 for first DNA, 0.0 for others)
        and updates the merge data context with current area weights and DNA information.
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.merge.merge_data import get_merge_data_context

        dna_infos = []
        for dna in self._dna_components:
            filtered_dna_data = {
                "dna_file_path": dna.dna_data.get("dna_file_path"),
                "thumbnail_file_path": dna.dna_data.get("thumbnail_file_path"),
                "dna_order": dna.dna_data.get("dna_order"),
            }
            dna_infos.append(filtered_dna_data)

        get_merge_data_context().update_dna_infos(dna_infos)

        self.logger.debug("dna_infos: {}".format(get_merge_data_context().dna_infos))

    @QtCore.Slot()
    def slot_dna_settings_changed(self):
        """Handle changes to DNA settings.

        Updates the DNA list from the ring components and checks if all DNAs have valid
        file paths. Finishes DNA settings if all required DNAs are present, otherwise resets settings.
        """
        self.logger.debug("dna settings changed")
        self._dna_components = self.base_merge_page.ring.components
        if all([dna.dna_file_path for dna in self._dna_components]):
            if len(self._dna_components) == 1:
                first_dna = self._dna_components[0]
                is_collapsed_mode = first_dna.get_collapsed_model()
                self.logger.info(
                    f"first_dna: {first_dna.dna_file_path}, is_collapsed_mode: {is_collapsed_mode}",
                )

                if not is_collapsed_mode:
                    self.check_single_dna_animation_timer = QtCore.QTimer()
                    self.check_single_dna_animation_timer.setInterval(10)
                    self.check_single_dna_animation_timer.timeout.connect(
                        self.check_single_dna_animation,
                    )
                    self.check_single_dna_animation_timer.start()

                    if hasattr(self.base_merge_page, "floating_panel") and self.base_merge_page.floating_panel:
                        self.base_merge_page.floating_panel.toggle_button.setEnabled(
                            True,
                        )
                        self.base_merge_page.floating_panel.toggle_button.setToolTip(
                            "切换编辑模式",
                        )
                        self.base_merge_page.floating_panel.toggle_button.setCursor(
                            QtCore.Qt.PointingHandCursor,
                        )

            elif len(self._dna_components) == const.DEFAULT_DNA_NUM:
                self.slot_finished_dna_settings()
                self.base_merge_page.enable_face_tabs()
        else:
            self.slot_reset_dna_settings()

    @QtCore.Slot()
    def check_single_dna_animation(self):
        first_dna_widget = self.base_merge_page.ring.components[0]
        animation = getattr(first_dna_widget, "animation", None)
        if (animation is None) or (animation.state() == QAbstractAnimation.Stopped):
            self.check_single_dna_animation_timer.stop()
            self.process_single_dna()

    def _update_progress(self, value, text):
        """更新进度条和状态文本

        将重复的状态栏更新逻辑抽取为独立方法，减少代码重复

        Args:
            value (int): 进度条值（0-100）
            text (str): 状态文本
        """
        if hasattr(self, "base_merge_page") and hasattr(
            self.base_merge_page,
            "status_bar",
        ):
            self.base_merge_page.status_bar.progress_bar.setValue(value)
            self.base_merge_page.status_bar.success_label.setText(text)

    @QtCore.Slot()
    def process_single_dna(self):
        """Process single DNA file"""
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.build_face.single_dna_builder import SingleDNABuilder

        try:
            builder = SingleDNABuilder(progress_callback=self._update_progress)
            builder.build(self._dna_components[0].dna_file_path)
            self.base_merge_page.save_scene_state()
        except Exception as e:
            self.logger.error(f"Process single DNA failed: {e}")
            dayu_widgets.MToast.error(f"Process single DNA failed: {str(e)}", self)

    @QtCore.Slot()
    def slot_reset_dna_settings(self):
        """Reset DNA settings.

        Disables the functional area container and logs the reset action.
        """
        self.base_merge_page.functional_area_container.setEnabled(False)
        self.logger.debug("reset dna settings")

    @QtCore.Slot()
    def set_widgets_default_value(self):
        """Set default values for UI widgets.

        Sets the default area selection, enables mirror merge mode, and sets the default weight values.
        """
        self.logger.debug("set widgets default value")
        self.slot_set_area_select_menu_default_value()

        self.base_merge_page.pivot_move.set_pos_by_weight([1.0, 0.0, 0.0, 0.0])

    @QtCore.Slot()
    def slot_set_area_select_menu_default_value(self):
        """Set default value for area selection menu.

        Sets the area selection to 'all' by default.
        """
        self.base_merge_page.area_select_widget.select_all()

    @QtCore.Slot()
    def slot_set_preview_animation_select_menu_default_value(self):
        """Set default value for preview animation selection menu.

        Sets the animation selection to the first animation in the configuration.
        """
        animation_list = list(self.anim_config.keys())
        self.base_merge_page.preview_animation_select_menu.set_value(animation_list[0])

    @QtCore.Slot()
    def slot_enable_functional_area(self):
        """Enable the functional area container.

        Enables user interaction with the functional area and logs the action.
        """
        self.base_merge_page.functional_area_container.setEnabled(True)
        self.logger.debug("enabled functional area")

    @QtCore.Slot()
    def slot_disable_functional_area(self):
        """Disable the functional area container.

        Disables user interaction with the functional area and logs the action.
        """
        self.base_merge_page.functional_area_container.setEnabled(False)
        self.logger.debug("disabled functional area")

    def clean_data(self):
        """Clean up data and resources before closing.

        Releases resources including merge data, loggers, handlers, and UI components
        to prevent memory leaks and ensure proper application shutdown.
        """
        self.logger.debug("clean_data for close window!")
        if self.scene_weight_mgr:
            del self.scene_weight_mgr
            self.logger.debug("clean_data formerge!")

        if hasattr(self, "logger") and hasattr(self, "log_handler"):
            self.logger.removeHandler(self.log_handler)
            self.logger = None

        self.log_handler.close()
        self.log_handler = None
        self.log_controller = None

        self.base_merge_page.dna_lib = None
        self.base_merge_page = None

    def closeEvent(self, event):
        """Handle window close event.

        Called when the application window is being closed.
        The actual cleanup is handled by _handle_close_event to avoid duplication.

        Args:
            event: The close event object
        """
        # Ensure cleanup is performed (handled by _handle_close_event via eventFilter)
        self._handle_close_event()

        # Call parent closeEvent
        super().closeEvent(event)

    def _on_reset_face(self):
        """Reset the face to default state.

        Resets all face controllers to their default positions and values.
        """
        if hasattr(self, "face_controller"):
            self.face_controller.reset_controllers()

    def dockCloseEventTriggered(self):
        """
        Triggered when the dock widget is closed.
        """

    def eventFilter(self, obj, event):
        """
        Event filter for handling close, hide, and window state change events.
        """
        if obj == self:
            if event.type() == QtCore.QEvent.Close:
                self._handle_close_event()
                return False
            elif event.type() == QtCore.QEvent.Hide:
                if not self.isVisible() and not self._is_closing:
                    self._handle_close_event()
                return False
            elif event.type() == QtCore.QEvent.WindowStateChange:
                return False

        return super(MainWidget, self).eventFilter(obj, event)

    def _handle_close_event(self):
        """
        Unified handler for close events.

        Ensures that the close event is only processed once by checking
        the _is_closing flag. Also stops export monitoring.
        """
        # Import local modules
        from cgame_avatar_factory.common.reporter.event_monitor import stop_event_monitoring

        if self._is_closing:
            return
        self._is_closing = True

        # Clean up export monitoring when tool is closed
        try:
            stop_event_monitoring()
        except Exception as e:
            self.logger.error(f"Failed to stop export monitoring: {e}")
