# -*- coding: utf-8 -*-
"""
Accessibility widget containing collapsible sections for accessibility features.
"""
# Import built-in modules

# Import third-party modules
from dayu_widgets import MCollapse
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.common.ui.layout import FramelessVLayout
from cgame_avatar_factory.face_sculpting_center.ui.accessibility.ref_image_plane_widget import RefImagePlaneWidget


class AccessibilityWidget(QtWidgets.QFrame):
    """Widget for the accessibility tab with collapsible sections.

    Displays the accessibility features in the UI with various collapsible sections.
    """

    def __init__(self, parent=None):
        super(AccessibilityWidget, self).__init__(parent)
        self.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.main_layout = FramelessVLayout()
        self.main_layout.setContentsMargins(5, 5, 5, 5)
        self.setLayout(self.main_layout)

        # Ensure the widget can expand
        self.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        # Create a single MCollapse widget and add sections to it
        self.collapse_widget = MCollapse()

        # Create the reference image plane widget
        self.ref_image_plane_widget = RefImagePlaneWidget()

        # Define sections with their titles and widgets
        sections = [
            {"title": "参考图像片", "expand": True, "widget": self.ref_image_plane_widget},
            {"title": "AI优化", "expand": True, "widget": QtWidgets.QWidget()},
        ]

        # Add sections to the collapse widget
        self.collapse_widget.add_section_list(sections)

        # Add the collapse widget to the main layout
        self.main_layout.addWidget(self.collapse_widget)

        # Stretch at bottom to push collapsibles up
        self.main_layout.addStretch(1)
