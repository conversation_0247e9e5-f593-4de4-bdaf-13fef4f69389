# Import built-in modules
import copy
import json
import os

# Import third-party modules
import maya.cmds as cmds
import numpy as np

# Import local modules
import cgame_avatar_factory.common.constants as const
from cgame_avatar_factory.common.utils import utils


def compute_rigid_transform(src_points, tgt_points):
    """Compute rigid transformation between source and target point sets

    Args:
        src_points: Source point coordinates. Vertices of the stationary model
        tgt_points: Target point coordinates. Vertices of the model to be aligned

    Returns:
        ndarray: 4x4 transformation matrix
    """
    src_centroid = np.mean(src_points, axis=0)
    tgt_centroid = np.mean(tgt_points, axis=0)

    src_centered = src_points - src_centroid
    tgt_centered = tgt_points - tgt_centroid

    src_scale = np.linalg.norm(src_centered)
    tgt_scale = np.linalg.norm(tgt_centered)
    scale = src_scale / tgt_scale if tgt_scale != 0 else 1.0

    H = np.dot(tgt_centered.T, src_centered)
    U, _, Vt = np.linalg.svd(H)
    R = np.dot(Vt.T, U.T)

    if np.linalg.det(R) < 0:
        Vt[-1, :] *= -1
        R = np.dot(Vt.T, U.T)
    translation = src_centroid - scale * np.dot(R, tgt_centroid)
    transform = np.eye(4)
    transform[:3, :3] = scale * R
    transform[:3, 3] = translation

    return transform


def apply_transform(mesh_data, transform_matrix):
    """Apply transformation matrix to mesh vertices

    Args:
        mesh_data: Dictionary of mesh vertices
        transform_matrix: 4x4 transformation matrix

    Returns:
        dict: Transformed mesh data
    """
    for mesh_name, vertices in mesh_data.items():
        transformed_vertices = []
        for vertex in vertices:
            point = np.array([vertex[0], vertex[1], vertex[2], 1.0])
            transformed_point = np.dot(transform_matrix, point)
            transformed_vertices.append(
                [
                    transformed_point[0],
                    transformed_point[1],
                    transformed_point[2],
                ],
            )
        mesh_data[mesh_name] = transformed_vertices

    return mesh_data


def apply_transform_to_vertices(mesh_data, transform_matrix, vertex_indices):
    """Apply transformation matrix only to specific vertices in a mesh

    Args:
        mesh_data: Dictionary of mesh vertices
        transform_matrix: 4x4 transformation matrix
        vertex_indices: List of vertex indices to transform

    Returns:
        dict: Mesh data with transformed vertices
    """
    result = {}
    vertex_indices = np.array(vertex_indices)

    for mesh_name, vertices in mesh_data.items():
        transformed_vertices = vertices.copy()

        valid_indices = vertex_indices[vertex_indices < len(vertices)]

        if len(valid_indices) > 0:
            vertices_array = np.asarray(vertices)[valid_indices]

            homogeneous_vertices = np.hstack((vertices_array, np.ones((len(vertices_array), 1))))

            transformed_points = np.dot(homogeneous_vertices, transform_matrix.T)

            for i, vid in enumerate(valid_indices):
                transformed_vertices[vid] = transformed_points[i, :3].tolist()

        result[mesh_name] = transformed_vertices

    return result


def start_align(mesh_data):
    """Align meshes using rigid transformation

    Args:
        mesh_data: Dictionary containing mesh vertex data

    Returns:
        list: Aligned mesh data
    """

    def _to_serializable(obj):
        """Recursively convert numpy arrays to Python built-in serialisable types."""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, dict):
            return {k: _to_serializable(v) for k, v in obj.items()}
        if isinstance(obj, list):
            return [_to_serializable(v) for v in obj]
        return obj

    # Calculate position, rotation and scaling alignment for 4 models using Procrustes analysis
    all_head_data = []
    copy.deepcopy(mesh_data)
    for data in mesh_data:
        _data = data[const.BASE_HEAD_MESH_NAME]
        all_head_data.append(_data)

    with open(os.path.join(const.CONFIG_PATH, const.VERTEX_USED_FOR_ALIGNMENT), "r", encoding="utf-8") as file:
        vertex_indices = json.load(file)["vertex_list"]

    a_points = get_point_positions(all_head_data[0], vertex_indices)
    b_points = get_point_positions(all_head_data[1], vertex_indices)
    c_points = get_point_positions(all_head_data[2], vertex_indices)
    d_points = get_point_positions(all_head_data[3], vertex_indices)

    b_transform = compute_rigid_transform(np.array(a_points), np.array(b_points))
    c_transform = compute_rigid_transform(np.array(a_points), np.array(c_points))
    d_transform = compute_rigid_transform(np.array(a_points), np.array(d_points))

    mesh_b_data = apply_transform(mesh_data[1], b_transform)
    mesh_c_data = apply_transform(mesh_data[2], c_transform)
    mesh_d_data = apply_transform(mesh_data[3], d_transform)

    mesh_data[1] = mesh_b_data
    mesh_data[2] = mesh_c_data
    mesh_data[3] = mesh_d_data

    face_align_mesh_data = apply_facial_region_alignment(mesh_data)
    mesh_deltas_data = calculate_mesh_alignment_deltas(mesh_data, face_align_mesh_data)

    return mesh_deltas_data, face_align_mesh_data


def get_point_positions(source, indices):
    """Generic function to get vertex positions

    Args:
        source: Data source (Maya object name, or list of vertex positions)
        indices: List of vertex indices to retrieve

    Returns:
        list: List of coordinates as numpy arrays

    Examples:
        # Get from Maya object
        get_point_positions("pCube1", [0,1,2])

        # Get from list of vertex positions
        get_point_positions(vertex_positions_list, [0,1,2])
    """
    if isinstance(source, str):
        # Maya object mode
        return [
            np.array(
                cmds.xform(
                    f"{source}.vtx[{i}]",
                    q=True,
                    t=True,
                    ws=True,
                ),
            )
            for i in indices
        ]
    elif isinstance(source, list):
        # List of vertex positions mode
        return [np.array(source[i]) for i in indices]
    else:
        raise ValueError(
            "Invalid source type, supported types are string (Maya object), dictionary (pre-stored data), or list"
            " (vertex positions)",
        )


def apply_facial_region_alignment(source_mesh_collection):
    """Apply facial region alignment using rigid body transformations between mesh models.

    This function performs mesh alignment by computing rigid transformations for each facial
    region (eyes, mouth, etc.) between a base model and target models, then applies these
    transformations to both head vertices and associated accessory models (eyeballs, teeth).

    Args:
        source_mesh_collection (list): Collection of mesh data dictionaries where:
            - First element [0]: Base/reference mesh model
            - Remaining elements [1:]: Target mesh models to be aligned
            Each mesh dictionary contains vertex position data keyed by model names.

    Returns:
        list: Deep copy of input mesh collection with aligned vertex positions applied.
              Maintains the same structure as input but with transformed coordinates.
    """
    aligned_mesh_collection = copy.deepcopy(source_mesh_collection)

    vertex_weights_file_path = _get_vertex_weights_file_path()
    facial_region_weights = utils.read_json(vertex_weights_file_path)
    base_head_region_weights = facial_region_weights[const.BASE_HEAD_MESH_NAME]
    reference_head_mesh = source_mesh_collection[0][const.BASE_HEAD_MESH_NAME]

    for facial_region_name, region_vertex_weights in base_head_region_weights.items():
        if not region_vertex_weights:
            continue

        region_vertex_indices = np.array([int(vertex_id) for vertex_id in region_vertex_weights.keys()])
        region_blend_weights = np.array(list(region_vertex_weights.values()))
        reference_region_positions = np.array(get_point_positions(reference_head_mesh, region_vertex_indices))

        for target_model_index, target_mesh_data in enumerate(source_mesh_collection[1:], start=1):
            target_head_mesh = target_mesh_data[const.BASE_HEAD_MESH_NAME]
            target_region_positions = np.array(get_point_positions(target_head_mesh, region_vertex_indices))
            rigid_transformation_matrix = compute_rigid_transform(reference_region_positions, target_region_positions)

            _apply_accessory_model_transformation(
                facial_region_name,
                rigid_transformation_matrix,
                facial_region_weights,
                target_mesh_data,
                aligned_mesh_collection[target_model_index],
            )
            _apply_weighted_head_transformation(
                facial_region_name,
                rigid_transformation_matrix,
                target_head_mesh,
                region_vertex_indices,
                region_blend_weights,
                aligned_mesh_collection[target_model_index],
            )

    return aligned_mesh_collection


def calculate_mesh_alignment_deltas(face_align_mesh_data, original_mesh_data):
    """计算面部对齐后的网格数据与原始网格数据的差值

    Args:
        face_align_mesh_data (list): 面部对齐后的网格数据
        original_mesh_data (list): 原始网格数据

    Returns:
        list: 每个模型的顶点位置差值数据，结构与输入数据相同
              差值 = face_align_mesh_data - original_mesh_data
    """
    if len(face_align_mesh_data) != len(original_mesh_data):
        raise ValueError("face_align_mesh_data 和 original_mesh_data 的长度必须相同")

    mesh_deltas = []

    for model_index in range(len(face_align_mesh_data)):
        if model_index == 0:
            mesh_deltas.append(face_align_mesh_data[0])
            continue
        face_align_model = face_align_mesh_data[model_index]
        original_model = original_mesh_data[model_index]
        model_delta = {}

        for mesh_name in face_align_model:
            if mesh_name not in original_model:
                continue

            face_align_vertices = face_align_model[mesh_name]
            original_vertices = original_model[mesh_name]
            for vertex_index, vertex in enumerate(face_align_vertices):
                delta = [
                    vertex[i] - original_vertices[vertex_index][i] + face_align_mesh_data[0][mesh_name][vertex_index][i]
                    for i in range(3)
                ]

                if mesh_name not in model_delta:
                    model_delta[mesh_name] = []
                model_delta[mesh_name].append(delta)

        mesh_deltas.append(model_delta)

    return mesh_deltas


def _get_vertex_weights_file_path():
    config_weights_path = os.path.join(const.CONFIG_PATH, const.WEIGHT_JSON_PATH)
    if os.path.exists(config_weights_path):
        return config_weights_path
    return os.path.join(const.FACE_RESOURCE_ROOT, const.WEIGHT_JSON_PATH)


def _apply_accessory_model_transformation(
    facial_region_name,
    transformation_matrix,
    facial_region_weights,
    source_mesh_data,
    target_aligned_mesh_data,
):
    """Apply rigid transformation to accessory models associated with a facial region.

    Processes accessory models (eyeballs, teeth, etc.) that are mapped to specific facial regions,
    applying full transformation to vertices with weight 1.0 (complete attachment to the region).

    Args:
        facial_region_name (str): Name of the facial region (e.g., 'eyes_l_blendshape', 'mouth_blendshape')
        transformation_matrix (np.ndarray): 4x4 rigid transformation matrix from region alignment
        facial_region_weights (dict): Complete vertex weights data for all models and regions
        source_mesh_data (dict): Original mesh data containing accessory model vertex positions
        target_aligned_mesh_data (dict): Target mesh data to update with transformed positions
    """
    if facial_region_name not in const.REGION_TO_MODEL_MAP:
        return

    for accessory_model_name in const.REGION_TO_MODEL_MAP[facial_region_name]:
        if accessory_model_name not in facial_region_weights or accessory_model_name not in source_mesh_data:
            continue

        if facial_region_name not in facial_region_weights[accessory_model_name]:
            continue

        region_vertex_weights = facial_region_weights[accessory_model_name][facial_region_name]
        fully_attached_vertex_indices = [
            int(vertex_id) for vertex_id, attachment_weight in region_vertex_weights.items() if attachment_weight == 1.0
        ]

        if not fully_attached_vertex_indices:
            continue

        source_accessory_mesh = source_mesh_data[accessory_model_name]
        original_vertex_positions = [source_accessory_mesh[vertex_id] for vertex_id in fully_attached_vertex_indices]
        region_transformation_data = {facial_region_name: original_vertex_positions}
        transformed_accessory_data = apply_transform(region_transformation_data, transformation_matrix)

        for vertex_index, vertex_id in enumerate(fully_attached_vertex_indices):
            target_aligned_mesh_data[accessory_model_name][vertex_id] = transformed_accessory_data[facial_region_name][
                vertex_index
            ]


def _apply_weighted_head_transformation(
    facial_region_name,
    transformation_matrix,
    source_head_mesh,
    region_vertex_indices,
    region_blend_weights,
    target_aligned_mesh_data,
):
    """Apply weighted transformation to head mesh vertices within a facial region.

    Transforms head vertices using the computed rigid transformation, then blends the result
    with original positions based on per-vertex blend weights for smooth transitions.

    Args:
        facial_region_name (str): Name of the facial region being processed
        transformation_matrix (np.ndarray): 4x4 rigid transformation matrix from region alignment
        source_head_mesh (dict): Original head mesh vertex position data
        region_vertex_indices (np.ndarray): Array of vertex indices belonging to this region
        region_blend_weights (np.ndarray): Per-vertex blend weights (0.0 to 1.0) for transformation
        target_aligned_mesh_data (dict): Target mesh data to update with blended positions
    """
    original_region_positions = [source_head_mesh[vertex_id] for vertex_id in region_vertex_indices]
    region_transformation_data = {facial_region_name: original_region_positions}

    transformed_region_data = apply_transform(region_transformation_data, transformation_matrix)
    transformed_vertex_positions = np.array(transformed_region_data[facial_region_name])

    current_vertex_positions = np.array(
        [target_aligned_mesh_data[const.BASE_HEAD_MESH_NAME][vertex_id] for vertex_id in region_vertex_indices],
    )

    blended_vertex_positions = transformed_vertex_positions * region_blend_weights[
        :, np.newaxis
    ] + current_vertex_positions * (1 - region_blend_weights[:, np.newaxis])

    for vertex_index, vertex_id in enumerate(region_vertex_indices):
        target_aligned_mesh_data[const.BASE_HEAD_MESH_NAME][vertex_id] = blended_vertex_positions[vertex_index].tolist()
