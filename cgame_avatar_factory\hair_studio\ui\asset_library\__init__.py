"""Asset Library Module.

This module provides different implementations of the asset library widget
for the Hair Studio tool.

Available implementations:
- AssetLibrary: Original implementation
- AssetLibraryOptimized: Current optimized implementation (default)
- ResponsiveAssetLibrary: New QListView-based implementation

The implementation used can be controlled via the HAIR_STUDIO_USE_LISTVIEW_LIBRARY
environment variable in BaseHairTab.
"""

# Import available implementations
# Note: Original AssetLibrary may not exist in all environments
try:
    # Import local modules
    from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.responsive_asset_library import (
        ResponsiveAssetLibrary,
    )
except ImportError:
    ResponsiveAssetLibrary = None

# Import local modules
# Import sub-components for advanced usage
from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation import AssetGridView
from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation import AssetItemDelegate
from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation import Asset<PERSON>istModel
from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.responsive_asset_library import (
    ResponsiveAssetLibrary,
)

# Build __all__ list dynamically based on what's available
__all__ = [
    # Main implementations
    "ResponsiveAssetLibrary",
    # Sub-components
    "AssetListModel",
    "AssetGridView",
    "AssetItemDelegate",
]
