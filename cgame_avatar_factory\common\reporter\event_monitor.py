# Import built-in modules
import logging
import sys

# Import third-party modules
import maya.OpenMaya as om
import maya.cmds as cmds

# Import local modules
from cgame_avatar_factory.common.reporter.wecom_reporter import WecomReporter

# Global storage that survives module reloads
_EVENT_MONITOR_GLOBAL_STATE = "_avatar_factory_event_monitor_state"


def _get_global_state():
    """Get global state that survives module reloads."""
    if not hasattr(sys, _EVENT_MONITOR_GLOBAL_STATE):
        setattr(
            sys,
            _EVENT_MONITOR_GLOBAL_STATE,
            {
                "initialized": False,
                "callback_ids": [],
                "is_monitoring": False,
                "export_objects": [],
                "logger": None,
                "wecom_reporter": None,
            },
        )
    return getattr(sys, _EVENT_MONITOR_GLOBAL_STATE)


def _init_globals():
    """Initialize global variables if not already initialized."""
    state = _get_global_state()

    if not state["initialized"]:
        state["logger"] = logging.getLogger(__name__)
        state["wecom_reporter"] = WecomReporter()
        state["callback_ids"] = []
        state["is_monitoring"] = False
        state["export_objects"] = []
        state["initialized"] = True


def _get_logger():
    """Get the logger instance."""
    _init_globals()
    return _get_global_state()["logger"]


def _get_wecom_reporter():
    """Get the WecomReporter instance."""
    _init_globals()
    return _get_global_state()["wecom_reporter"]


def start_monitoring():
    """Start monitoring operations."""
    state = _get_global_state()
    logger = _get_logger()

    if state["is_monitoring"]:
        logger.info("Event monitoring is already active")
        return

    # Clean up any existing callbacks first
    stop_monitoring()

    try:
        callback_ids = state["callback_ids"]

        # Monitor file operations (Export All, Save Scene As, etc.)
        callback_id = om.MSceneMessage.addCallback(
            om.MSceneMessage.kBeforeExport,
            _on_before_export,
        )
        callback_ids.append(callback_id)

        callback_id = om.MSceneMessage.addCallback(
            om.MSceneMessage.kAfterExport,
            _on_after_export,
        )
        callback_ids.append(callback_id)

        state["is_monitoring"] = True
        logger.info("Export monitoring started successfully")

    except Exception as e:
        logger.error(f"Failed to start export monitoring: {e}")
        stop_monitoring()


def stop_monitoring():
    """Stop monitoring operations."""
    state = _get_global_state()
    logger = _get_logger()
    callback_ids = state["callback_ids"]

    for callback_id in callback_ids:
        try:
            om.MMessage.removeCallback(callback_id)
        except Exception as e:
            logger.warning(f"Failed to remove callback {callback_id}: {e}")

    callback_ids.clear()
    state["is_monitoring"] = False
    logger.info("Event monitoring stopped")


def _on_before_export(*args):
    """Callback for before export operations."""
    try:
        logger = _get_logger()
        export_type = _detect_export_type()
        export_objects = _get_export_objects()

        # Store export objects in global state for after export callback
        state = _get_global_state()
        state["export_objects"] = export_objects

        logger.info(f"Export operation starting: {export_type}, Objects: {export_objects}")
    except Exception as e:
        _get_logger().error(f"Error in before export callback: {e}")


def _on_after_export(*args):
    """Callback for after export operations."""
    try:
        _get_logger()
        export_type = _detect_export_type()

        # Get export objects from global state
        state = _get_global_state()
        export_objects = state.get("export_objects", [])

        _report_export_operation(export_type, export_objects)
    except Exception as e:
        _get_logger().error(f"Error in after export callback: {e}")


def _detect_export_type() -> str:
    """Detect the type of export operation."""
    try:
        # Get the last few commands from Maya's command history
        history = cmds.commandEcho(query=True, state=True)

        # Check for specific export commands
        if "FBXExport" in str(history):
            return "FBX Export"
        elif "OBJExport" in str(history):
            return "OBJ Export"
        elif "gameExporter" in str(history):
            return "Game Exporter"
        elif cmds.ls(selection=True):
            return "Export Selection"
        else:
            return "Export All"
    except:
        # Fallback detection
        selection = cmds.ls(selection=True)
        return "Export Selection" if selection else "Export All"


def _get_export_objects() -> list:
    """Get list of objects being exported."""
    try:
        _get_logger()
        # Get currently selected objects
        selected_objects = cmds.ls(selection=True, long=True) or []

        if selected_objects:
            # If objects are selected, they are likely being exported
            export_objects = []
            for obj in selected_objects:
                # Get short name for better readability
                short_name = obj.split("|")[-1] if "|" in obj else obj

                # Get object type
                obj_type = cmds.nodeType(obj) if cmds.objExists(obj) else "unknown"

                # For transform nodes, check if they have mesh children
                if obj_type == "transform":
                    shapes = cmds.listRelatives(obj, shapes=True, type="mesh") or []
                    if shapes:
                        obj_type = "mesh"

                export_objects.append(f"{short_name} ({obj_type})")

            return export_objects
        else:
            # No selection - might be exporting all or specific objects
            # Try to get all visible mesh objects as a fallback
            all_meshes = cmds.ls(type="mesh", visible=True) or []
            if all_meshes:
                mesh_transforms = []
                for mesh in all_meshes[:5]:  # Limit to first 5 to avoid too long list
                    transform = cmds.listRelatives(mesh, parent=True, type="transform")
                    if transform:
                        short_name = transform[0].split("|")[-1] if "|" in transform[0] else transform[0]
                        mesh_transforms.append(f"{short_name} (mesh)")

                if len(all_meshes) > 5:
                    mesh_transforms.append(f"... and {len(all_meshes) - 5} more objects")

                return mesh_transforms

            return ["All scene objects"]

    except Exception as e:
        _get_logger().error(f"Error getting export objects: {e}")
        return ["Unknown objects"]


def _report_export_operation(export_type: str, export_objects: list):
    """Report export operation to WeChat Work."""
    try:
        logger = _get_logger()
        wecom_reporter = _get_wecom_reporter()

        # Format export objects for display
        if export_objects:
            if len(export_objects) <= 10:
                objects_str = ", ".join(export_objects)
            else:
                objects_str = ", ".join(export_objects[:10]) + f" 等{len(export_objects)}个物体"
        else:
            objects_str = "未知物体"

        message = {
            "用户": wecom_reporter.user_name,
            "项目": wecom_reporter.project_abbr,
            "操作": export_type,
            "导出物体": objects_str,
        }

        wecom_reporter.send_message(message)
        logger.info(f"Reported export operation: {export_type}, Objects: {objects_str}")
    except Exception as e:
        _get_logger().error(f"Failed to report export operation: {e}")


def start_event_monitoring():
    """Start event monitoring (convenience function).

    This function is safe to call multiple times - it will not create
    duplicate monitoring instances.
    """
    start_monitoring()


def stop_event_monitoring():
    """Stop event monitoring (convenience function)."""
    stop_monitoring()


def is_monitoring_active() -> bool:
    """Check if event monitoring is currently active."""
    return _get_global_state()["is_monitoring"]


def restart_event_monitoring():
    """Restart event monitoring (stop and start again).

    Useful for ensuring clean state when tool is reopened.
    """
    stop_monitoring()
    start_monitoring()
