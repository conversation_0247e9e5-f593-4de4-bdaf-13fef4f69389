"""Asset List Model for QListView implementation.

This module provides the data model for hair assets, replacing the manual
asset management in the original QScrollArea + QGridLayout implementation.
"""

# Import built-in modules
import logging

# Import third-party modules
from qtpy import QtCore


class AssetListModel(QtCore.QAbstractListModel):
    """Data model for hair assets.

    This model manages asset data and provides the interface for QListView
    to display and interact with hair assets. It supports filtering, dynamic
    updates, and maintains compatibility with existing asset data structures.

    Signals:
        dataUpdated: Emitted when the underlying data changes
        filterChanged: Emitted when the filter criteria changes
    """

    # Custom data roles for asset information
    AssetDataRole = QtCore.Qt.UserRole + 1  # Complete asset dict
    AssetIdRole = QtCore.Qt.UserRole + 2  # Asset ID string
    AssetTypeRole = QtCore.Qt.UserRole + 3  # Asset type string
    ThumbnailPathRole = QtCore.Qt.UserRole + 4  # Thumbnail file path

    # Signals
    dataUpdated = QtCore.Signal()
    filterChanged = QtCore.Signal(str)

    def __init__(self, parent=None):
        """Initialize the asset list model.

        Args:
            parent (QObject, optional): Parent object
        """
        super().__init__(parent)

        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Data storage
        self._assets = []  # Original asset list
        self._filtered_assets = []  # Filtered asset list (what's actually displayed)

        # Filter state management
        self._filter_text = ""
        self._filter_type = None

        # Sub-type filtering support for hair asset categorization
        self._sub_type_filter = None  # Current sub-type filter ('eyebrow', 'hair', 'beard')

        # Reserved for future tag-based filtering functionality
        self._tag_filters = []  # List of active tag filters
        self._enable_combined_filtering = False  # Enable combined filtering mode

        self._logger.debug("AssetListModel initialized")

    def rowCount(self, parent=QtCore.QModelIndex()):
        """Return the number of items in the model.

        Args:
            parent (QModelIndex): Parent index (unused for list model)

        Returns:
            int: Number of filtered assets
        """
        return len(self._filtered_assets)

    def data(self, index, role):
        """Return data for the given index and role.

        Args:
            index (QModelIndex): Model index
            role (int): Data role

        Returns:
            Various: Data for the requested role, or None if invalid
        """
        if not index.isValid() or index.row() >= len(self._filtered_assets):
            return None

        asset = self._filtered_assets[index.row()]

        if role == QtCore.Qt.DisplayRole:
            return asset.get("name", "Unknown Asset")

        elif role == QtCore.Qt.DecorationRole:
            # Return thumbnail path for delegate to handle
            return asset.get("thumbnail")

        elif role == self.AssetDataRole:
            return asset  # Return complete asset dictionary

        elif role == self.AssetIdRole:
            return asset.get("id")

        elif role == self.AssetTypeRole:
            return asset.get("asset_type", asset.get("type"))

        elif role == self.ThumbnailPathRole:
            return asset.get("thumbnail")

        return None

    def setAssets(self, assets):
        """Set the complete list of assets.

        Args:
            assets (list): List of asset dictionaries
        """
        self.beginResetModel()

        self._assets = assets[:] if assets else []
        self._apply_filters()

        self.endResetModel()

        self._logger.debug(
            f"Assets updated: {len(self._assets)} total, {len(self._filtered_assets)} filtered",
        )
        self.dataUpdated.emit()

    def getAssets(self):
        """Get the current list of all assets.

        Returns:
            list: Copy of the complete asset list
        """
        return self._assets[:]

    def getFilteredAssets(self):
        """Get the current list of filtered assets.

        Returns:
            list: Copy of the filtered asset list
        """
        return self._filtered_assets[:]

    def setFilterText(self, filter_text):
        """Set the text filter for asset names.

        Args:
            filter_text (str): Text to filter by (case-insensitive)
        """
        if self._filter_text == filter_text:
            return

        self.beginResetModel()
        self._filter_text = filter_text.lower() if filter_text else ""
        self._apply_filters()
        self.endResetModel()

        self._logger.debug(
            f"Filter text updated: '{filter_text}' -> {len(self._filtered_assets)} results",
        )
        self.filterChanged.emit(filter_text)

    def setFilterType(self, asset_type):
        """Set the type filter for assets.

        Args:
            asset_type (str, optional): Asset type to filter by, or None for all types
        """
        if self._filter_type == asset_type:
            return

        self.beginResetModel()
        self._filter_type = asset_type
        self._apply_filters()
        self.endResetModel()

        self._logger.debug(
            f"Filter type updated: '{asset_type}' -> {len(self._filtered_assets)} results",
        )

    def clearFilters(self):
        """Clear all filters and show all assets."""
        self.beginResetModel()
        self._filter_text = ""
        self._filter_type = None
        self._apply_filters()
        self.endResetModel()

        self._logger.debug(f"Filters cleared -> {len(self._filtered_assets)} results")

    def setSubTypeFilter(self, sub_type):
        """Sets the sub-type filter for asset categorization.

        Filters the asset list to show only assets of the specified sub-type.
        Triggers a model reset to update the view with filtered results.

        Args:
            sub_type (str): The sub-type to filter by ('eyebrow', 'hair', 'beard')
                          or None to show all assets.
        """
        if self._sub_type_filter != sub_type:
            self._sub_type_filter = sub_type
            self.beginResetModel()
            self._apply_filters()
            self.endResetModel()

            self._logger.debug(f"Sub-type filter set to: {sub_type} -> {len(self._filtered_assets)} results")

    def _apply_filters(self):
        """Applies all active filters to the asset list.

        Processes the complete asset list through multiple filter stages:
        sub-type filtering, tag filtering (reserved), text search, and type filtering.
        Only assets that pass all active filters are included in the filtered results.
        """
        self._filtered_assets = []

        for asset in self._assets:
            if asset is None:
                continue
            # Stage 1: Sub-type filtering (for hair asset categorization)
            if self._sub_type_filter:
                asset_sub_type = asset.get("sub_asset_type", "hair")  # Default to hair
                if asset_sub_type != self._sub_type_filter:
                    continue

            # Stage 2: Tag filtering (reserved for future implementation)
            if self._enable_combined_filtering and self._tag_filters:
                asset_tags = asset.get("tags", [])
                # Check if asset contains all required tags
                if not all(tag in asset_tags for tag in self._tag_filters):
                    continue

            # Stage 3: Text search filtering (existing functionality)
            if self._filter_text:
                asset_name = asset.get("name", "").lower()
                if self._filter_text not in asset_name:
                    continue

            # Stage 4: Asset type filtering (existing functionality)
            if self._filter_type:
                asset_type = asset.get("asset_type", asset.get("type", ""))
                if asset_type != self._filter_type:
                    continue

            # Asset passed all filter stages
            self._filtered_assets.append(asset)

    def addAsset(self, asset):
        """Add a new asset to the model.

        Args:
            asset (dict): Asset dictionary to add

        Returns:
            bool: True if asset was added successfully
        """
        if not asset or not asset.get("id"):
            self._logger.warning("Cannot add asset: missing or invalid asset data")
            return False

        # Check for duplicate ID
        for existing_asset in self._assets:
            if existing_asset.get("id") == asset.get("id"):
                self._logger.warning(
                    f"Asset with ID '{asset.get('id')}' already exists",
                )
                return False

        # Add to main list
        self._assets.append(asset)

        # Check if it passes current filters
        old_filtered_count = len(self._filtered_assets)
        self._apply_filters()
        new_filtered_count = len(self._filtered_assets)

        # If the filtered list changed, notify views
        if new_filtered_count != old_filtered_count:
            # Find the new item's position in filtered list
            for i, filtered_asset in enumerate(self._filtered_assets):
                if filtered_asset.get("id") == asset.get("id"):
                    self.beginInsertRows(QtCore.QModelIndex(), i, i)
                    self.endInsertRows()
                    break

        self._logger.debug(
            f"Asset added: {asset.get('name', 'Unknown')} (ID: {asset.get('id')})",
        )
        self.dataUpdated.emit()
        return True

    def removeAsset(self, asset_id):
        """Remove an asset from the model.

        Args:
            asset_id (str): ID of the asset to remove

        Returns:
            bool: True if asset was removed successfully
        """
        if not asset_id:
            return False

        # Find and remove from main list
        removed_asset = None
        for i, asset in enumerate(self._assets):
            if asset.get("id") == asset_id:
                removed_asset = self._assets.pop(i)
                break

        if not removed_asset:
            self._logger.warning(f"Asset with ID '{asset_id}' not found for removal")
            return False

        # Find and remove from filtered list if present
        for i, asset in enumerate(self._filtered_assets):
            if asset.get("id") == asset_id:
                self.beginRemoveRows(QtCore.QModelIndex(), i, i)
                self._filtered_assets.pop(i)
                self.endRemoveRows()
                break

        self._logger.debug(
            f"Asset removed: {removed_asset.get('name', 'Unknown')} (ID: {asset_id})",
        )
        self.dataUpdated.emit()
        return True

    def updateAsset(self, asset_id, updated_asset):
        """Update an existing asset in the model.

        Args:
            asset_id (str): ID of the asset to update
            updated_asset (dict): Updated asset data

        Returns:
            bool: True if asset was updated successfully
        """
        if not asset_id or not updated_asset:
            return False

        # Find and update in main list
        updated_index = -1
        for i, asset in enumerate(self._assets):
            if asset.get("id") == asset_id:
                self._assets[i] = updated_asset
                updated_index = i
                break

        if updated_index == -1:
            self._logger.warning(f"Asset with ID '{asset_id}' not found for update")
            return False

        # Reapply filters and update filtered list
        old_filtered_assets = self._filtered_assets[:]
        self._apply_filters()

        # Find the asset in old and new filtered lists
        old_filtered_index = -1
        new_filtered_index = -1

        for i, asset in enumerate(old_filtered_assets):
            if asset.get("id") == asset_id:
                old_filtered_index = i
                break

        for i, asset in enumerate(self._filtered_assets):
            if asset.get("id") == asset_id:
                new_filtered_index = i
                break

        # Handle different update scenarios
        if old_filtered_index >= 0 and new_filtered_index >= 0:
            # Asset was and still is in filtered list - update in place
            if old_filtered_index == new_filtered_index:
                index = self.createIndex(new_filtered_index, 0)
                self.dataChanged.emit(index, index)
            else:
                # Position changed - remove and re-add
                self.beginRemoveRows(
                    QtCore.QModelIndex(),
                    old_filtered_index,
                    old_filtered_index,
                )
                self.endRemoveRows()
                self.beginInsertRows(
                    QtCore.QModelIndex(),
                    new_filtered_index,
                    new_filtered_index,
                )
                self.endInsertRows()

        elif old_filtered_index >= 0 and new_filtered_index == -1:
            # Asset was in filtered list but no longer passes filters - remove
            self.beginRemoveRows(
                QtCore.QModelIndex(),
                old_filtered_index,
                old_filtered_index,
            )
            self.endRemoveRows()

        elif old_filtered_index == -1 and new_filtered_index >= 0:
            # Asset wasn't in filtered list but now passes filters - add
            self.beginInsertRows(
                QtCore.QModelIndex(),
                new_filtered_index,
                new_filtered_index,
            )
            self.endInsertRows()

        self._logger.debug(
            f"Asset updated: {updated_asset.get('name', 'Unknown')} (ID: {asset_id})",
        )
        self.dataUpdated.emit()
        return True

    def getAssetById(self, asset_id):
        """Get an asset by its ID.

        Args:
            asset_id (str): Asset ID to search for

        Returns:
            dict or None: Asset data if found, None otherwise
        """
        for asset in self._assets:
            if asset.get("id") == asset_id:
                return asset
        return None

    def clear(self):
        """Clear all assets from the model."""
        self.beginResetModel()
        self._assets.clear()
        self._filtered_assets.clear()
        self.endResetModel()

        self._logger.debug("All assets cleared")
        self.dataUpdated.emit()

    # Reserved interfaces for future tag-based filtering functionality
    def setTagFilters(self, tags):
        """Sets tag-based filters for asset display (reserved for future use).

        This method is reserved for future implementation of tag-based filtering,
        where assets can be filtered by multiple tags simultaneously.

        Args:
            tags (list): List of tag strings to filter by.
        """
        if self._tag_filters != tags:
            self._tag_filters = tags.copy() if tags else []
            if self._enable_combined_filtering:
                self.beginResetModel()
                self._apply_filters()
                self.endResetModel()
                self._logger.debug(f"Tag filters set: {self._tag_filters} -> {len(self._filtered_assets)} results")

    def setCombinedFilteringEnabled(self, enabled):
        """Enables or disables combined filtering functionality (reserved for future use).

        This method is reserved for future implementation where multiple filter
        types (text, sub-type, tags) can be combined for more precise filtering.

        Args:
            enabled (bool): Whether to enable combined filtering.
        """
        if self._enable_combined_filtering != enabled:
            self._enable_combined_filtering = enabled
            self.beginResetModel()
            self._apply_filters()
            self.endResetModel()
            self._logger.debug(f"Combined filtering {'enabled' if enabled else 'disabled'}")

    def getFilterState(self):
        """Gets the current filter state for persistence or debugging.

        Returns:
            dict: Dictionary containing all current filter settings with keys:
                - sub_type_filter: Current sub-type filter
                - text_filter: Current text search filter
                - type_filter: Current asset type filter
                - tag_filters: List of active tag filters
                - combined_filtering_enabled: Whether combined filtering is active
        """
        return {
            "sub_type_filter": self._sub_type_filter,
            "text_filter": self._filter_text,
            "type_filter": self._filter_type,
            "tag_filters": self._tag_filters.copy(),
            "combined_filtering_enabled": self._enable_combined_filtering,
        }
