# Import third-party modules
from qtpy import QtWidgets


def FramelessLayout(qt_layout_cls):
    class _FramelessLayout(qt_layout_cls):
        def __init__(self, *args, **kwargs):
            super(_FramelessLayout, self).__init__(*args, **kwargs)
            self.setContentsMargins(0, 0, 0, 0)
            self.setSpacing(0)

    return _FramelessLayout


FramelessVLayout = FramelessLayout(QtWidgets.QVBoxLayout)
FramelessHLayout = FramelessLayout(QtWidgets.QHBoxLayout)
