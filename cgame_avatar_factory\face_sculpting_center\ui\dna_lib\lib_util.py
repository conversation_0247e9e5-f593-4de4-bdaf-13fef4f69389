# Import built-in modules
import os

# Import third-party modules
from qtpy import Qt<PERSON>ore
from qtpy import QtGui

# Import local modules
import cgame_avatar_factory.common.constants as const
import cgame_avatar_factory.face_sculpting_center.utils.tag_util as tag_util


def set_default_lib(property, value):
    # NOTE: find packages name
    settings = QtCore.QSettings(
        QtCore.QSettings.IniFormat,
        QtCore.QSettings.UserScope,
        const.ORGANIZATION_NAME,
        const.PACKAGE_NAME,
    )
    settings.setValue(property, value)


def get_local_lib_path():
    """
    Get the local resource path from settings

    Returns:
        str: Local resource path or empty string if not set
    """
    settings = QtCore.QSettings(
        QtCore.QSettings.IniFormat,
        QtCore.QSettings.UserScope,
        const.ORGANIZATION_NAME,
        const.PACKAGE_NAME,
    )
    return settings.value("local_lib_path", const.DEFAULT_LOCAL_LIB_PATH)


def set_local_lib_path(path):
    """
    Set the local resource path in settings

    Args:
        path (str): Path to the local resource
    """
    settings = QtCore.QSettings(
        QtCore.QSettings.IniFormat,
        QtCore.QSettings.UserScope,
        const.ORGANIZATION_NAME,
        const.PACKAGE_NAME,
    )
    settings.setValue("local_lib_path", path)


def is_role_folder(folder_path):
    """Determine whether a folder is a character folder: that is, whether there is a .dna file with the same name under the folder.

    Args:
        folder_path (str): Folder path.

    Returns:
        bool: Returns True if it is a character folder, otherwise returns False.
    """
    folder_name = os.path.basename(folder_path)
    dna_file = os.path.join(folder_path, f"{folder_name}.dna")
    return os.path.isfile(dna_file)


def find_role_folders(parent_path):
    """Recursively find all character folders under parent_path (i.e., folders containing a dna file with the same name).

    Args:
        parent_path (str): The parent directory path to search.

    Returns:
        list[str]: List of absolute paths of all character folders.
    """
    role_folders = []
    for d in os.listdir(parent_path):
        d_path = os.path.join(parent_path, d)
        if os.path.isdir(d_path):
            if is_role_folder(d_path):
                role_folders.append(d_path)
            else:
                role_folders.extend(find_role_folders(d_path))
    return role_folders


def read_resource_path():
    """Read all DNA file configurations from resource paths"""
    dna_config = []

    # List of resource paths to check
    # TODO: Future support for configuring the visibility of public library content will be required.
    #  Access to the public library is only granted when the project's character complies with MetaHuman specifications.
    resource_paths = [const.PROJECT_LIB_PATH, const.PUBLIC_LIB_PATH]

    # Add local resource path if configured
    local_path = get_local_lib_path()
    if local_path and os.path.exists(local_path):
        resource_paths.append(local_path)

    for resource_path in resource_paths:
        if not os.path.exists(resource_path):
            continue
        role_folders = find_role_folders(resource_path)
        for role_dir_path in role_folders:
            dna_files = get_lib_dna_file(role_dir_path)
            for dna_name in dna_files:
                config = const.DEFAULT_DATA_LIST.copy()
                character_name = os.path.splitext(dna_name)[0]
                config["character_name"] = character_name
                config["dna_file_path"] = os.path.join(role_dir_path, dna_name)
                thumbnail_path = os.path.join(role_dir_path, f"{config['character_name']}.png")
                if os.path.exists(thumbnail_path):
                    config["thumbnail_file_path"] = thumbnail_path
                if resource_path == const.PUBLIC_LIB_PATH:
                    config["resource_path"] = "PUBLIC_LIB_PATH"
                elif resource_path == const.PROJECT_LIB_PATH:
                    config["resource_path"] = "PROJECT_LIB_PATH"
                elif resource_path == local_path:
                    config["resource_path"] = "LOCAL_LIB_PATH"
                config["tags"] = tag_util.read_role_tags(role_dir_path, config["character_name"])
                dna_config.append(config)

    return dna_config


def get_lib_dna_file(path: str):
    if not os.path.exists(path):
        return None

    dna_files = []
    for file in os.listdir(path):
        if os.path.isfile(os.path.join(path, file)):
            file_ext = os.path.splitext(file)[1]
            if file_ext in const.SUPPORTED_DNA_FILE_EXTENSIONS:
                dna_files.append(file)

    return dna_files


def filter_dna_type(dna_list, attr_filter):
    """
    Filter DNA list based on attribute filter

    Args:
        dna_list: List of DNA information
        attr_filter: Attribute filter name, corresponding to keys in constants.DNA_TYPE_ALL

    Returns:
        Filtered DNA list
    """
    if attr_filter == const.ALL_TYPE_NAME:
        return dna_list

    if attr_filter in const.DNA_TYPE_ALL:
        filter_config = const.DNA_TYPE_ALL[attr_filter]
        match_config = filter_config.get("match", {})
        attr = match_config.get("attr")
        val = match_config.get("value")

        if attr and val is not None:
            # Determine matching resource path based on attr_filter
            if attr_filter == "公有库":
                if const.IS_METAHUMAN_SPEC:
                    return [dna for dna in dna_list if dna.get(attr) == "PUBLIC_LIB_PATH"]
                else:
                    return []
            elif attr_filter == "项目库":
                return [dna for dna in dna_list if dna.get(attr) == "PROJECT_LIB_PATH"]
            elif attr_filter == "个人库":
                return [dna for dna in dna_list if dna.get(attr) == "LOCAL_LIB_PATH"]
            else:
                return [dna for dna in dna_list if dna.get(attr) == val]

    return dna_list


def find_small_icon(dna_info):
    """
    Find small icon based on DNA information

    Args:
        dna_info: DNA information dictionary

    Returns:
        Small icon filename or None
    """
    for item in const.DNA_TYPE_ALL.values():
        attr = item["match"]["attr"]
        val = item["match"]["value"]
        small = item["small_icon"]
        if dna_info.get(attr) == val:
            return small
    return None


def merge_pixmaps(pixmap1, pixmap2, scale_factor=0.1):
    merged_pixmap = QtGui.QPixmap(pixmap1.size())
    merged_pixmap.fill(QtCore.Qt.transparent)

    painter = QtGui.QPainter(merged_pixmap)
    painter.drawPixmap(0, 0, pixmap1)

    scale_factor *= pixmap1.width() / pixmap2.width()
    scaled_pixmap2 = pixmap2.scaled(
        pixmap2.size() * scale_factor,
        QtCore.Qt.KeepAspectRatio,
        QtCore.Qt.SmoothTransformation,
    )

    painter.drawPixmap(merged_pixmap.width() - scaled_pixmap2.width(), 0, scaled_pixmap2)
    painter.end()

    return merged_pixmap
