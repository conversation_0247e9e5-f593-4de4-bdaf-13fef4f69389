"""Asset Path Resolver Module.

This module provides functionality to resolve asset-related paths based on main asset paths.
It follows the single responsibility principle by focusing solely on path resolution logic.
"""

# Import built-in modules
import logging
import os
from typing import Dict
from typing import List
from typing import Optional


def _path_exists(path: str) -> bool:
    """Check if a path exists.

    Args:
        path (str): Path to check

    Returns:
        bool: True if path exists, False otherwise
    """
    try:
        return os.path.exists(path)
    except (TypeError, ValueError):
        return False


def _normalize_path(path: Optional[str]) -> Optional[str]:
    """Normalize a file path.

    Args:
        path (Optional[str]): Path to normalize

    Returns:
        Optional[str]: Normalized path or None if input is None
    """
    if path is None:
        return None

    try:
        # Normalize path separators and resolve relative components
        normalized = os.path.normpath(path)
        # Convert to forward slashes for consistency (common in many systems)
        return normalized.replace(os.sep, "/")
    except (<PERSON><PERSON>rro<PERSON>, ValueError):
        return path


def is_path_exist_and_dir(path: str):
    """Check if a path exists and is a directory.

    Args:
        path (str): Path to check

    Returns:
        bool: True if path exists and is a directory, False otherwise
    """
    return True if _path_exists(path) and os.path.isdir(path) else False


def resolve_texture_path(fbx_path: str) -> Optional[str]:
    """Resolve texture directory path based on FBX file path.

    This function looks for a 'textures' directory in the same directory as the FBX file.
    If no texture directory is found, it returns the FBX file's directory itself.
    It follows common asset organization conventions where textures are stored alongside
    the main asset file.

    Args:
        fbx_path (str): Path to the FBX file

    Returns:
        Optional[str]: Path to the textures directory if found, otherwise the FBX directory.
                      Returns None only if the FBX file doesn't exist or path is invalid.

    Examples:
        >>> resolve_texture_path("/path/to/hair_asset.fbx")
        "/path/to/textures"  # If textures folder exists

        >>> resolve_texture_path("/path/to/hair_asset.fbx")
        "/path/to"  # If no textures folder found, returns FBX directory

        >>> resolve_texture_path("/path/to/nonexistent.fbx")
        None  # If FBX file doesn't exist
    """
    logger = logging.getLogger(__name__)

    if not fbx_path or not isinstance(fbx_path, str):
        logger.warning(f"Invalid fbx_path provided: {fbx_path}")
        return None

    if not _path_exists(fbx_path):
        logger.warning(f"FBX file does not exist: {fbx_path}")
        return None

    # Get the directory containing the FBX file
    fbx_dir = os.path.dirname(fbx_path)

    # Look for textures directory in the same directory
    texture_dir = os.path.join(fbx_dir, "textures")

    _check_exist = is_path_exist_and_dir(texture_dir)
    if _check_exist:
        return _normalize_path(texture_dir)

    # Try alternative naming conventions
    alternative_names = ["texture", "Textures", "Texture", "tex", "maps"]
    for alt_name in alternative_names:
        alt_texture_dir = os.path.join(fbx_dir, alt_name)

        _check_exist = is_path_exist_and_dir(alt_texture_dir)
        if _check_exist:
            return _normalize_path(alt_texture_dir)

    # If no texture directory found, return the FBX directory itself
    logger.debug(f"No texture directory found for FBX: {fbx_path}, returning FBX directory: {fbx_dir}")
    return _normalize_path(fbx_dir)


def resolve_related_assets(asset_path: str, asset_types: Optional[List[str]] = None) -> Dict[str, Optional[str]]:
    """Resolve paths for related assets based on main asset path.

    This function can be extended to resolve various types of related assets
    like textures, materials, animations, etc.

    Args:
        asset_path (str): Path to the main asset file
        asset_types (Optional[List[str]]): List of asset types to resolve.
                                         Defaults to ['textures']

    Returns:
        Dict[str, Optional[str]]: Dictionary mapping asset types to their resolved paths

    Examples:
        >>> resolve_related_assets("/path/to/hair.fbx")
        {"textures": "/path/to/textures"}  # If textures folder exists

        >>> resolve_related_assets("/path/to/hair.fbx")
        {"textures": "/path/to"}  # If no textures folder, returns FBX directory

        >>> resolve_related_assets("/path/to/hair.fbx", ["textures", "materials"])
        {"textures": "/path/to/textures", "materials": None}
    """
    logger = logging.getLogger(__name__)

    if asset_types is None:
        asset_types = ["textures"]

    resolved_paths = {}

    for asset_type in asset_types:
        if asset_type == "textures":
            resolved_paths[asset_type] = resolve_texture_path(asset_path)
        else:
            # Placeholder for future asset type resolution
            logger.debug(f"Asset type '{asset_type}' resolution not implemented yet")
            resolved_paths[asset_type] = None

    return resolved_paths


def validate_texture_directory(texture_path: str, required_extensions: Optional[List[str]] = None) -> bool:
    """Validate if a texture directory contains expected texture files.

    Args:
        texture_path (str): Path to the texture directory
        required_extensions (Optional[List[str]]): List of required file extensions.
                                                 Defaults to common texture formats.

    Returns:
        bool: True if directory contains valid texture files, False otherwise
    """
    logger = logging.getLogger(__name__)

    if required_extensions is None:
        required_extensions = [".png", ".jpg", ".jpeg", ".tga", ".tif", ".tiff", ".exr", ".bmp", ".dds"]

    if not _path_exists(texture_path) or not os.path.isdir(texture_path):
        return False

    try:
        files = os.listdir(texture_path)
        texture_files = [f for f in files if any(f.lower().endswith(ext) for ext in required_extensions)]

        if texture_files:
            logger.debug(f"Found {len(texture_files)} texture files in {texture_path}")
            return True
        else:
            logger.warning(f"No texture files found in {texture_path}")
            return False

    except OSError as e:
        logger.error(f"Error accessing texture directory {texture_path}: {e}")
        return False


def get_texture_files_info(texture_path: str) -> Dict[str, List[str]]:
    """Get information about texture files in a directory.

    Args:
        texture_path (str): Path to the texture directory

    Returns:
        Dict[str, List[str]]: Dictionary with file extensions as keys and
                             lists of matching files as values
    """
    logger = logging.getLogger(__name__)

    if not _path_exists(texture_path) or not os.path.isdir(texture_path):
        return {}

    texture_info = {}
    common_extensions = [".png", ".jpg", ".jpeg", ".tga", ".tif", ".tiff", ".exr", ".bmp", ".dds"]

    try:
        files = os.listdir(texture_path)

        for ext in common_extensions:
            matching_files = [f for f in files if f.lower().endswith(ext)]
            if matching_files:
                texture_info[ext] = matching_files

    except OSError as e:
        logger.error(f"Error reading texture directory {texture_path}: {e}")

    return texture_info


# Convenience function for backward compatibility and ease of use
def get_texture_path_for_hair_asset(fbx_path: str) -> Optional[str]:
    """Convenience function specifically for hair assets.

    This is a wrapper around resolve_texture_path with hair-specific logic
    and validation.

    Args:
        fbx_path (str): Path to the hair FBX file

    Returns:
        Optional[str]: Path to the texture directory if valid, None otherwise
    """
    texture_path = resolve_texture_path(fbx_path)

    if texture_path and validate_texture_directory(texture_path):
        return texture_path

    return None
