# Import built-in modules
import json
import os

# Import third-party modules
from dayu_widgets import MLabel
import maya.cmds as cmds
import maya.mel as mel
from qtpy import QtCore
from qtpy import QtGui
from qtpy import QtWidgets

# Import local modules
import cgame_avatar_factory.common.constants as const
from cgame_avatar_factory.common.ui.layout import FramelessV<PERSON>ayout
from cgame_avatar_factory.common.ui.mixin.style_mixin import MStyleMixin
from cgame_avatar_factory.common.utils import custom_topo_processing
from cgame_avatar_factory.face_sculpting_center import constants as face_const


class ZoomableImageLabel(QtWidgets.QLabel):
    """简单的图片显示标签"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlignment(QtCore.Qt.AlignCenter)
        self.setMinimumSize(300, 200)

        self.original_pixmap = None

        self.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

        self.parent_widget = None

    def set_image(self, pixmap):
        if pixmap and not pixmap.isNull():
            self.original_pixmap = pixmap
            widget_size = self.size()
            scaled_pixmap = pixmap.scaled(
                widget_size,
                QtCore.Qt.KeepAspectRatio,
                QtCore.Qt.SmoothTransformation,
            )
            self.setPixmap(scaled_pixmap)
        else:
            self.original_pixmap = None
            self.clear()

    def resizeEvent(self, event):
        super().resizeEvent(event)
        if self.original_pixmap:
            widget_size = self.size()
            scaled_pixmap = self.original_pixmap.scaled(
                widget_size,
                QtCore.Qt.KeepAspectRatio,
                QtCore.Qt.SmoothTransformation,
            )
            self.setPixmap(scaled_pixmap)

    def show_context_menu(self, position):
        context_menu = QtWidgets.QMenu(self)

        refresh_action = context_menu.addAction("更新贴图")
        refresh_action.triggered.connect(self.refresh_texture_paths)

        open_transfermap_action = context_menu.addAction("打开文件")
        open_transfermap_action.triggered.connect(self.open_transfermap_folder)

        context_menu.exec_(self.mapToGlobal(position))

    def refresh_texture_paths(self):
        if self.parent_widget and hasattr(self.parent_widget, "refresh_texture_paths"):
            self.parent_widget.refresh_texture_paths()

    def reset_view(self):
        if self.original_pixmap:
            widget_size = self.size()
            scaled_pixmap = self.original_pixmap.scaled(
                widget_size,
                QtCore.Qt.KeepAspectRatio,
                QtCore.Qt.SmoothTransformation,
            )
            self.setPixmap(scaled_pixmap)

    def open_transfermap_folder(self):
        transfermap_folder = custom_topo_processing._get_transfermap_folder()
        if os.path.exists(transfermap_folder):
            normalized_path = os.path.normpath(transfermap_folder)
            os.startfile(normalized_path)

    def set_parent_widget(self, parent_widget):
        self.parent_widget = parent_widget


class BakePreviewWidget(QtWidgets.QWidget):
    BUTTON_SIZE = 240
    PREVIEW_SIZE = (300, 200)
    TITLE_HEIGHT = 50

    def __init__(self, parent=None):
        super().__init__(parent)
        self.model_wrap_widget = None
        self.texture_paths = []
        self.current_texture_index = 0
        self.setup_ui()
        self.load_texture_paths_from_maya()

        custom_topo_processing.register_texture_update_callback(self.on_texture_update_callback)

    def create_section_title(self, text):
        title = MStyleMixin.instance_wrapper(MLabel(text))
        title.foreground_color(const.DAYU_PRIMARY_TEXT_COLOR)
        title.setAlignment(QtCore.Qt.AlignCenter)
        title.setFixedHeight(self.TITLE_HEIGHT)
        title.setContentsMargins(0, 5, 0, 5)

        font = title.font()
        font.setPointSize(font.pointSize() + 2)
        font.setBold(True)
        title.setFont(font)

        title.setStyleSheet(
            f"""
            QFrame {{
                background-color: {const.BUTTON_BG};
                border: 2px solid {const.BUTTON_HOVER_BG};
                border-radius: 8px;
                min-height: {self.TITLE_HEIGHT}px;
                max-height: {self.TITLE_HEIGHT}px;
            }}
        """
        )

        return title

    def _create_collapsible_bake_panel(self):
        panel = QtWidgets.QWidget()
        panel.setFixedWidth(280)
        panel.setSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        panel.setAttribute(QtCore.Qt.WA_TransparentForMouseEvents, False)
        panel.setStyleSheet(
            f"""
            QWidget {{
                background-color: rgba(50, 50, 50, 200);
                border: 1px solid {const.BUTTON_BORDER};
                border-radius: 6px;
            }}
        """
        )

        main_layout = QtWidgets.QVBoxLayout(panel)
        main_layout.setContentsMargins(12, 12, 12, 12)
        main_layout.setSpacing(8)

        self.bake_type_combo = QtWidgets.QComboBox()
        self.bake_type_combo.addItems(["预览采样", "低等采样", "中等采样", "高等采样"])
        self.bake_type_combo.setCurrentIndex(1)
        self.bake_type_combo.setAttribute(QtCore.Qt.WA_TransparentForMouseEvents, False)
        self._setup_combo_style(self.bake_type_combo)
        main_layout.addWidget(self.bake_type_combo)

        self.bake_quality_combo = QtWidgets.QComboBox()
        self.bake_quality_combo.addItems(["512", "1024", "2048", "4096"])
        self.bake_quality_combo.setCurrentIndex(2)
        self.bake_quality_combo.setAttribute(QtCore.Qt.WA_TransparentForMouseEvents, False)
        self._setup_combo_style(self.bake_quality_combo)
        main_layout.addWidget(self.bake_quality_combo)

        self.bake_button = QtWidgets.QPushButton("开始烘焙")
        self.bake_button.setFixedHeight(42)
        self.bake_button.setAttribute(QtCore.Qt.WA_TransparentForMouseEvents, False)
        self.bake_button.setStyleSheet(
            f"""
            QPushButton {{
                background-color: {const.PRIMARY_COLOR};
                border: 1px solid {const.BUTTON_BORDER};
                border-radius: 4px;
                color: {const.DAYU_PRIMARY_TEXT_COLOR};
                font-size: 18px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {const.DAYU_BACKGROUND_SELECTED_TEXT_COLOR};
            }}
            QPushButton:pressed {{
                background-color: {const.SUCCESS_COLOR};
            }}
        """
        )
        self.bake_button.clicked.connect(self._on_check_wrap_clicked)
        main_layout.addWidget(self.bake_button)

        panel.adjustSize()

        return panel

    def _setup_combo_style(self, combo):
        combo.setFixedHeight(38)
        combo.setStyleSheet(
            f"""
            QComboBox {{
                background-color: {const.BUTTON_BG};
                border: 1px solid {const.BUTTON_BORDER};
                border-radius: 4px;
                padding: 6px 10px;
                color: {const.DAYU_PRIMARY_TEXT_COLOR};
                font-size: 18px;
                min-height: 20px;
            }}
            QComboBox:hover {{
                background-color: {const.BUTTON_HOVER_BG};
                border-color: {const.CONTROL_POINT_COLOR};
            }}
            QComboBox:focus {{
                border-color: {const.CONTROL_POINT_COLOR};
                outline: none;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
                padding-right: 4px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {const.DAYU_PRIMARY_TEXT_COLOR};
                margin-right: 2px;
            }}
            QComboBox QAbstractItemView {{
                background-color: {const.BUTTON_BG};
                border: 1px solid {const.BUTTON_BORDER};
                selection-background-color: {const.BUTTON_HOVER_BG};
                color: {const.DAYU_PRIMARY_TEXT_COLOR};
                padding: 4px;
            }}
        """
        )

    def setup_ui(self):
        main_layout = FramelessVLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)
        self.setLayout(main_layout)

        title = self.create_section_title("贴图烘焙")
        main_layout.addWidget(title)

        preview_container = self._create_preview_container()
        main_layout.addWidget(preview_container)

    def _create_preview_container(self):
        container = QtWidgets.QFrame()
        container.setFrameShape(QtWidgets.QFrame.NoFrame)
        container.setStyleSheet(
            f"""
            QFrame {{
                border: 1px solid {const.BUTTON_BORDER};
                border-radius: 8px;
                background-color: {const.DAYU_BG_COLOR};
                padding: 10px;
            }}
        """
        )

        layout = FramelessVLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        container.setLayout(layout)

        preview_stack = self._create_preview_stack()
        layout.addWidget(preview_stack)

        return container

    def _create_preview_stack(self):
        stack = QtWidgets.QStackedWidget()
        stack.setMinimumSize(*self.PREVIEW_SIZE)

        preview_page = self._create_preview_with_controls()
        stack.addWidget(preview_page)

        stack.setCurrentWidget(preview_page)
        return stack

    def _create_preview_with_controls(self):
        container = QtWidgets.QWidget()
        container.setMinimumSize(*self.PREVIEW_SIZE)

        layout = QtWidgets.QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        self.texture_preview_label = self._create_preview_label()
        layout.addWidget(self.texture_preview_label)

        overlay_container = QtWidgets.QWidget(container)
        overlay_container.setStyleSheet("background: transparent;")
        overlay_container.setAttribute(QtCore.Qt.WA_TransparentForMouseEvents, True)

        self.bake_control_panel = self._create_collapsible_bake_panel()
        self.bake_control_panel.setParent(container)
        self.bake_control_panel.setAttribute(QtCore.Qt.WA_TransparentForMouseEvents, False)
        self.bake_control_panel.move(10, 10)
        self.bake_control_panel.show()
        self.bake_control_panel.raise_()

        self.prev_texture_button = self._create_navigation_button("◀", "上一张贴图")
        self.prev_texture_button.setParent(container)
        self.prev_texture_button.setAttribute(QtCore.Qt.WA_TransparentForMouseEvents, False)
        self.prev_texture_button.setWindowFlags(QtCore.Qt.Widget | QtCore.Qt.FramelessWindowHint)
        self.prev_texture_button.show()
        self.prev_texture_button.raise_()
        self.prev_texture_button.clicked.connect(self._on_prev_texture_clicked)

        self.next_texture_button = self._create_navigation_button("▶", "下一张贴图")
        self.next_texture_button.setParent(container)
        self.next_texture_button.setAttribute(QtCore.Qt.WA_TransparentForMouseEvents, False)
        self.next_texture_button.setWindowFlags(QtCore.Qt.Widget | QtCore.Qt.FramelessWindowHint)
        self.next_texture_button.show()
        self.next_texture_button.raise_()
        self.next_texture_button.clicked.connect(self._on_next_texture_clicked)

        overlay_container.setParent(container)
        overlay_container.setGeometry(0, 0, *self.PREVIEW_SIZE)

        self.overlay_container = overlay_container

        def on_resize():
            if hasattr(self, "overlay_container") and self.overlay_container:
                size = self.texture_preview_label.size()
                self.overlay_container.setGeometry(0, 0, size.width(), size.height())
                self._update_navigation_buttons_position()

        self.texture_preview_label.resizeEvent = lambda event: (
            QtWidgets.QLabel.resizeEvent(self.texture_preview_label, event),
            on_resize(),
        )[-1]

        self._update_navigation_buttons_position()

        self.prev_texture_button.raise_()
        self.next_texture_button.raise_()

        return container

    def _create_preview_label(self):
        label = ZoomableImageLabel()
        label.setText("贴图预览区域")
        label.setToolTip("贴图预览区域\n• 左右按钮: 切换贴图\n• 右键菜单: 更新贴图路径")
        label.set_parent_widget(self)
        label.setStyleSheet(
            f"""
            QLabel {{
                border: 2px dashed {const.SLIDER_BORDER};
                border-radius: 4px;
                background-color: {const.UI_COLOR_BG_DARKER};
                color: {const.DAYU_SECONDARY_TEXT_COLOR};
                font-size: 14px;
            }}
        """
        )
        return label

    def _create_navigation_button(self, text, tooltip):
        button = QtWidgets.QPushButton(text)
        button.setFixedSize(50, 50)
        button.setToolTip(tooltip)
        button.setStyleSheet(
            f"""
            QPushButton {{
                background-color: rgba(60, 60, 60, 180);
                border: 1px solid {const.BUTTON_BORDER};
                border-radius: 25px;
                color: {const.DAYU_PRIMARY_TEXT_COLOR};
                font-size: 20px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: rgba(80, 80, 80, 200);
                border-color: {const.CONTROL_POINT_COLOR};
            }}
            QPushButton:pressed {{
                background-color: rgba(100, 100, 100, 220);
            }}
        """
        )
        return button

    def _update_navigation_buttons_position(self):
        if not hasattr(self, "prev_texture_button") or not hasattr(self, "next_texture_button"):
            return

        if not hasattr(self, "texture_preview_label"):
            return

        preview_size = self.texture_preview_label.size()
        button_size = 60
        margin = 10

        left_x = margin
        left_y = (preview_size.height() - button_size) // 2
        self.prev_texture_button.move(left_x, left_y)
        self.prev_texture_button.raise_()
        self.prev_texture_button.repaint()

        right_x = preview_size.width() - button_size - margin
        right_y = (preview_size.height() - button_size) // 2
        self.next_texture_button.move(right_x, right_y)
        self.next_texture_button.raise_()
        self.next_texture_button.repaint()

        self.prev_texture_button.setAttribute(QtCore.Qt.WA_TransparentForMouseEvents, False)
        self.next_texture_button.setAttribute(QtCore.Qt.WA_TransparentForMouseEvents, False)
        self.prev_texture_button.raise_()
        self.next_texture_button.raise_()

        if hasattr(self, "bake_control_panel"):
            self.bake_control_panel.raise_()

        self.update_navigation_buttons_visibility()

    def _on_prev_texture_clicked(self):
        if self.texture_paths:
            self.current_texture_index = (self.current_texture_index - 1) % len(self.texture_paths)
            self.display_current_texture()

    def _on_next_texture_clicked(self):
        if self.texture_paths:
            self.current_texture_index = (self.current_texture_index + 1) % len(self.texture_paths)
            self.display_current_texture()

    def set_model_wrap_widget(self, model_wrap_widget):
        self.model_wrap_widget = model_wrap_widget
        if hasattr(model_wrap_widget, "drop_canvas"):
            model_wrap_widget.drop_canvas.icon_deleted.connect(self._on_wrap_icon_deleted)

    def _on_check_wrap_clicked(self):
        if self._has_wrap_object():
            self.model_wrap_widget.drop_canvas

            bake_type = self.bake_type_combo.currentText()
            bake_quality = int(self.bake_quality_combo.currentText())

            custom_topo_processing.start_baking_process(bake_type, bake_quality)

    def _has_wrap_object(self):
        if not (self.model_wrap_widget and hasattr(self.model_wrap_widget, "drop_canvas")):
            return False

        drop_canvas = self.model_wrap_widget.drop_canvas
        return hasattr(drop_canvas, "current_icon_path") and drop_canvas.current_icon_path

    def set_texture_preview(self, texture_path):
        if self._is_valid_texture_path(texture_path):
            self._load_texture_preview(texture_path)
        else:
            self.clear_texture_preview()

    def _is_valid_texture_path(self, texture_path):
        return texture_path and QtCore.QFile.exists(texture_path)

    def _load_texture_preview(self, texture_path):
        pixmap = QtGui.QPixmap(texture_path)
        if not pixmap.isNull():
            self.texture_preview_label.set_image(pixmap)
            self.texture_preview_label.setText("")
            self._apply_loaded_texture_style()
        else:
            self.clear_texture_preview()

    def _apply_loaded_texture_style(self):
        self.texture_preview_label.setStyleSheet(
            f"""
            QLabel {{
                border: 2px solid {const.DAYU_BACKGROUND_SELECTED_TEXT_COLOR};
                border-radius: 4px;
                background-color: {const.UI_COLOR_BG_DARKER};
            }}
        """
        )

    def clear_texture_preview(self):
        if hasattr(self.texture_preview_label, "original_pixmap"):
            self.texture_preview_label.original_pixmap = None
        self.texture_preview_label.clear()
        self.texture_preview_label.setText("贴图预览区域")
        self._apply_default_texture_style()

    def _apply_default_texture_style(self):
        self.texture_preview_label.setStyleSheet(
            f"""
            QLabel {{
                border: 2px dashed {const.SLIDER_BORDER};
                border-radius: 4px;
                background-color: {const.UI_COLOR_BG_DARKER};
                color: {const.DAYU_SECONDARY_TEXT_COLOR};
                font-size: 14px;
            }}
        """
        )

    def _on_wrap_icon_deleted(self):
        if cmds.objExists(face_const.CUSTOM_TOPO_GRP_NAME):
            cmds.delete(face_const.CUSTOM_TOPO_GRP_NAME)
        custom_topo_processing.base_mesh_visibility(True)

        self.clear_texture_preview()
        mel.eval("MLdeleteUnused;")

    def load_texture_paths_from_maya(self):
        info_node = face_const.CUSTOM_TOPO_INFO_NODE_NAME
        if not cmds.objExists(info_node) or not cmds.attributeQuery("texturePaths", node=info_node, exists=True):
            return

        paths_str = cmds.getAttr(f"{info_node}.texturePaths")
        if not paths_str:
            return

        texture_paths = json.loads(paths_str)
        self.texture_paths = [path for path in texture_paths if path and os.path.exists(path)]

        if self.texture_paths:
            self.current_texture_index = 0
            self.display_current_texture()
            self.update_navigation_buttons_visibility()

    def display_current_texture(self):
        if not self.texture_paths or self.current_texture_index >= len(self.texture_paths):
            return

        current_path = self.texture_paths[self.current_texture_index]
        self.set_texture_preview(current_path)

    def update_navigation_buttons_visibility(self):
        if hasattr(self, "prev_texture_button") and hasattr(self, "next_texture_button"):
            has_multiple_textures = len(self.texture_paths) > 1
            self.prev_texture_button.setVisible(has_multiple_textures)
            self.next_texture_button.setVisible(has_multiple_textures)

    def refresh_texture_paths(self):
        self.texture_paths = []
        self.current_texture_index = 0
        self.clear_texture_preview()
        self.load_texture_paths_from_maya()

    def on_texture_update_callback(self):
        self.refresh_texture_paths()

    def __del__(self):
        try:
            custom_topo_processing.unregister_texture_update_callback(self.on_texture_update_callback)
        except:
            pass
