"""Component Context Menu Module.

This module provides a centralized context menu management system for Hair Studio
component list items. It handles all right-click menu operations including rename,
duplicate, delete, and visibility toggle actions.
"""

# Import built-in modules
import logging

# Import third-party modules
from qtpy import QtGui
from qtpy import QtWidgets
from qtpy.QtCore import QObject
from qtpy.QtCore import Signal

# Import local modules
from cgame_avatar_factory.hair_studio.constants import ICON_ADD_LINE
from cgame_avatar_factory.hair_studio.constants import ICON_TRASH_LINE
from cgame_avatar_factory.hair_studio.utils.icon_utils import get_icon_with_fallback


class ComponentContextMenu(QObject):
    """Centralized context menu manager for component list items.

    This class handles all context menu operations for component items,
    providing a clean separation of concerns and improved maintainability.
    """

    # Signals for component operations
    component_rename_requested = Signal(str)  # component_id
    component_duplicate_requested = Signal(str)  # component_id
    component_delete_requested = Signal(str)  # component_id
    component_visibility_toggle_requested = Signal(str, bool)  # component_id, visible
    component_add_requested = Signal()
    selection_clear_requested = Signal()

    def __init__(self, parent=None):
        """Initialize the context menu manager.

        Args:
            parent (QWidget, optional): Parent widget
        """
        super(ComponentContextMenu, self).__init__(parent)
        self._parent = parent
        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

    def show_context_menu(
        self,
        position,
        component_data=None,
        has_items=False,
        global_position=None,
    ):
        """Show context menu at the specified position.

        Args:
            position (QtCore.QPoint): Position to show the menu (local coordinates)
            component_data (dict, optional): Component data if right-clicked on an item
            has_items (bool): Whether the list has any items
            global_position (QtCore.QPoint, optional): Global position for menu display

        Returns:
            bool: True if menu was shown, False otherwise
        """
        try:
            # Create context menu
            context_menu = QtWidgets.QMenu(self._parent)

            if component_data:
                # Item-specific actions
                self._add_item_actions(context_menu, component_data)
                # General actions (always available)
                context_menu.addSeparator()

            self._add_general_actions(context_menu, has_items)

            # Show the menu if it has actions
            if not context_menu.isEmpty():
                # Use provided global position or convert local position to global
                if global_position:
                    # Use the provided global position directly (ensures left-top alignment)
                    context_menu.exec_(global_position)
                elif self._parent:
                    # Convert local position to global coordinates
                    global_pos = self._parent.mapToGlobal(position)
                    context_menu.exec_(global_pos)
                else:
                    # Fallback: show at current cursor position
                    context_menu.exec_(QtGui.QCursor.pos())
                return True

        except (RuntimeError, AttributeError) as e:
            self._logger.error(f"Qt operation failed when showing context menu: {e}")
        except Exception as e:
            self._logger.error(f"Unexpected error when showing context menu: {e}")

        return False

    def _add_item_actions(self, menu, component_data):
        """Add item-specific actions to the context menu.

        Args:
            menu (QtWidgets.QMenu): The menu to add actions to
            component_data (dict): The component data
        """
        component_name = component_data.get("name", "Component")
        component_id = component_data.get("id")

        if not component_id:
            self._logger.warning("Component data missing ID, skipping item actions")
            return

        # Rename action
        rename_action = menu.addAction(f"重命名 '{component_name}'")
        rename_action.triggered.connect(
            lambda: self.component_rename_requested.emit(component_id),
        )

        # Duplicate action
        duplicate_action = menu.addAction(f"复制 '{component_name}'")
        duplicate_action.triggered.connect(
            lambda: self.component_duplicate_requested.emit(component_id),
        )

        menu.addSeparator()

        # Visibility toggle
        is_visible = component_data.get("visible", True)
        visibility_text = "隐藏" if is_visible else "显示"
        visibility_action = menu.addAction(f"{visibility_text} '{component_name}'")
        visibility_action.triggered.connect(
            lambda: self.component_visibility_toggle_requested.emit(
                component_id,
                not is_visible,
            ),
        )

        menu.addSeparator()

        # Delete action
        delete_action = menu.addAction(f"删除 '{component_name}'")
        delete_action.triggered.connect(
            lambda: self.component_delete_requested.emit(component_id),
        )
        delete_action.setIcon(get_icon_with_fallback(ICON_TRASH_LINE))

    def _add_general_actions(self, menu, has_items):
        """Add general actions to the context menu.

        Args:
            menu (QtWidgets.QMenu): The menu to add actions to
            has_items (bool): Whether the list has any items
        """
        # Add new component action
        add_action = menu.addAction("添加新组件")
        add_action.triggered.connect(lambda: self.component_add_requested.emit())
        add_action.setIcon(get_icon_with_fallback(ICON_ADD_LINE))

        # Selection actions
        if has_items:
            menu.addSeparator()

            clear_selection_action = menu.addAction("清除选择")
            clear_selection_action.triggered.connect(
                lambda: self.selection_clear_requested.emit(),
            )


class ComponentItemContextHandler:
    """Helper class to handle context menu events for ComponentItem widgets.

    This class provides methods to properly handle right-click events on
    ComponentItem widgets and delegate them to the context menu manager.
    """

    def __init__(self, context_menu_manager):
        """Initialize the context handler.

        Args:
            context_menu_manager (ComponentContextMenu): The menu manager
        """
        self.context_menu_manager = context_menu_manager
        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

    def handle_component_context_menu(self, component_widget, position):
        """Handle context menu request for a component item.

        Args:
            component_widget: The ComponentItem widget
            position (QtCore.QPoint): Local position within the widget

        Returns:
            bool: True if menu was handled, False otherwise
        """
        try:
            if not component_widget:
                return False

            # Get component data
            component_data = component_widget.get_component_data()
            if not component_data:
                self._logger.warning("No component data available for context menu")
                return False

            # Convert position to global coordinates for precise menu positioning
            global_pos = component_widget.mapToGlobal(position)

            # Convert position to parent coordinates for compatibility
            parent_widget = component_widget.parent()
            if parent_widget:
                parent_pos = parent_widget.mapFromGlobal(global_pos)

                # Show context menu through the manager with global position
                return self.context_menu_manager.show_context_menu(
                    parent_pos,
                    component_data,
                    True,
                    global_position=global_pos,
                )

        except Exception as e:
            self._logger.error(f"Failed to handle component context menu: {e}")

        return False

    def install_context_handler(self, component_widget):
        """Install context menu handler on a ComponentItem widget.

        Args:
            component_widget: The ComponentItem widget to install handler on
        """
        try:
            # Store reference to handler in the widget
            component_widget._context_handler = self

            # Override the contextMenuEvent method
            original_context_menu_event = getattr(
                component_widget,
                "contextMenuEvent",
                None,
            )

            def context_menu_event(event):
                """Handle context menu events for the component item."""
                # Handle the context menu through our handler
                handled = self.handle_component_context_menu(
                    component_widget,
                    event.pos(),
                )

                if not handled and original_context_menu_event:
                    # Fall back to original handler if we couldn't handle it
                    original_context_menu_event(event)
                elif not handled:
                    # Accept the event to prevent further propagation
                    event.accept()

            # Install the new context menu handler
            component_widget.contextMenuEvent = context_menu_event

            self._logger.debug(
                f"Installed context menu handler on ComponentItem: {component_widget}",
            )

        except Exception as e:
            self._logger.error(f"Failed to install context handler: {e}")
