# Import built-in modules
from enum import Enum
import logging
import os
import shutil

# Import third-party modules
import maya.cmds as cmds

# Import local modules
import cgame_avatar_factory.common.constants as const
from cgame_avatar_factory.face_sculpting_center.utils import dna_utils as dna
import cgame_avatar_factory.face_sculpting_center.utils.tag_util as tag_util

# Set up module-level logger
logger = logging.getLogger(__name__)


def duplicate_and_export_head_mesh():
    """
    Duplicate and export the entire head_grp as an FBX file.

    This function performs the following steps:
    1. Verify that the head_grp exists in the scene.
    2. Duplicate the entire head_grp.
    3. Delete construction history on the duplicated group to clean it.
    4. Export the duplicated group as an FBX file.
    5. Delete the duplicated group to clean up temporary objects.

    Raises:
        RuntimeError: If head_grp or geometry_grp is not found in the scene,
                      or if duplication fails.
    """

    # Define the head group name
    head_grp = const.HEAD_GRP

    # Check if head_grp exists
    if not cmds.objExists(head_grp):
        raise RuntimeError(f"Group '{head_grp}' not found in the scene")

    cmds.select(head_grp, replace=True)
    duplicated_head_grp = cmds.duplicate(returnRootsOnly=True)
    duplicated_head_grp = cmds.rename(duplicated_head_grp, "head_grp_output")

    if not duplicated_head_grp:
        raise RuntimeError("Failed to duplicate head_grp")

    # Delete construction history on duplicated group
    cmds.delete(duplicated_head_grp, constructionHistory=True)
    # Select duplicated group for export
    cmds.select(duplicated_head_grp, replace=True)
    file_path = cmds.fileDialog2(
        fileFilter="FBX Files (*.fbx)",
        dialogStyle=2,
        fileMode=0,
        caption="Export Head FBX",
    )

    if file_path and file_path[0]:
        file_path = file_path[0]
        if not file_path.lower().endswith(".fbx"):
            file_path += ".fbx"

        cmds.file(file_path, force=True, exportSelected=True, type="FBX export")
        logger.info(f"Successfully exported head FBX to: {file_path}")
    else:
        logger.info("Export cancelled by user")
    # Delete duplicated group
    cmds.delete(duplicated_head_grp)


def get_base_dna_info():
    """Loads DNA calibration data and extracts mesh names and their vertex counts.

    This function reads the DNA calibration file specified by the configuration paths,
    retrieves all mesh names, and for each mesh, obtains the vertex count. It returns
    a list of mesh names and a dictionary mapping each mesh name to its vertex count.

    Returns:
        tuple: A tuple containing:
            - mesh_name_list (list of str): List of mesh names.
            - mesh_topology_spec (dict): Dictionary where keys are mesh names and values
              are dictionaries with a single key "vertex_count" indicating the number of vertices."""
    mesh_name_list = []
    mesh_topology_spec = {}
    reader = dna.TemplateDna.get_reader()
    for mesh_index in range(reader.getMeshCount()):
        mesh_name = reader.getMeshName(mesh_index)
        if cmds.objExists(mesh_name):
            mesh_name_list.append(mesh_name)
            vertex_count = reader.getVertexPositionCount(mesh_index)
            mesh_topology_spec[mesh_name] = {
                "vertex_count": vertex_count,
            }
    return mesh_name_list, mesh_topology_spec


def export_character_package_from_dna(character_data, progress_callback=None):
    """Exports a complete character package folder based on DNA data.

    This function creates a character folder, copies the head mesh's texture,
    and generates a 128x128 thumbnail of the head mesh.

    Args:
        character_data (dict): Dictionary containing DNA and character info. Must include 'character_name'.
        progress_callback : progress callback.

    Returns:
        str: Path to the exported character package folder.
    """
    mesh_name_list, mesh_topology_spec = get_base_dna_info()

    # Step 0: Create character folder
    progress_callback({"value": 10, "text": "创建角色文件夹..."})
    export_root = character_data["export_root"]
    character_name = character_data["character_name"]
    export_dir = os.path.join(export_root, character_name)
    os.makedirs(export_dir, exist_ok=True)
    textures_dir = os.path.join(export_dir, "Textures")
    os.makedirs(textures_dir, exist_ok=True)
    mesh_name = const.BASE_HEAD_MESH_NAME
    progress_callback({"value": 20, "text": "获取贴图..."})
    texture_type = character_data["texture_file_path"]
    if texture_type == ExportType.FROM_MODEL:
        texture_name = const.HEAD_DIFFUSE_TEX_NAME
        texture_path = cmds.getAttr(f"{texture_name}.fileTextureName")
    else:
        texture_path = texture_type.replace("\\", "/")
    ext = os.path.splitext(texture_path)[1]
    texture_dst = os.path.join(textures_dir, f"{character_name}_head_color_map{ext}")
    shutil.copy(texture_path, texture_dst)
    progress_callback({"value": 30, "text": "获取缩略图..."})
    thumbnail_type = character_data["thumbnail_file_path"]
    thumbnail_path_dst = os.path.join(export_dir, f"{character_name}.png")
    if thumbnail_type == ExportType.FROM_MODEL:
        export_mesh_thumbnail(mesh_name, thumbnail_path_dst, width=128, height=128)
    else:
        thumbnail_path = thumbnail_type.replace("\\", "/")
        shutil.copy(thumbnail_path, thumbnail_path_dst)
    progress_callback({"value": 50, "text": "在目标路径写入标签信息 ..."})
    character_tags = character_data["tags"]
    tag_util.write_role_tags(export_dir, character_name, list(character_tags))
    progress_callback({"value": 60, "text": "在目标路径写入DNA ..."})
    dna.write_mesh(os.path.join(export_dir, f"{character_name}.dna"), mesh_name_list)
    progress_callback({"value": 0, "text": ""})
    return export_dir


def get_viewport_panel():
    """Get available viewport panel"""
    panel = cmds.getPanel(withFocus=True)
    if cmds.getPanel(typeOf=panel) != "modelPanel":
        # First try to get main viewport
        main_viewports = ["modelPanel4", "modelPanel1", "modelPanel2", "modelPanel3"]
        for main_panel in main_viewports:
            if cmds.modelPanel(main_panel, exists=True):
                panel = main_panel
                break
        else:
            # If main viewports don't exist, find any model panel
            panels = cmds.getPanel(type="modelPanel")
            if not panels:
                raise RuntimeError("No available modelPanel for rendering view")
            panel = panels[0]

    return panel


def export_mesh_thumbnail(
    mesh_name: str,
    thumbnail_path: str,
    width: int = 128,
    height: int = 128,
    focal_length=100,
    distance_scale=2.0,
):
    """Renders a thumbnail image of the given mesh.

    Args:
        mesh_name (str): Name of the mesh to render.
        thumbnail_path (str): Output path for the thumbnail image.
        width (int): Width of the thumbnail.
        height (int): Height of the thumbnail.

    Raises:
        RuntimeError: If rendering fails.
    """
    layers = cmds.listConnections(mesh_name, type="displayLayer") or []
    if layers:
        target_layer = layers[0]
    else:
        target_layer = None
    # Get all Display Layers (excluding default layer)
    all_layers = cmds.ls(type="displayLayer")
    all_layers = [l for l in all_layers if l != "defaultLayer"]

    # Backup visibility state of all layers
    visibility_backup = {}
    for layer in all_layers:
        try:
            visibility_backup[layer] = cmds.getAttr(f"{layer}.visibility")
        except Exception as e:
            logging.warning(f"Failed to get layer {layer} visibility: {e}")
    # Set only target layer visible, hide others
    for layer in all_layers:
        visible = layer == target_layer
        try:
            cmds.setAttr(f"{layer}.visibility", visible)
        except Exception as e:
            logging.warning(f"Failed to set layer {layer} visibility: {e}")

    if not cmds.objExists(mesh_name):
        logging.warning(f"Mesh '{mesh_name}' does not exist in the scene.")

        # Calculate model bounding box center and maximum size
    bbox = cmds.exactWorldBoundingBox(mesh_name)
    center = [
        (bbox[0] + bbox[3]) / 2.0,
        (bbox[1] + bbox[4]) / 2.0,
        (bbox[2] + bbox[5]) / 2.0,
    ]
    size_x = bbox[3] - bbox[0]
    size_y = bbox[4] - bbox[1]
    size_z = bbox[5] - bbox[2]
    max_size = max(size_x, size_y, size_z)

    # Create temporary camera
    cam_name, cam_shape = cmds.camera(name="temp_thumbnail_cam")
    cmds.setAttr(f"{cam_shape}.focalLength", focal_length)
    # Camera position: model center +Y direction, distance is model max size * distance_scale
    cam_distance = max_size * distance_scale
    is_y_up = cmds.upAxis(q=True, axis=True).lower() == "y"

    # Set camera position based on coordinate system
    if is_y_up:
        cam_pos = [center[0], center[1], center[2] + cam_distance]
        rotate_x = 0
    else:
        cam_pos = [center[0], center[1] - cam_distance, center[2]]
        rotate_x = 90

    # Apply camera transform
    cmds.setAttr(f"{cam_name}.translateX", cam_pos[0])
    cmds.setAttr(f"{cam_name}.translateY", cam_pos[1])
    cmds.setAttr(f"{cam_name}.translateZ", cam_pos[2])

    cmds.setAttr(f"{cam_name}.rotateX", rotate_x)
    cmds.setAttr(f"{cam_name}.rotateY", 0)
    cmds.setAttr(f"{cam_name}.rotateZ", 0)

    # Set current view to use this camera
    try:
        panel = get_viewport_panel()
        cmds.modelEditor(panel, e=True, camera=cam_name)
    except RuntimeError as e:
        logging.error(f"Unable to set viewport: {e}")

    result = cmds.playblast(
        completeFilename=thumbnail_path,
        format="image",
        width=width,
        height=height,
        showOrnaments=False,
        frame=[cmds.currentTime(q=True)],
        viewer=False,
        percent=100,
        offScreen=True,
    )

    # Clear selection
    cmds.select(clear=True)
    # Delete temporary camera
    cmds.modelEditor(panel, e=True, camera="persp")
    if cmds.objExists(cam_name):
        cmds.delete(cam_name)
    # Restore all layers visibility
    for layer, vis in visibility_backup.items():
        try:
            cmds.setAttr(f"{layer}.visibility", vis)
        except Exception as e:
            logging.info(f"Failed to restore layer {layer} visibility: {e}")
    # Check if file is generated
    if not os.path.isfile(thumbnail_path):
        logging.info(f"Failed to create thumbnail at {thumbnail_path}")

    return thumbnail_path


def get_topology_info(obj_name):
    """Get topology information of specified object: vertex count and face count

    Args:
        obj_name (str): Object name

    Returns:
        dict: Dictionary containing 'vertex_count' and 'face_count', returns None if object doesn't exist
    """
    if not cmds.objExists(obj_name):
        logging.info(f"Object {obj_name} does not exist.")
        return None

    verts = cmds.polyEvaluate(obj_name, vertex=True)
    edges = cmds.polyEvaluate(obj_name, edge=True)
    faces = cmds.polyEvaluate(obj_name, face=True)

    return {"vertex_count": verts, "edge_count": edges, "face_count": faces}


def check_topology_compliance(obj_list, spec_dict):
    """Check if objects in scene meet topology specifications

    Args:
        obj_list (list of str): List of object names
        spec_dict (dict): Object names and corresponding topology specifications
    Returns:
        dict: Check results, keys are object names, values are boolean, True means compliant, False means non-compliant or object doesn't exist
    """
    # spec_dict = const.TOPOLOGY_SPEC
    results = {}
    for obj in obj_list:
        if obj not in spec_dict:
            logging.warning(f"Warning: Object {obj} not in specification dictionary, skipping check.")
            results[obj] = False
            continue

        topo_info = get_topology_info(obj)
        if topo_info is None:
            results[obj] = False
            continue

        spec = spec_dict[obj]
        vertex_match = topo_info["vertex_count"] == spec["vertex_count"]

        if vertex_match:
            results[obj] = True
        else:
            logging.warning(f"Object {obj} topology does not meet specifications:")
            logging.warning(
                f"  Expected vertex count: {spec['vertex_count']}, Actual vertex count: {topo_info['vertex_count']}",
            )
            results[obj] = False

    return results


class ExportType(Enum):
    FROM_MODEL = "from_model"
