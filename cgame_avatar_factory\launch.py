# Import built-in modules
import sys
import time

# Import third-party modules
from lightbox_log import init_logger
from lightbox_ui.apps.maya_funcs import get_main_window

# Import local modules
from cgame_avatar_factory.common import core
import cgame_avatar_factory.common.constants as const


def module_cleanup(module_name):
    """Cleanup module_name in sys.modules cache.

    Args:
        module_name (str, ModuleType): Module Name

    Raises:
        TypeError: invalid module_name
    """
    if module_name in sys.builtin_module_names:
        return

    pred = "%s." % module_name
    packages = [mod for mod in sys.modules if mod.startswith(pred)]
    packages += [module_name]
    for package in packages:
        module = sys.modules.get(package)
        if module is not None:
            del sys.modules[package]  # noqa:WPS420


def debug_launch_window():
    start_time = time.time()
    module_cleanup(const.PACKAGE_NAME)
    launch_window()
    print(f"[TIMING] Launch took {(time.time() - start_time) * 1000:.2f} ms")


def launch_window():
    # Import third-party modules
    from dayu_widgets import dayu_theme
    from dayu_widgets.qt import application
    from lightbox_ui.banner import setup_banner

    # Import local modules
    from cgame_avatar_factory.common.reporter.event_monitor import is_monitoring_active
    from cgame_avatar_factory.common.reporter.event_monitor import start_event_monitoring
    from cgame_avatar_factory.common.ui import style
    from cgame_avatar_factory.ui.main_window import MainWidget

    init_logger(const.PACKAGE_NAME)

    style.set_global_style()
    dayu_theme.set_theme("dark")
    dayu_theme.set_primary_color(dayu_theme.blue)

    with application():
        main_window = MainWidget(parent=get_main_window())
        dayu_theme.apply(main_window)

        setup_banner(parent=main_window, embed_type="banner", button=const.BANNER_BUTTON)
        main_window.show()

        # Start export monitoring (safe to call multiple times)
        if not is_monitoring_active():
            start_event_monitoring()

        with core.get_reporter_instance() as api:
            api.report_count(
                event_name="start",
                action="tool started from shelf",
                tool_name=const.PACKAGE_NAME,
            )
    return main_window
