#!/usr/bin/env python
"""Test Runner for Updated Asset Loading Tests.

Runs the updated tests that reflect the latest asset loading optimizations
and configuration system changes.
"""

# Import built-in modules
import os
import sys
import time
import unittest

# Add the project root to Python path for testing
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def run_updated_tests():
    """Run all updated tests for the latest asset loading system."""
    print("=" * 80)
    print("HAIR STUDIO UPDATED ASSET LOADING TESTS")
    print("=" * 80)
    print()

    start_time = time.time()
    all_results = []

    # Test modules to run with their descriptions
    test_modules = [
        ("Constants Configuration", "test_constants_config"),
        ("Asset Library Config (Mock)", "asset_library.test_asset_lib_config_core"),
        ("Sub-Type Logic (Mock)", "asset_library.test_sub_type_logic"),
        ("Sub-Tab Switching", "asset_library.test_sub_tab_switching"),
        ("Data Manager Core (Updated)", "test_data_manager_core"),
    ]

    for test_name, module_name in test_modules:
        print(f"Running {test_name} Tests...")
        print("-" * 60)

        try:
            # Import and run the test module
            if "." in module_name:
                # Handle nested modules
                parts = module_name.split(".")
                module_path = f"cgame_avatar_factory.hair_studio.tests.{module_name}"
                module = __import__(module_path, fromlist=[parts[-1]])
            else:
                module = __import__(
                    f"cgame_avatar_factory.hair_studio.tests.{module_name}",
                    fromlist=[module_name],
                )

            if hasattr(module, "run_tests"):
                success = module.run_tests()
                all_results.append((test_name, success))
            else:
                # Fallback to standard unittest discovery
                loader = unittest.TestLoader()
                suite = loader.loadTestsFromModule(module)
                runner = unittest.TextTestRunner(verbosity=1)
                result = runner.run(suite)
                success = result.wasSuccessful()
                all_results.append((test_name, success))

        except Exception as e:
            print(f"❌ Error running {test_name} tests: {e}")
            all_results.append((test_name, False))

        print()

    # Print summary
    end_time = time.time()
    duration = end_time - start_time

    print("=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)

    passed = 0
    failed = 0

    for test_name, success in all_results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:<35} {status}")
        if success:
            passed += 1
        else:
            failed += 1

    print()
    print(f"Total Test Suites: {len(all_results)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Duration: {duration:.2f} seconds")

    if failed == 0:
        print()
        print("🎉 ALL UPDATED TESTS PASSED!")
        print("✅ The updated asset loading system is working correctly.")
        print("🚀 Optimizations are functioning as expected.")
        return True
    else:
        print()
        print("⚠️  SOME TESTS FAILED!")
        print("❌ Please check the failed tests and fix any issues.")
        return False


def run_specific_updated_test(test_name):
    """Run a specific updated test module."""
    test_modules = {
        "constants": "test_constants_config",
        "config": "asset_library.test_asset_lib_config_core",
        "subtype": "asset_library.test_sub_type_logic",
        "subtab": "asset_library.test_sub_tab_switching",
        "data": "test_data_manager_core",
    }

    if test_name not in test_modules:
        print(f"Unknown test: {test_name}")
        print(f"Available tests: {', '.join(test_modules.keys())}")
        return False

    module_name = test_modules[test_name]

    try:
        print(f"Running {test_name} tests...")

        if "." in module_name:
            # Handle nested modules
            parts = module_name.split(".")
            module_path = f"cgame_avatar_factory.hair_studio.tests.{module_name}"
            module = __import__(module_path, fromlist=[parts[-1]])
        else:
            module = __import__(
                f"cgame_avatar_factory.hair_studio.tests.{module_name}",
                fromlist=[module_name],
            )

        if hasattr(module, "run_tests"):
            return module.run_tests()
        else:
            loader = unittest.TestLoader()
            suite = loader.loadTestsFromModule(module)
            runner = unittest.TextTestRunner(verbosity=2)
            result = runner.run(suite)
            return result.wasSuccessful()

    except Exception as e:
        print(f"Error running {test_name} tests: {e}")
        # Import built-in modules
        import traceback

        traceback.print_exc()
        return False


def show_test_info():
    """Show information about the updated tests."""
    print("=" * 80)
    print("UPDATED ASSET LOADING TESTS INFORMATION")
    print("=" * 80)
    print()

    print("🔄 WHAT'S UPDATED:")
    print("1. Mock Data Usage: Tests now use mock data instead of real files")
    print("2. Optimized Loading: Tests cover the new optimized asset loading workflow")
    print("3. Configuration System: Tests validate the new AssetLibConfig system")
    print("4. Performance Testing: Tests verify performance improvements")
    print()

    print("📋 TEST MODULES:")
    print("• constants      - Constants configuration validation")
    print("• config         - AssetLibConfig core functionality (with mocks)")
    print("• subtype        - Sub-type logic and path parsing (with mocks)")
    print("• subtab         - Sub-tab switching logic")
    print("• data           - Data manager with optimized loading workflow")
    print()

    print("🚀 KEY IMPROVEMENTS TESTED:")
    print("• Pre-determined sub-types (no redundant path parsing)")
    print("• Dynamic path discovery from configuration")
    print("• Mock-based testing for reliability")
    print("• Integration between all components")
    print()

    print("💡 USAGE:")
    print("python run_updated_tests.py           # Run all tests")
    print("python run_updated_tests.py config    # Run specific test")
    print("python run_updated_tests.py --info    # Show this information")


def main():
    """Main entry point."""
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ["--info", "-i", "info"]:
            show_test_info()
            return
        else:
            # Run specific test
            success = run_specific_updated_test(arg)
    else:
        # Run all tests
        success = run_updated_tests()

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
