# Import built-in modules
from math import sqrt
from typing import List

# Import third-party modules
import maya.cmds as cmds

# Import local modules
import cgame_avatar_factory.common.constants as const


class AdaptiveBonePosition:
    """Adapts bone positions based on mesh differences

    Adjusts joint positions by calculating differences between source and target meshes.
    """

    def __init__(self, source_dna_data, target_dna_data, world_positions):
        """Initialize bone position adapter

        Args:
            source_dna_data: Template DNA mesh data
            target_dna_data: Base DNA mesh data
            world_positions: Dictionary of joint world positions
        """
        source_mesh_data = source_dna_data[const.BASE_HEAD_MESH_NAME]
        target_mesh_data = target_dna_data[const.BASE_HEAD_MESH_NAME]

        cmds.select(clear=True)
        mesh_difference = self.calculate_mesh_difference(source_mesh_data, target_mesh_data)
        joint_index = 0
        for joint_name, joint_pos in world_positions.items():
            if joint_index != 0:
                nearest_idx = self.find_nearest_vertex(joint_pos, source_mesh_data)
                new_joint_pos = (
                    joint_pos[0] - mesh_difference[nearest_idx][0],
                    joint_pos[1] - mesh_difference[nearest_idx][1],
                    joint_pos[2] - mesh_difference[nearest_idx][2],
                )
                cmds.xform(joint_name, worldSpace=True, translation=new_joint_pos)
            else:
                joint_index += 1

    # Apply displacement to joints based on nearest vertex
    def find_nearest_vertex(self, point: List[float], vertices: List[List[float]]) -> int:
        """Find index of nearest vertex to given point

        Args:
            point: 3D point coordinates
            vertices: List of vertex positions

        Returns:
            int: Index of nearest vertex
        """
        dist_list = []
        for vertex in vertices:
            distance = sqrt(
                (point[0] - vertex[0]) ** 2 + (point[1] - vertex[1]) ** 2 + (point[2] - vertex[2]) ** 2,
            )
            dist_list.append(distance)
        min_dist_idx = dist_list.index(min(dist_list))
        return min_dist_idx

    def calculate_mesh_difference(
        self,
        source_mesh: List[List[float]],
        target_mesh: List[List[float]],
    ) -> List[List[float]]:
        """Calculate vertex position differences between meshes

        Args:
            source_mesh: Source mesh vertex positions
            target_mesh: Target mesh vertex positions

        Returns:
            List[List[float]]: Difference vectors for each vertex
        """
        return [[s[0] - t[0], s[1] - t[1], s[2] - t[2]] for s, t in zip(source_mesh, target_mesh)]
