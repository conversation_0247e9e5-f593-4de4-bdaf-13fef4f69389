"""Asset Library Configuration Module.

This module provides configuration management for the Hair Studio Asset Library,
including tab configuration, default values, and dynamic path discovery.
"""
# Import built-in modules
import logging
import os
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

# Import local modules
# Import constants from hair_studio.constants
from cgame_avatar_factory.hair_studio.constants import ALL_HAIR_ASSET_DIR
from cgame_avatar_factory.hair_studio.constants import DEFAULT_SUB_TAB_KEY
from cgame_avatar_factory.hair_studio.constants import HAIR_ASSET_LIB_TAB_MAP
from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CARD
from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CURVE
from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_XGEN

# Global configuration instance
_config_instance = None


class AssetLibConfig:
    """Configuration manager for Asset Library.

    This class centralizes all configuration related to asset library tabs,
    including tab definitions, default values, and validation logic.
    """

    def __init__(self):
        """Initialize the configuration manager."""
        self._logger = logging.getLogger(__name__)

        # Load base configuration
        self._tab_map = HAIR_ASSET_LIB_TAB_MAP.copy()  # Make a copy to avoid modifying original
        self._card_hair_type = HAIR_TYPE_CARD
        self._all_asset_dirs = ALL_HAIR_ASSET_DIR

        # Configuration validation
        self._validate_config()

        # Dynamic path discovery and merging
        self._discover_and_merge_asset_paths()

    def _validate_config(self):
        """Validate the configuration data."""
        if not self._tab_map:
            self._logger.warning("HAIR_ASSET_LIB_TAB_MAP is empty")
            return

        # Validate each tab configuration
        for tab_key, tab_config in self._tab_map.items():
            if not isinstance(tab_config, dict):
                self._logger.error(f"Invalid tab config for '{tab_key}': not a dictionary")
                continue

            # Check required fields
            required_fields = ["show_text"]
            for field in required_fields:
                if field not in tab_config:
                    self._logger.error(f"Missing required field '{field}' in tab '{tab_key}'")

    def _discover_and_merge_asset_paths(self):
        """Discover asset paths from root directories and merge with configured paths."""
        self._logger.debug("Starting dynamic asset path discovery...")

        # Discover paths for each tab
        for tab_key in self._tab_map.keys():
            discovered_paths = self._discover_paths_for_tab(tab_key)
            configured_paths = self._get_configured_paths_for_tab(tab_key)

            # Merge paths (configured + discovered, remove duplicates)
            merged_paths = self._merge_paths(configured_paths, discovered_paths)

            # Update tab configuration
            self._tab_map[tab_key]["lib_path"] = merged_paths

            self._logger.debug(
                f"Tab '{tab_key}': configured={len(configured_paths)}, "
                f"discovered={len(discovered_paths)}, merged={len(merged_paths)}",
            )

    def _discover_paths_for_tab(self, tab_key: str) -> List[str]:
        """Discover asset paths for a specific tab by scanning root directories.

        Args:
            tab_key (str): Tab key to discover paths for

        Returns:
            List[str]: List of discovered paths
        """
        discovered_paths = []

        for root_dir in self._all_asset_dirs:
            if not root_dir or not root_dir.strip():
                continue

            if not os.path.exists(root_dir):
                self._logger.warning(f"Root asset directory does not exist: {root_dir}")
                continue

            # Look for matching subdirectories
            matching_paths = self._find_matching_subdirs(root_dir, tab_key)
            discovered_paths.extend(matching_paths)

        return discovered_paths

    def _find_matching_subdirs(self, root_dir: str, tab_key: str) -> List[str]:
        """Find subdirectories that match the tab key with flexible matching rules.

        Args:
            root_dir (str): Root directory to search in
            tab_key (str): Tab key to match against

        Returns:
            List[str]: List of matching subdirectory paths
        """
        matching_paths = []

        try:
            # Get all subdirectories
            subdirs = [d for d in os.listdir(root_dir) if os.path.isdir(os.path.join(root_dir, d))]

            # Flexible matching rules
            for subdir in subdirs:
                if self._is_path_match(subdir, tab_key):
                    full_path = os.path.join(root_dir, subdir)
                    matching_paths.append(full_path)
                    self._logger.debug(f"Found matching path: {full_path}")

            if not matching_paths:
                self._logger.warning(
                    f"No matching subdirectories found for tab '{tab_key}' in {root_dir}",
                )

        except OSError as e:
            self._logger.warning(f"Error scanning directory {root_dir}: {e}")

        return matching_paths

    def _is_path_match(self, dir_name: str, tab_key: str) -> bool:
        """Check if directory name matches tab key with flexible rules.

        Args:
            dir_name (str): Directory name to check
            tab_key (str): Tab key to match against

        Returns:
            bool: True if directory matches tab key
        """
        # Normalize for comparison (case-insensitive)
        dir_lower = dir_name.lower().strip()
        key_lower = tab_key.lower().strip()

        # Exact match
        if dir_lower == key_lower:
            return True

        # Common variations and aliases
        aliases = {
            "hair": ["hair", "hairs", "head_hair", "headhair"],
            "eyebrow": ["eyebrow", "eyebrows", "brow", "brows", "eye_brow"],
            "beard": ["beard", "beards", "facial_hair", "facialhair"],
            "scalp": ["scalp", "scalps", "head_scalp", "headscalp"],
        }

        if key_lower in aliases:
            return dir_lower in aliases[key_lower]

        return False

    def _get_configured_paths_for_tab(self, tab_key: str) -> List[str]:
        """Get already configured paths for a tab.

        Args:
            tab_key (str): Tab key

        Returns:
            List[str]: List of configured paths
        """
        if tab_key not in self._tab_map:
            return []

        lib_path = self._tab_map[tab_key].get("lib_path", [])

        # Handle both string and list formats
        if isinstance(lib_path, str):
            return [lib_path] if lib_path.strip() else []
        elif isinstance(lib_path, list):
            return [p for p in lib_path if p and p.strip()]
        else:
            self._logger.warning(f"Invalid lib_path format for tab '{tab_key}': {type(lib_path)}")
            return []

    def _merge_paths(self, configured_paths: List[str], discovered_paths: List[str]) -> List[str]:
        """Merge configured and discovered paths, removing duplicates.

        Args:
            configured_paths (List[str]): Already configured paths
            discovered_paths (List[str]): Dynamically discovered paths

        Returns:
            List[str]: Merged list of unique paths
        """
        # Use set to remove duplicates, but preserve order
        seen = set()
        merged = []

        # Add configured paths first (higher priority)
        for path in configured_paths:
            normalized_path = os.path.normpath(path)
            if normalized_path not in seen:
                seen.add(normalized_path)
                merged.append(path)

        # Add discovered paths
        for path in discovered_paths:
            normalized_path = os.path.normpath(path)
            if normalized_path not in seen:
                seen.add(normalized_path)
                merged.append(path)

        return merged

    def get_tab_map(self) -> Dict[str, Dict[str, Any]]:
        """Get the complete tab configuration map.

        Returns:
            Dict[str, Dict[str, Any]]: Tab configuration mapping
        """
        return self._tab_map.copy()

    def get_tab_keys(self) -> List[str]:
        """Get all available tab keys.

        Returns:
            List[str]: List of tab keys (e.g., ['eyebrow', 'hair', 'beard'])
        """
        return list(self._tab_map.keys())

    def get_tab_count(self) -> int:
        """Get the total number of tabs.

        Returns:
            int: Number of tabs
        """
        return len(self._tab_map)

    def get_tab_display_text(self, tab_key: str) -> str:
        """Get the display text for a specific tab.

        Args:
            tab_key (str): Tab key (e.g., 'hair', 'eyebrow')

        Returns:
            str: Display text for the tab, or tab_key if not found
        """
        if tab_key in self._tab_map:
            return self._tab_map[tab_key].get("show_text", tab_key)
        return tab_key

    def get_tab_lib_paths(self, tab_key: str) -> List[str]:
        """Get the library paths for a specific tab (includes configured + discovered paths).

        Args:
            tab_key (str): Tab key (e.g., 'hair', 'eyebrow')

        Returns:
            List[str]: List of library paths for the tab
        """
        if tab_key not in self._tab_map:
            return []

        lib_path = self._tab_map[tab_key].get("lib_path", [])

        # After _discover_and_merge_asset_paths, lib_path should always be a list
        if isinstance(lib_path, list):
            return lib_path.copy()
        else:
            self._logger.warning(f"Unexpected lib_path format for tab '{tab_key}': {type(lib_path)}")
            return []

    def get_all_asset_paths(self) -> List[str]:
        """Get all asset paths from all tabs (replaces DEFAULT_ASSET_CARD_BASE_PATHS).

        This method aggregates all library paths from all configured tabs,
        providing a unified list of all asset directories.

        Returns:
            List[str]: List of all asset paths across all tabs
        """
        all_paths = []
        seen = set()

        for tab_key in self._tab_map.keys():
            tab_paths = self.get_tab_lib_paths(tab_key)
            for path in tab_paths:
                normalized_path = os.path.normpath(path)
                if normalized_path not in seen:
                    seen.add(normalized_path)
                    all_paths.append(path)

        self._logger.debug(f"Aggregated {len(all_paths)} unique asset paths from all tabs")
        return all_paths

    def get_asset_paths_summary(self) -> Dict[str, List[str]]:
        """Get a summary of asset paths organized by tab.

        Returns:
            Dict[str, List[str]]: Dictionary mapping tab keys to their asset paths
        """
        summary = {}
        for tab_key in self._tab_map.keys():
            summary[tab_key] = self.get_tab_lib_paths(tab_key)
        return summary

    def get_default_tab_key(self) -> Optional[str]:
        """Get the default tab key.

        Returns the preferred default tab key, falling back to the first available
        tab if the preferred default is not available.

        Returns:
            Optional[str]: Default tab key, or None if no tabs available
        """
        if not self._tab_map:
            return None

        # Preferred default tab
        preferred_default = DEFAULT_SUB_TAB_KEY

        if preferred_default in self._tab_map:
            return preferred_default

        # Fallback to first available tab
        return self.get_tab_keys()[0] if self.get_tab_keys() else None

    def get_default_tab_index(self) -> int:
        """Get the default tab index.

        Returns:
            int: Default tab index, or 0 if no default found
        """
        default_key = self.get_default_tab_key()
        if default_key is None:
            return 0

        try:
            return self.get_tab_keys().index(default_key)
        except ValueError:
            return 0

    def should_show_sub_tabs(self, hair_type: str) -> bool:
        """Determine if sub-tabs should be shown for the given hair type.

        Args:
            hair_type (str): Hair type (e.g., 'card', 'xgen', 'curve')

        Returns:
            bool: True if sub-tabs should be shown
        """
        return hair_type == self._card_hair_type and len(self._tab_map) > 0

    def get_tab_config(self, tab_key: str) -> Dict[str, Any]:
        """Get the complete configuration for a specific tab.

        Args:
            tab_key (str): Tab key (e.g., 'hair', 'eyebrow')

        Returns:
            Dict[str, Any]: Tab configuration dictionary
        """
        return self._tab_map.get(tab_key, {}).copy()

    def is_valid_tab_key(self, tab_key: str) -> bool:
        """Check if a tab key is valid.

        Args:
            tab_key (str): Tab key to validate

        Returns:
            bool: True if the tab key is valid
        """
        return tab_key in self._tab_map

    def get_tab_key_by_index(self, index: int) -> Optional[str]:
        """Get tab key by index.

        Args:
            index (int): Tab index

        Returns:
            Optional[str]: Tab key, or None if index is invalid
        """
        tab_keys = self.get_tab_keys()
        if 0 <= index < len(tab_keys):
            return tab_keys[index]
        return None

    def get_tab_index_by_key(self, tab_key: str) -> int:
        """Get tab index by key.

        Args:
            tab_key (str): Tab key

        Returns:
            int: Tab index, or -1 if key is invalid
        """
        try:
            return self.get_tab_keys().index(tab_key)
        except ValueError:
            return -1

    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of the current configuration.

        Returns:
            Dict[str, Any]: Configuration summary
        """
        total_paths = len(self.get_all_asset_paths())
        path_summary = {}
        for tab_key in self.get_tab_keys():
            paths = self.get_tab_lib_paths(tab_key)
            path_summary[tab_key] = len(paths)

        return {
            "total_tabs": self.get_tab_count(),
            "tab_keys": self.get_tab_keys(),
            "default_tab": self.get_default_tab_key(),
            "default_index": self.get_default_tab_index(),
            "card_hair_type": self._card_hair_type,
            "total_asset_paths": total_paths,
            "paths_per_tab": path_summary,
            "root_directories": [d for d in self._all_asset_dirs if d and d.strip()],
        }


def get_asset_lib_config() -> AssetLibConfig:
    """Get the global asset library configuration instance.

    Returns:
        AssetLibConfig: Global configuration instance
    """
    global _config_instance
    if _config_instance is None:
        _config_instance = AssetLibConfig()
    return _config_instance


# Convenience functions for common operations
def get_tab_map() -> Dict[str, Dict[str, Any]]:
    """Get the complete tab configuration map."""
    return get_asset_lib_config().get_tab_map()


def get_default_tab_key() -> Optional[str]:
    """Get the default tab key."""
    return get_asset_lib_config().get_default_tab_key()


def should_show_sub_tabs(hair_type: str) -> bool:
    """Determine if sub-tabs should be shown for the given hair type."""
    return get_asset_lib_config().should_show_sub_tabs(hair_type)


def get_tab_display_text(tab_key: str) -> str:
    """Get the display text for a specific tab."""
    return get_asset_lib_config().get_tab_display_text(tab_key)


def get_all_asset_paths() -> List[str]:
    """Get all asset paths from all tabs (replaces DEFAULT_ASSET_CARD_BASE_PATHS)."""
    return get_asset_lib_config().get_all_asset_paths()


def get_tab_lib_paths(tab_key: str) -> List[str]:
    """Get the library paths for a specific tab."""
    return get_asset_lib_config().get_tab_lib_paths(tab_key)


def get_asset_paths_by_type(asset_type: str) -> List[str]:
    """Get asset paths for a specific asset type (card, xgen, curve).

    Args:
        asset_type (str): Asset type ('card', 'xgen', 'curve')

    Returns:
        List[str]: List of asset paths for the specified type
    """
    if asset_type == HAIR_TYPE_CARD:
        return get_all_asset_paths()
    elif asset_type == HAIR_TYPE_XGEN:
        return _get_xgen_asset_paths()
    elif asset_type == HAIR_TYPE_CURVE:
        return _get_curve_asset_paths()
    else:
        return []


def _get_xgen_asset_paths() -> List[str]:
    """Get XGen asset paths using dynamic discovery (currently returns empty list)."""
    # XGen assets are not currently configured, but the system is ready for expansion
    # When XGen support is needed, this function can be enhanced to use dynamic discovery
    # similar to the card type system
    return []


def _get_curve_asset_paths() -> List[str]:
    """Get Curve asset paths using dynamic discovery (currently returns empty list)."""
    # Curve assets are not currently configured, but the system is ready for expansion
    # When Curve support is needed, this function can be enhanced to use dynamic discovery
    # similar to the card type system
    return []
