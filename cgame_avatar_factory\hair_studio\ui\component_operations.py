"""Component Operations Module.

This module provides operations for hair components including rename, delete, duplicate, and visibility toggle.
"""

# Import built-in modules
import logging

# Import third-party modules
from qtpy import QtCore
from qtpy import QtWidgets


class ComponentOperations(QtCore.QObject):
    """Handles component operations like rename, delete, duplicate, and visibility toggle."""

    # Signals
    component_deleted = QtCore.Signal(str)  # component_id
    component_renamed = QtCore.Signal(str, str)  # component_id, new_name
    component_visibility_toggled = QtCore.Signal(str, bool)  # component_id, visible

    def __init__(self, parent=None):
        """Initialize ComponentOperations.

        Args:
            parent: Parent widget for dialogs
        """
        super(ComponentOperations, self).__init__(parent)
        self._parent = parent
        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

    def rename_component(self, component_data):
        """Rename a component.

        Args:
            component_data (dict): Component data containing current name and id

        Returns:
            str: New name if renamed, None if cancelled
        """
        if not component_data:
            return None

        current_name = component_data.get("name", "Unnamed Component")

        # Show input dialog
        new_name, ok = QtWidgets.QInputDialog.getText(
            self._parent,
            "Rename Component",
            "Enter new name:",
            QtWidgets.QLineEdit.Normal,
            current_name,
        )

        if ok and new_name.strip() and new_name.strip() != current_name:
            component_id = component_data.get("id")
            if component_id:
                self.component_renamed.emit(component_id, new_name.strip())
                self._logger.info("Renamed component to '%s'", new_name.strip())
                return new_name.strip()

        return None

    def delete_component(self, component_data):
        """Delete a component with confirmation.

        Args:
            component_data (dict): Component data to delete

        Returns:
            bool: True if deleted, False if cancelled
        """
        if not component_data:
            return False

        component_name = component_data.get("name", "Unnamed Component")

        # Show confirmation dialog
        reply = QtWidgets.QMessageBox.question(
            self._parent,
            "Delete Component",
            f"Are you sure you want to delete '{component_name}'?",
            QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
            QtWidgets.QMessageBox.No,
        )

        if reply == QtWidgets.QMessageBox.Yes:
            component_id = component_data.get("id")
            if component_id:
                self.component_deleted.emit(component_id)
                self._logger.info("Deleted component '%s'", component_name)
                return True

        return False

    def duplicate_component(self, component_data):
        """Duplicate a component.

        Args:
            component_data (dict): Component data to duplicate

        Returns:
            dict: New component data if duplicated, None if failed
        """
        if not component_data:
            return None

        original_name = component_data.get("name", "Unnamed Component")

        # Create duplicated data
        duplicated_data = component_data.copy()
        duplicated_data["name"] = f"{original_name} Copy"
        # Generate new ID (this should be handled by the manager)
        duplicated_data["id"] = f"{component_data.get('id', '')}_copy"

        self._logger.info("Duplicated component '%s'", original_name)
        return duplicated_data

    def toggle_visibility(self, component_data):
        """Toggle component visibility.

        Args:
            component_data (dict): Component data

        Returns:
            bool: New visibility state, None if failed
        """
        if not component_data:
            return None

        component_id = component_data.get("id")
        if not component_id:
            return None

        # Toggle visibility
        current_visibility = component_data.get("visible", True)
        new_visibility = not current_visibility

        # Emit signal to notify other components (like maya_api)
        self.component_visibility_toggled.emit(component_id, new_visibility)

        visibility_text = "shown" if new_visibility else "hidden"
        self._logger.info(
            "Component '%s' %s",
            component_data.get("name", "Unknown"),
            visibility_text,
        )

        return new_visibility
