"""
Asset Properties Dialog for displaying asset details.

This module provides a dialog widget for displaying comprehensive asset properties
and metadata. It is designed to be used as a right-click context menu option in
the asset library.
"""

# Import built-in modules
import logging

# Import third-party modules
from qtpy import QtGui
from qtpy import QtWidgets


class AssetPropertiesDialog(QtWidgets.QDialog):
    """Dialog for displaying asset properties and metadata."""

    def __init__(self, asset_data, parent=None):
        """Initialize the asset properties dialog.

        Args:
            asset_data (dict): Asset data to display
            parent (QWidget, optional): Parent widget
        """
        super().__init__(parent)
        self.asset_data = asset_data
        self._logger = logging.getLogger(__name__)

        self.setWindowTitle(f"资产属性 - {asset_data.get('name', 'Unknown Asset')}")
        self.setModal(True)
        self.resize(500, 400)

        self._setup_ui()
        self._populate_data()

    def _setup_ui(self):
        """Set up the user interface."""
        layout = QtWidgets.QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)

        # Title
        title_label = QtWidgets.QLabel(f"资产属性")
        title_font = title_label.font()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # Asset name
        name_label = QtWidgets.QLabel(f"名称: {self.asset_data.get('name', 'Unknown')}")
        name_font = name_label.font()
        name_font.setPointSize(12)
        name_font.setBold(True)
        name_label.setFont(name_font)
        layout.addWidget(name_label)

        # Separator
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        layout.addWidget(separator)

        # Properties table
        self.properties_table = QtWidgets.QTableWidget()
        self.properties_table.setColumnCount(2)
        self.properties_table.setHorizontalHeaderLabels(["属性", "值"])

        # Set table properties
        header = self.properties_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)

        self.properties_table.setAlternatingRowColors(True)
        self.properties_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.properties_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)

        layout.addWidget(self.properties_table)

        # Button box
        button_box = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.Ok)
        button_box.accepted.connect(self.accept)
        layout.addWidget(button_box)

    def _populate_data(self):
        """Populate the table with asset data."""
        try:
            # Collect all properties
            properties = []

            # Basic properties
            basic_props = [
                ("ID", self.asset_data.get("id", "N/A")),
                ("名称", self.asset_data.get("name", "N/A")),
                ("资产类型", self.asset_data.get("asset_type", "N/A")),
                ("文件路径", self.asset_data.get("file_path", "N/A")),
                ("缩略图", self.asset_data.get("thumbnail", "N/A")),
            ]
            properties.extend(basic_props)

            # Metadata properties
            metadata = self.asset_data.get("metadata", {})
            if metadata:
                properties.append(("--- 元数据 ---", ""))
                for key, value in metadata.items():
                    # Format the key for display
                    display_key = key.replace("_", " ").title()
                    if key == "file_path":
                        display_key = "文件路径"
                    elif key == "reference":
                        display_key = "参考文件"

                    # Format the value
                    if isinstance(value, (dict, list)):
                        display_value = str(value)
                    else:
                        display_value = str(value) if value is not None else "N/A"

                    properties.append((display_key, display_value))

            # Set table row count
            self.properties_table.setRowCount(len(properties))

            # Populate table
            for row, (key, value) in enumerate(properties):
                key_item = QtWidgets.QTableWidgetItem(str(key))
                value_item = QtWidgets.QTableWidgetItem(str(value))

                # Style separator rows
                if key.startswith("---") and key.endswith("---"):
                    font = key_item.font()
                    font.setBold(True)
                    key_item.setFont(font)
                    value_item.setFont(font)
                    key_item.setBackground(QtGui.QColor(240, 240, 240))
                    value_item.setBackground(QtGui.QColor(240, 240, 240))

                self.properties_table.setItem(row, 0, key_item)
                self.properties_table.setItem(row, 1, value_item)

            self._logger.debug(f"Populated properties table with {len(properties)} items")

        except Exception as e:
            self._logger.error(f"Failed to populate asset properties: {e}")
            # Show error in table
            self.properties_table.setRowCount(1)
            error_item = QtWidgets.QTableWidgetItem("错误")
            error_value = QtWidgets.QTableWidgetItem(f"无法加载属性: {str(e)}")
            self.properties_table.setItem(0, 0, error_item)
            self.properties_table.setItem(0, 1, error_value)
