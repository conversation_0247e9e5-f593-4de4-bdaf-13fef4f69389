# -*- coding: utf-8 -*-
"""Import Test."""

# Import future modules
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

# Import built-in modules
import importlib
import pkgutil
import sys
import traceback

# Import local modules
import cgame_avatar_factory.hair_studio as hair_studio


def test_imports():
    """Test import modules."""
    print("\n开始测试所有模块的导入...")
    prefix = "{}.".format(hair_studio.__name__)
    iter_packages = pkgutil.walk_packages(
        hair_studio.__path__,  # noqa: WPS609
        prefix,
    )

    success_count = 0
    failed_modules = []

    for _, name, _ in iter_packages:
        module_name = name if name.startswith(prefix) else prefix + name
        try:
            print(f"正在导入: {module_name}")
            importlib.import_module(module_name)
            success_count += 1
        except Exception as e:
            failed_modules.append((module_name, str(e)))
            print(f"导入失败: {module_name} - {e}")
            traceback.print_exc()

    total = success_count + len(failed_modules)
    print(f"\n导入测试完成: 总计 {total} 个模块, 成功 {success_count} 个, 失败 {len(failed_modules)} 个")

    if failed_modules:
        print("\n失败的模块:")
        for module, error in failed_modules:
            print(f"  - {module}: {error}")
        return False
    else:
        print("\n所有模块导入成功!")
        return True


if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
