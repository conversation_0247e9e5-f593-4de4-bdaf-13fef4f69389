# Import built-in modules
import logging
import os

# Import third-party modules
import dayu_widgets
from dayu_widgets.tab_widget import MTabWidget
import maya.cmds as cmds
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.animation_theater.ui.face_anim_preview import FacePreviewWidget
from cgame_avatar_factory.animation_theater.ui.face_workshop import FaceWorkshopWidget
from cgame_avatar_factory.common import constants as const
from cgame_avatar_factory.common.ui.layout import FramelessVLayout
from cgame_avatar_factory.common.ui.mixin.style_mixin import MStyleMixin
from cgame_avatar_factory.common.ui.overlay_widgets import create_overlay
from cgame_avatar_factory.common.utils import utils
from cgame_avatar_factory.face_sculpting_center.build_face import setup_riglogic as riglogic
from cgame_avatar_factory.face_sculpting_center.ui.pages.base_merge import BaseMergePage
from cgame_avatar_factory.face_sculpting_center.utils import joint_utils as joint
from cgame_avatar_factory.face_sculpting_center.utils import dna_utils as dna
from cgame_avatar_factory.face_sculpting_center.utils import mesh_util


def find_target_and_create_overlay(source_widget: QtWidgets.QWidget, target_class: type) -> bool:
    """
    在主窗口(MainWidget)中查找指定类型的目标组件并调用其create_overlay_delayed方法

    参数:
        source_widget: 源组件，用于查找主窗口
        target_class: 目标组件的类

    返回值:
        bool: 是否成功找到并创建了遮罩层
    """
    main_window = None
    parent = source_widget.parent()

    while parent:
        if parent.objectName() == "MainWidget":
            main_window = parent
            break
        parent = parent.parent()

    if main_window:
        for child in main_window.findChildren(target_class):
            if isinstance(child, target_class):
                child.create_overlay_delayed()
                return True

    return False


class AnimationTheaterPage(QtWidgets.QFrame):
    sig_animation_changed = QtCore.Signal(str)
    sig_sequence_changed = QtCore.Signal(str)
    sig_effect_changed = QtCore.Signal(str)
    sig_export_settings_changed = QtCore.Signal(dict)

    _instance = None

    @staticmethod
    def instance():
        if AnimationTheaterPage._instance is None:
            AnimationTheaterPage._instance = AnimationTheaterPage()
        return AnimationTheaterPage._instance

    def __init__(self, parent=None):
        super(AnimationTheaterPage, self).__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.dna_path = os.path.join(const.CONFIG_PATH, const.FACE_RIG_DNA)
        self.anim_attr_json_path = os.path.join(const.CONFIG_PATH, const.ANIM_ATTR_JSON)
        self.anim_list = []
        self.setup_ui()

    def setup_ui(self):
        self.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.main_layout = FramelessVLayout()
        self.main_layout.setContentsMargins(5, 5, 5, 5)
        self.setLayout(self.main_layout)

        self.tab_widget = MStyleMixin.instance_wrapper(MTabWidget())
        self.tab_widget.setTabPosition(QtWidgets.QTabWidget.North)
        self.main_layout.addWidget(self.tab_widget)

        self.face_tab = self.create_face_tab()
        self.tab_2_tab = self.create_tab_2_tab()

        self.tab_widget.addTab(self.face_tab, "表情工坊")
        self.tab_widget.addTab(self.tab_2_tab, "Tab-2")

    def on_binding_button_clicked(self):
        self.logger.info("点击了表情绑定转换按钮")
        find_target_and_create_overlay(self, BaseMergePage)
        to_duplicate_face_rig_mesh()
        self.adaptive_bone_position()
        dna.import_all_skin_clusters(self.dna_path)
        output_dna_path = dna.init_face_dna()
        riglogic.SetupRiglogic(output_dna_path, const.FACE_CTRL_NAME, const.FACE_NODE_NAME)
        self.set_animation_list()

    def adaptive_bone_position(self):
        joint_world_position = {}
        source_mesh_data = {}
        target_mesh_data = {}
        reader = dna.load_dna_calib(self.dna_path)
        source_mesh_data[const.BASE_HEAD_MESH_NAME] = mesh_util.get_mesh_data(const.BAK_HEAD_MESH_NAME)
        target_mesh_data[const.BASE_HEAD_MESH_NAME] = mesh_util.get_mesh_data(const.BASE_HEAD_MESH_NAME)
        for i in range(reader.getJointCount()):
            joint_name = reader.getJointName(i)
            joint_world_position[joint_name] = mesh_util.get_joint_world_position(joint_name)
        joint.AdaptiveBonePosition(source_mesh_data, target_mesh_data, joint_world_position)

    def create_face_tab(self):
        face_workshop = FaceWorkshopWidget()
        face_workshop.sig_face_selected.connect(self._on_face_selected)
        face_workshop.sig_face_edited.connect(self._on_face_edited)
        return face_workshop

    def _on_face_selected(self, face_name):
        self.logger.info(f"选择了表情: {face_name}")
        self.sig_animation_changed.emit(face_name)

    def _on_face_edited(self, face_data):
        self.logger.info(f"编辑了表情: {face_data}")
        self.sig_effect_changed.emit(str(face_data))

    def create_tab_2_tab(self):
        tab = QtWidgets.QWidget()
        layout = FramelessVLayout()
        tab.setLayout(layout)

        placeholder = dayu_widgets.MLabel("Tab-2 - 功能开发中")
        placeholder.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(placeholder)
        return tab

    def set_animation_list(self):
        if not self.anim_list:
            data = utils.read_json(self.anim_attr_json_path)
            self.anim_list = list(data.keys())

            for widget in self.findChildren(FacePreviewWidget):
                face_anim_preview = widget
                break
            else:
                face_anim_preview = FacePreviewWidget()

            list_widget = face_anim_preview.return_list_widget()
            for anim_name in self.anim_list:
                item = QtWidgets.QListWidgetItem(anim_name)
                item.setIcon(QtWidgets.QApplication.style().standardIcon(QtWidgets.QStyle.SP_FileIcon))
                list_widget.addItem(item)

    def showEvent(self, event):
        super(AnimationTheaterPage, self).showEvent(event)

        if not hasattr(self, "overlay") or not self.overlay:
            QtCore.QTimer.singleShot(100, self.create_overlay_delayed)

    def create_overlay_delayed(self):
        self.overlay = create_overlay(
            "表情绑定转换",
            self.on_binding_button_clicked,
            self,
            self.tab_widget,
        )


def to_duplicate_face_rig_mesh():
    mesh_name_list = dna.TemplateDna.get_mesh_name()
    for base_mesh_name in mesh_name_list:
        new_name = base_mesh_name + f"_{const.PINCH_OUTPUT_DNA_NAME}"
        bak_name = base_mesh_name + const.FACE_MESH_SUFFIX
        cmds.rename(base_mesh_name, new_name)
        cmds.duplicate(new_name, name=bak_name)
        cmds.setAttr(f"{new_name}.visibility", 0)
        cmds.rename(bak_name, base_mesh_name)
