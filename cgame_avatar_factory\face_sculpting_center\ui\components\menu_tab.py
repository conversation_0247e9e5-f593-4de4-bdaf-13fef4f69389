# Import third-party modules
import dayu_widgets
from dayu_widgets import dayu_theme
from dayu_widgets.menu_tab_widget import MBlockButtonGroup
from dayu_widgets.menu_tab_widget import MMenuTabWidget
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.common.ui.mixin.style_mixin import MStyleMixin


class DNAPushButton(QtWidgets.QPushButton):
    """
    QPushButton.

    Property:
        dayu_size: The size of push button
    """

    def __init__(self, text="", icon=None, parent=None):
        if icon is None:
            super(DNAPushButton, self).__init__(text=text, parent=parent)
        else:
            super(DNAPushButton, self).__init__(icon=icon, text=text, parent=parent)
        self._dayu_size = dayu_theme.default_size

    def get_dayu_size(self):
        """
        Get the push button height
        :return: integer
        """
        return self._dayu_size

    def set_dayu_size(self, value):
        """
        Set the avatar size.
        :param value: integer
        :return: None
        """
        self._dayu_size = value
        self.style().polish(self)

    dayu_size = QtCore.Property(int, get_dayu_size, set_dayu_size)


class DNABlockButtonGroup(MBlockButtonGroup):
    """Button group for menu tab, support menu for button."""

    def __init__(self, tab, orientation=QtCore.Qt.Horizontal, parent=None):
        super(DNABlockButtonGroup, self).__init__(tab=tab, orientation=orientation, parent=parent)
        self._main_layout.addSpacing(5)

    def create_button(self, data_dict):
        button = MStyleMixin.instance_wrapper(DNAPushButton())
        button.frameless().hide_menu_indicator().transparent_background()

        if data_dict.get("menu"):
            button.setMenu(data_dict.get("menu"))
        button.set_dayu_size(self._menu_tab.get_dayu_size())
        return button


@MStyleMixin.cls_wrapper
class DNAMenuTabWidget(dayu_widgets.MMenuTabWidget):
    """Seamless Menu Tab Widget"""

    def __init__(self, orientation=QtCore.Qt.Horizontal, parent=None):
        super(MMenuTabWidget, self).__init__(parent=parent)
        self.tool_button_group = DNABlockButtonGroup(tab=self, orientation=orientation)

        if orientation == QtCore.Qt.Horizontal:
            self._bar_layout = QtWidgets.QHBoxLayout()
            # Amend the margin to prevent the gap between tab bar and the main widget
            self._bar_layout.setContentsMargins(0, 0, 10, 0)
        else:
            self._bar_layout = QtWidgets.QVBoxLayout()
            self._bar_layout.setContentsMargins(0, 0, 0, 0)

        self._bar_layout.addWidget(self.tool_button_group)
        self._bar_layout.addStretch()
        bar_widget = QtWidgets.QWidget()
        bar_widget.setObjectName("bar_widget")
        bar_widget.setLayout(self._bar_layout)
        bar_widget.setAttribute(QtCore.Qt.WA_StyledBackground)
        main_lay = QtWidgets.QVBoxLayout()
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_lay.setSpacing(0)
        main_lay.addWidget(bar_widget)

        # Removed addSpacing to prevent the gap between tab bar and the main widget
        self.setLayout(main_lay)
        self._dayu_size = dayu_theme.large
