"""Icon utilities for Hair Studio.

This module provides utilities for loading icons with fallback support
to Qt standard icons when custom icons are not available. Includes advanced
icon processing for consistent sizing and aspect ratio handling.
"""

# Import built-in modules
# Import standard library
import logging

# Import third-party modules
# Import dayu widgets
from dayu_widgets.qt import MIcon

# Import Qt modules
from qtpy import QtCore
from qtpy import QtGui
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.constants import DEFAULT_ICON_SIZE
from cgame_avatar_factory.hair_studio.constants import QT_ICON_FALLBACKS


def create_square_icon_from_pixmap(source_pixmap, target_size, background_color=None):
    """Create a square icon from a source pixmap with proper aspect ratio handling.

    Args:
        source_pixmap (QtGui.QPixmap): Source pixmap to process
        target_size (int): Target square size (width and height)
        background_color (QtGui.QColor, optional): Background color for padding.
                                                  Defaults to transparent.

    Returns:
        QtGui.QPixmap: Processed square pixmap
    """
    if source_pixmap.isNull():
        return source_pixmap

    # Create target pixmap with square dimensions
    target_pixmap = QtGui.QPixmap(target_size, target_size)

    # Set background color (transparent by default)
    if background_color:
        target_pixmap.fill(background_color)
    else:
        target_pixmap.fill(QtCore.Qt.transparent)

    # Calculate scaling to fit within target size while maintaining aspect ratio
    source_width = source_pixmap.width()
    source_height = source_pixmap.height()

    if source_width == 0 or source_height == 0:
        return target_pixmap

    # Calculate scale factor to fit within target size
    scale_factor = min(target_size / source_width, target_size / source_height)

    # Calculate scaled dimensions
    scaled_width = int(source_width * scale_factor)
    scaled_height = int(source_height * scale_factor)

    # Scale the source pixmap
    scaled_pixmap = source_pixmap.scaled(
        scaled_width,
        scaled_height,
        QtCore.Qt.KeepAspectRatio,
        QtCore.Qt.SmoothTransformation,
    )

    # Calculate position to center the scaled pixmap
    x_offset = (target_size - scaled_width) // 2
    y_offset = (target_size - scaled_height) // 2

    # Draw the scaled pixmap onto the target pixmap
    painter = QtGui.QPainter(target_pixmap)
    painter.setRenderHint(QtGui.QPainter.Antialiasing)
    painter.setRenderHint(QtGui.QPainter.SmoothPixmapTransform)
    painter.drawPixmap(x_offset, y_offset, scaled_pixmap)
    painter.end()

    return target_pixmap


def get_icon_with_fallback(icon_name, size=None):
    """Get an icon with fallback to Qt standard icons and proper size handling.

    This function first tries to load the icon using MIcon (dayu_widgets).
    If that fails (icon file not found), it falls back to Qt standard icons.
    Ensures consistent sizing and aspect ratio handling.

    Args:
        icon_name (str): Name of the icon file or constant
        size (tuple, optional): Icon size as (width, height). Defaults to None.

    Returns:
        QtGui.QIcon: The loaded icon with proper sizing, or a fallback Qt standard icon
    """
    logger = logging.getLogger(__name__)

    # Determine target size
    if size is None:
        target_width, target_height = DEFAULT_ICON_SIZE, DEFAULT_ICON_SIZE
    else:
        target_width, target_height = size

    # For square icons, use the smaller dimension to ensure it fits
    target_size = min(target_width, target_height)

    try:
        # First try to load with MIcon
        icon = MIcon(icon_name)

        # Test if the icon is valid by trying to get a pixmap
        test_pixmap = icon.pixmap(target_size, target_size)
        if not test_pixmap.isNull():
            # Process the pixmap to ensure proper sizing and aspect ratio
            processed_pixmap = create_square_icon_from_pixmap(test_pixmap, target_size)
            processed_icon = QtGui.QIcon(processed_pixmap)

            return processed_icon
        else:
            logger.debug("Icon pixmap is null for: %s, using fallback", icon_name)

    except Exception as e:
        logger.debug(
            "Failed to load icon %s with MIcon: %s, using fallback",
            icon_name,
            str(e),
        )

    # Fallback to Qt standard icon
    return get_qt_standard_icon(icon_name, size)


def get_qt_standard_icon(icon_name, size=None):
    """Get a Qt standard icon as fallback with proper size handling.

    Args:
        icon_name (str): Name of the icon (used to look up fallback mapping)
        size (tuple, optional): Icon size as (width, height). Defaults to None.

    Returns:
        QtGui.QIcon: Qt standard icon with proper sizing
    """
    logger = logging.getLogger(__name__)

    # Determine target size
    if size is None:
        target_width, target_height = DEFAULT_ICON_SIZE, DEFAULT_ICON_SIZE
    else:
        target_width, target_height = size

    target_size = min(target_width, target_height)

    # Get the Qt standard icon name from our mapping
    qt_icon_name = QT_ICON_FALLBACKS.get(icon_name, "SP_FileIcon")

    try:
        # Get the application style
        style = QtWidgets.QApplication.style()
        if not style:
            logger.warning("No application style available, creating default icon")
            return create_default_icon(size)

        # Get the standard icon enum value
        icon_enum = getattr(
            QtWidgets.QStyle,
            qt_icon_name,
            QtWidgets.QStyle.SP_FileIcon,
        )

        # Get the icon from style
        icon = style.standardIcon(icon_enum)

        # Process the icon to ensure proper sizing
        if icon and not icon.isNull():
            source_pixmap = icon.pixmap(target_size, target_size)
            processed_pixmap = create_square_icon_from_pixmap(
                source_pixmap,
                target_size,
            )
            processed_icon = QtGui.QIcon(processed_pixmap)

            logger.debug(
                "Using processed Qt standard icon %s for %s (size: %dx%d)",
                qt_icon_name,
                icon_name,
                target_size,
                target_size,
            )
            return processed_icon

        logger.debug("Using Qt standard icon %s for %s", qt_icon_name, icon_name)
        return icon

    except Exception as e:
        logger.warning("Failed to get Qt standard icon %s: %s", qt_icon_name, str(e))
        return create_default_icon(size)


def create_default_icon(size=None):
    """Create a simple default icon when all else fails.

    Args:
        size (tuple, optional): Icon size as (width, height). Defaults to (DEFAULT_ICON_SIZE, DEFAULT_ICON_SIZE).

    Returns:
        QtGui.QIcon: A simple colored square icon
    """
    if size is None:
        target_size = DEFAULT_ICON_SIZE
    else:
        width, height = size
        target_size = min(width, height)  # Ensure square icon

    # Create a simple colored pixmap
    pixmap = QtGui.QPixmap(target_size, target_size)
    pixmap.fill(QtGui.QColor("#4080FF"))  # Blue color

    # Add a simple border and center dot for visual distinction
    painter = QtGui.QPainter(pixmap)
    painter.setRenderHint(QtGui.QPainter.Antialiasing)

    # Draw border
    painter.setPen(QtGui.QPen(QtGui.QColor("#FFFFFF"), 1))
    painter.drawRect(0, 0, target_size - 1, target_size - 1)

    # Draw center dot to indicate this is a fallback icon
    center = target_size // 2
    painter.setBrush(QtGui.QColor("#FFFFFF"))
    painter.drawEllipse(center - 2, center - 2, 4, 4)

    painter.end()

    return QtGui.QIcon(pixmap)


def get_pixmap_with_fallback(icon_name, width, height):
    """Get a pixmap with fallback support.

    Args:
        icon_name (str): Name of the icon
        width (int): Desired width
        height (int): Desired height

    Returns:
        QtGui.QPixmap: The icon pixmap
    """
    icon = get_icon_with_fallback(icon_name, (width, height))
    return icon.pixmap(width, height)


def set_button_icon_with_fallback(button, icon_name, size=None):
    """Set a button's icon with fallback support and proper sizing.

    Args:
        button (QtWidgets.QAbstractButton): The button to set icon for
        icon_name (str): Name of the icon
        size (tuple, optional): Icon size as (width, height). Defaults to None.
    """
    logger = logging.getLogger(__name__)

    # Determine target size
    if size is None:
        target_size = DEFAULT_ICON_SIZE
    else:
        width, height = size
        target_size = min(width, height)

    try:
        # Get the processed icon
        icon = get_icon_with_fallback(icon_name, size)
        if icon and not icon.isNull():
            button.setIcon(icon)

            # Ensure the button's icon size is set correctly
            button.setIconSize(QtCore.QSize(target_size, target_size))
        else:
            logger.warning("Icon is null for button: %s", icon_name)
            # Create a simple default icon
            default_icon = create_default_icon(size)
            button.setIcon(default_icon)
            button.setIconSize(QtCore.QSize(target_size, target_size))
            logger.debug("Used default icon fallback for button: %s", icon_name)
    except Exception as e:
        logger.error("Failed to set button icon for %s: %s", icon_name, str(e))
        # Try to create a simple default icon
        try:
            default_icon = create_default_icon(size)
            button.setIcon(default_icon)
            button.setIconSize(QtCore.QSize(target_size, target_size))
        except Exception as e2:
            logger.error("Even default icon creation failed: %s", str(e2))


def set_label_pixmap_with_fallback(label, icon_name, width, height):
    """Set a label's pixmap with fallback support.

    Args:
        label (QtWidgets.QLabel): The label to set pixmap for
        icon_name (str): Name of the icon
        width (int): Desired width
        height (int): Desired height
    """
    logger = logging.getLogger(__name__)

    try:
        pixmap = get_pixmap_with_fallback(icon_name, width, height)
        if pixmap and not pixmap.isNull():
            label.setPixmap(pixmap)
            logger.debug(
                "Successfully set pixmap for label: %s (%dx%d)",
                icon_name,
                width,
                height,
            )
        else:
            logger.warning("Pixmap is null for icon: %s", icon_name)
            # Create a simple colored rectangle as ultimate fallback
            fallback_pixmap = QtGui.QPixmap(width, height)
            fallback_pixmap.fill(QtGui.QColor("#4080FF"))
            label.setPixmap(fallback_pixmap)
            logger.debug("Used colored rectangle fallback for: %s", icon_name)
    except Exception as e:
        logger.error("Failed to set label pixmap for %s: %s", icon_name, str(e))
        # Create a simple colored rectangle as ultimate fallback
        try:
            fallback_pixmap = QtGui.QPixmap(width, height)
            fallback_pixmap.fill(
                QtGui.QColor("#FF4080"),
            )  # Different color to indicate error
            label.setPixmap(fallback_pixmap)
        except Exception as e2:
            logger.error("Even fallback pixmap creation failed: %s", str(e2))
