#!/usr/bin/env python
# -*- coding: utf-8 -*-

# Import built-in modules
import logging

# Import third-party modules
from dayu_widgets import dayu_theme
from dayu_widgets.divider import MDivider
from dayu_widgets.label import <PERSON><PERSON><PERSON><PERSON>
from qtpy import QtCore
from qtpy import Qt<PERSON><PERSON>
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.common import constants as const
from cgame_avatar_factory.common.ui.layout import FramelessHLayout
from cgame_avatar_factory.common.ui.layout import FramelessVLayout
from cgame_avatar_factory.common.ui.mixin.style_mixin import MStyleMixin
from cgame_avatar_factory.face_sculpting_center.utils import edit_mode_utils


class BlendShapeItemWidget(QtWidgets.QWidget):
    """Widget for displaying and controlling a blendshape target item.

    This widget provides controls for toggling visibility, editing mode, and deletion
    of a blendshape target.

    Attributes:
        edit_toggled: Signal emitted when edit mode is toggled
        visibility_toggled: Signal emitted when visibility is toggled
        delete_requested: Signal emitted when deletion is requested
        weight_changed: Signal emitted when weight is changed via slider
    """

    edit_toggled = QtCore.Signal(str, bool)
    visibility_toggled = QtCore.Signal(str, bool)
    delete_requested = QtCore.Signal(str)
    weight_changed = QtCore.Signal(str, float)

    current_slider_widget = None

    def __init__(self, target_name, is_visible=True, is_editing=False, parent=None):
        """Initialize the blendshape item widget.

        Args:
            target_name: Name of the blendshape target
            is_visible: Initial visibility state
            is_editing: Initial editing state
            parent: Parent widget
        """
        super(BlendShapeItemWidget, self).__init__(parent)
        self.target_name = target_name
        self.is_visible = is_visible
        self.is_editing = is_editing
        self.weight_slider_visible = False
        self._setup_ui()
        self.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_context_menu)

    def _setup_ui(self):
        """Set up the user interface components."""
        self.main_layout = FramelessHLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(5)
        self.setLayout(self.main_layout)

        self.stacked_widget = QtWidgets.QStackedWidget()

        self.content_widget = QtWidgets.QWidget()
        self.content_layout = FramelessHLayout()
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(5)
        self.content_widget.setLayout(self.content_layout)

        self.eye_button = QtWidgets.QToolButton()
        self.eye_button.setFixedSize(24, 24)
        self.eye_button.setCursor(QtCore.Qt.PointingHandCursor)
        self._update_eye_icon()
        self.eye_button.clicked.connect(self._on_eye_clicked)

        self.name_label = QtWidgets.QLabel(self.target_name)
        self.name_label.setStyleSheet(f"color: {const.DAYU_SECONDARY_TEXT_COLOR};")
        self.name_label.setCursor(QtCore.Qt.PointingHandCursor)
        self.name_label.mousePressEvent = self._on_name_label_clicked

        self.edit_button = QtWidgets.QPushButton("编辑")
        self.edit_button.setFixedSize(50, 32)

        self.edit_button.setStyleSheet(
            f"""
            QPushButton {{
                background-color: {const.DAYU_BG_IN_COLOR};
                color: {const.DAYU_SECONDARY_TEXT_COLOR};
            }}
            QPushButton:hover {{
                background-color: {const.DAYU_BG_OUT_COLOR};
            }}
            QPushButton:pressed {{
                background-color: {const.DAYU_BG_COLOR};
            }}
            QPushButton:checked {{
                background-color: {const.APP_PRIMARY_COLOR};
                color: white;
            }}
        """
        )
        self.edit_button.setCheckable(True)
        self.edit_button.setChecked(self.is_editing)
        self.edit_button.clicked.connect(self._on_edit_clicked)

        self.content_layout.addWidget(self.eye_button)
        self.content_layout.addWidget(self.name_label, 1)
        self.content_layout.addWidget(self.edit_button)

        self.slider_widget = QtWidgets.QWidget()
        self.slider_layout = FramelessHLayout()
        self.slider_layout.setContentsMargins(5, 0, 5, 0)
        self.slider_layout.setSpacing(5)
        self.slider_widget.setLayout(self.slider_layout)

        self.back_button = QtWidgets.QToolButton()
        self.back_button.setText("←")
        self.back_button.setToolTip("返回")
        self.back_button.setCursor(QtCore.Qt.PointingHandCursor)
        self.back_button.setFixedSize(24, 24)
        self.back_button.setStyleSheet(
            f"""
            QToolButton {{
                background-color: {const.DAYU_BG_IN_COLOR};
                color: {const.DAYU_SECONDARY_TEXT_COLOR};
                border: 1px solid {const.DAYU_BORDER_COLOR};
                border-radius: 3px;
            }}
            QToolButton:hover {{
                background-color: {const.DAYU_BG_OUT_COLOR};
                color: {const.DAYU_PRIMARY_TEXT_COLOR};
            }}
            QToolButton:pressed {{
                background-color: {const.DAYU_BG_COLOR};
            }}
            """
        )
        self.back_button.clicked.connect(self._on_back_button_clicked)

        self.weight_slider = QtWidgets.QSlider(QtCore.Qt.Horizontal)
        self.weight_slider.setRange(0, 100)
        self.weight_slider.setValue(100 if self.is_visible else 0)
        self.weight_slider.setStyleSheet(
            f"""
            QSlider::groove:horizontal {{
                border: 1px solid {const.DAYU_BORDER_COLOR};
                height: 6px;
                background: {const.DAYU_BG_IN_COLOR};
                margin: 0px;
                border-radius: 3px;
            }}
            QSlider::handle:horizontal {{
                background: {const.APP_PRIMARY_COLOR};
                border: none;
                width: 16px;
                height: 16px;
                margin: -5px 0;
                border-radius: 8px;
            }}
            QSlider::handle:horizontal:hover {{
                background: {const.DAYU_PRIMARY_5};
            }}
            QSlider::sub-page:horizontal {{
                background: {const.DAYU_PRIMARY_7};
                border: 1px solid {const.DAYU_BORDER_COLOR};
                height: 6px;
                border-radius: 3px;
            }}
            """
        )

        self.weight_value_label = QtWidgets.QLabel(f"{self.weight_slider.value() / 100:.2f}")
        self.weight_value_label.setFixedWidth(40)
        self.weight_value_label.setAlignment(QtCore.Qt.AlignRight | QtCore.Qt.AlignVCenter)
        self.weight_value_label.setStyleSheet(f"color: {const.DAYU_PRIMARY_TEXT_COLOR};")

        self.slider_layout.addWidget(self.back_button)
        self.slider_layout.addWidget(self.weight_slider, 1)
        self.slider_layout.addWidget(self.weight_value_label)

        self.weight_slider.valueChanged.connect(self._on_weight_changed)

        self.stacked_widget.addWidget(self.content_widget)
        self.stacked_widget.addWidget(self.slider_widget)

        self.main_layout.addWidget(self.stacked_widget, 1)

        try:
            blendshape_name = f"{edit_mode_utils.const.GOZ_MESH_NAME}_{edit_mode_utils.const.GOZ_BLEND_SHAPE_SUFFIX}"
            current_weight = edit_mode_utils.cmds.getAttr(f"{blendshape_name}.{self.target_name}")
            self.weight_slider.setValue(int(current_weight * 100))
        except:
            pass

    def _on_name_label_clicked(self, event):
        """Handle click event on name label to show weight slider"""
        BlendShapeItemWidget.current_slider_widget = self
        self.stacked_widget.setCurrentIndex(1)

        event.accept()

    def _on_back_button_clicked(self):
        """Handle back button click event to return to main content view"""
        self.stacked_widget.setCurrentIndex(0)  # Switch back to main content view
        BlendShapeItemWidget.current_slider_widget = None

    def _on_weight_changed(self, value):
        """Handle weight slider value change event"""
        weight = value / 100.0
        self.weight_value_label.setText(f"{weight:.2f}")
        try:
            edit_mode_utils.set_blendshape_weight(self.target_name, weight)
            self.is_visible = weight > 0
            self._update_eye_icon()
            self.weight_changed.emit(self.target_name, weight)
        except Exception as e:
            logging.getLogger(__name__).exception(f"设置权重时发生错误: {e}")

    def _create_eye_icon(self, visible):
        """Create an eye icon for the visibility button.

        Args:
            visible: Whether the eye should be open (visible) or closed

        Returns:
            QIcon: The created eye icon
        """
        icon = QtGui.QIcon()
        pixmap = QtGui.QPixmap(24, 24)
        pixmap.fill(QtCore.Qt.transparent)

        painter = QtGui.QPainter(pixmap)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        if visible:
            pen = QtGui.QPen(QtGui.QColor(const.APP_PRIMARY_COLOR))
            pen.setWidth(2)
            painter.setPen(pen)
            painter.drawEllipse(2, 8, 20, 10)
            painter.setBrush(QtGui.QBrush(QtGui.QColor(const.APP_PRIMARY_COLOR)))
            painter.drawEllipse(9, 10, 6, 6)
            painter.setBrush(QtGui.QBrush(QtGui.QColor(const.BUTTON_CHECKED_TEXT)))
            painter.drawEllipse(11, 11, 2, 2)
        else:
            pen = QtGui.QPen(QtGui.QColor(const.DAYU_SECONDARY_TEXT_COLOR))
            pen.setWidth(2)
            painter.setPen(pen)
            path = QtGui.QPainterPath()
            path.moveTo(2, 14)
            path.cubicTo(8, 8, 16, 8, 22, 14)
            painter.drawPath(path)
            painter.drawLine(5, 10, 8, 7)
            painter.drawLine(12, 8, 12, 5)
            painter.drawLine(19, 10, 16, 7)

        painter.end()
        icon.addPixmap(pixmap, QtGui.QIcon.Normal, QtGui.QIcon.Off)
        return icon

    def _update_eye_icon(self):
        """Update the eye icon based on current visibility state."""
        self.eye_button.setIcon(self._create_eye_icon(self.is_visible))
        self.eye_button.setToolTip("点击隐藏权重" if self.is_visible else "点击显示权重")

    def _on_eye_clicked(self):
        """Handle eye button click to toggle visibility."""
        self.is_visible = not self.is_visible
        self._update_eye_icon()
        self.visibility_toggled.emit(self.target_name, self.is_visible)

    def _on_edit_clicked(self):
        """Handle edit button click to toggle editing mode."""
        self.is_editing = self.edit_button.isChecked()
        self.edit_button.setText("取消" if self.is_editing else "编辑")
        self.edit_toggled.emit(self.target_name, self.is_editing)

    def set_visible(self, visible):
        """Set the visibility state of the blendshape target.

        Args:
            visible: New visibility state
        """
        if self.is_visible != visible:
            self.is_visible = visible
            self._update_eye_icon()

    def set_editing(self, editing):
        """Set the editing state of the blendshape target.

        Args:
            editing: New editing state
        """
        self.is_editing = editing
        self.edit_button.setChecked(editing)
        self.edit_button.setText("取消" if editing else "编辑")

    def _show_context_menu(self, position):
        """Show the context menu for additional operations.

        Args:
            position: Position where to show the menu
        """
        menu = QtWidgets.QMenu(self)
        delete_action = menu.addAction("删除")
        delete_action.triggered.connect(lambda: self._on_delete_clicked())

        menu.setStyleSheet(
            f"""
            QMenu::item:selected {{
                background-color: {const.DAYU_ERROR_COLOR};
            }}
        """
        )
        menu.exec_(self.mapToGlobal(position))

    def _on_delete_clicked(self):
        """Handle delete action from context menu."""
        self.delete_requested.emit(self.target_name)


class ContentPanel(QtWidgets.QWidget):
    """Panel for displaying and managing blendshape targets.

    This panel provides a list of blendshape targets with controls for visibility,
    editing, and deletion. It also includes buttons for freezing and creating edit layers.
    """

    def __init__(self, parent=None):
        """Initialize the content panel.

        Args:
            parent: Parent widget
        """
        super(ContentPanel, self).__init__(parent)
        self.logger = logging.getLogger(__name__)
        self._target_visibility = {}
        self._target_editing = {}
        self.selected_target = None
        self._setup_ui()

    def _setup_ui(self):
        """Set up the user interface components."""
        self.main_layout = FramelessVLayout()
        self.main_layout.setContentsMargins(0, 5, 0, 0)
        self.main_layout.setSpacing(0)
        self.setLayout(self.main_layout)

        self.title_container = MStyleMixin.cls_wrapper(QtWidgets.QWidget)()
        self.title_container.frameless()
        self.title_layout = FramelessVLayout()
        self.title_container.setLayout(self.title_layout)

        self.title_label = MStyleMixin.instance_wrapper(MLabel("编辑模式"))
        self.title_label.setAlignment(QtCore.Qt.AlignCenter)
        self.title_label.foreground_color(const.DAYU_PRIMARY_TEXT_COLOR)
        font = self.title_label.font()
        font.setPointSize(dayu_theme.h2_size)
        self.title_label.setFont(font)
        self.title_layout.addWidget(self.title_label)

        self.title_divider = MDivider()
        self.title_layout.addWidget(self.title_divider)
        self.main_layout.insertWidget(0, self.title_container)

        self.content_container = MStyleMixin.cls_wrapper(QtWidgets.QWidget)()
        self.content_container.frameless().border_radius(4).background_color(
            const.DAYU_BG_IN_COLOR,
        )
        self.content_layout = FramelessVLayout()
        self.content_layout.setContentsMargins(8, 8, 8, 8)
        self.content_layout.setSpacing(8)
        self.content_container.setLayout(self.content_layout)

        self.targets_list = QtWidgets.QListWidget()
        self.targets_list.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)

        self.targets_list.setStyleSheet(
            f"""
            QListWidget {{
                background-color: {const.DAYU_BG_IN_COLOR};
                border: 1px solid {const.DAYU_BORDER_COLOR};
                color: {const.DAYU_SECONDARY_TEXT_COLOR};
            }}
            QListWidget::item {{
            }}
            QListWidget::item:selected {{
                background-color: {const.APP_PRIMARY_COLOR};
            }}
            QListWidget::item:hover:!selected {{
                background-color: {const.DAYU_BG_OUT_COLOR};
            }}
        """
        )

        self.content_layout.addWidget(self.targets_list, 1)
        self.main_layout.addWidget(self.content_container, 1)

        self.bottom_container = QtWidgets.QWidget()

        self.bottom_container.setStyleSheet(
            f"""
            background-color: {const.DAYU_BG_IN_COLOR};
            border-radius: 4px;
        """
        )
        self.bottom_layout = FramelessHLayout()
        self.bottom_layout.setContentsMargins(8, 8, 8, 8)
        self.bottom_layout.setSpacing(8)
        self.bottom_container.setLayout(self.bottom_layout)

        self.freeze_button = QtWidgets.QPushButton("吸附")
        self.freeze_button.setFixedHeight(40)

        self.freeze_button.setStyleSheet(
            f"""
            QPushButton {{
                background-color: {const.DAYU_BG_IN_COLOR};
            }}
            QPushButton:hover {{
                background-color: {const.DAYU_BG_OUT_COLOR};
            }}
            QPushButton:pressed {{
                background-color: {const.DAYU_BG_COLOR};
            }}
        """
        )
        self.freeze_button.clicked.connect(self._on_freeze_clicked)
        self.freeze_button.setCursor(QtCore.Qt.PointingHandCursor)
        self._frozen_state = False

        self.create_edit_layer_button = QtWidgets.QPushButton("创建编辑层")
        self.create_edit_layer_button.setFixedHeight(40)

        self.create_edit_layer_button.setStyleSheet(
            f"""
            QPushButton {{
                background-color: {const.APP_PRIMARY_COLOR};
            }}
            QPushButton:hover {{
                background-color: {const.DAYU_PRIMARY_5};
            }}
            QPushButton:pressed {{
                background-color: {const.DAYU_PRIMARY_7};
            }}
        """
        )
        self.create_edit_layer_button.clicked.connect(self._on_create_edit_layer_clicked)
        self.create_edit_layer_button.setCursor(QtCore.Qt.PointingHandCursor)

        self.bottom_layout.addWidget(self.freeze_button, 1)
        self.bottom_layout.addWidget(self.create_edit_layer_button, 1)

        self.main_layout.addWidget(self.bottom_container)
        self.create_edit_layer_button.setEnabled(True)

        QtWidgets.QApplication.instance().installEventFilter(self)

    def _on_freeze_clicked(self):
        """Handle freeze button click to toggle freeze state."""
        self._frozen_state = not self._frozen_state

        if self._frozen_state:
            self.freeze_button.setText("取消吸附")

            self.freeze_button.setStyleSheet(
                f"""
                QPushButton {{
                    background-color: {const.DAYU_SUCCESS_COLOR};
                }}
                QPushButton:hover {{
                    background-color: {const.DAYU_PRIMARY_5};
                }}
                QPushButton:pressed {{
                    background-color: {const.DAYU_PRIMARY_7};
                }}
            """
            )
        else:
            self.freeze_button.setText("冻结")

            self.freeze_button.setStyleSheet(
                f"""
                QPushButton {{
                    background-color: {const.DAYU_BG_IN_COLOR};
                }}
                QPushButton:hover {{
                    background-color: {const.DAYU_BG_OUT_COLOR};
                }}
                QPushButton:pressed {{
                    background-color: {const.DAYU_BG_COLOR};
                }}
            """
            )

    def _on_create_edit_layer_clicked(self):
        """Handle create edit layer button click to create a new edit layer."""
        try:
            self.create_edit_layer_button.setStyleSheet(
                f"""
                QPushButton {{
                    background-color: {const.DAYU_WARNING_COLOR};
                }}
            """
            )
            original_text = self.create_edit_layer_button.text()
            self.create_edit_layer_button.setText("正在创建...")
            QtWidgets.QApplication.processEvents()

            self.reset_target_editing(self)
            self.remove_selected_target(self)

            target_name = edit_mode_utils.create_edit_layer()
            if not target_name:
                self.logger.warning("无法创建编辑层")
            else:
                self.logger.info(f"成功创建编辑层: {target_name}")
                self._refresh_targets_list()

                for i in range(self.targets_list.count()):
                    item = self.targets_list.item(i)
                    widget = self.targets_list.itemWidget(item)
                    if isinstance(widget, BlendShapeItemWidget) and widget.target_name == target_name:
                        widget.set_editing(True)
                        self._target_editing[target_name] = True
                        self.selected_target = target_name
                        self.blendshape_target_set_enabled(self)
                        self.logger.info(f"自动开启目标编辑状态: {target_name}")
                        break

            self.create_edit_layer_button.setText(original_text)
            self.create_edit_layer_button.setStyleSheet(
                f"""
                QPushButton {{
                    background-color: {const.APP_PRIMARY_COLOR};
                }}
                QPushButton:hover {{
                    background-color: {const.DAYU_PRIMARY_5};
                }}
                QPushButton:pressed {{
                    background-color: {const.DAYU_PRIMARY_7};
                }}
            """
            )
        except Exception as e:
            self.logger.exception(f"创建编辑层时发生错误: {e}")
            self.create_edit_layer_button.setText(original_text)
            self.create_edit_layer_button.setStyleSheet(
                f"""
                QPushButton {{
                    background-color: {const.APP_PRIMARY_COLOR};
                }}
                QPushButton:hover {{
                    background-color: {const.DAYU_PRIMARY_5};
                }}
                QPushButton:pressed {{
                    background-color: {const.DAYU_PRIMARY_7};
                }}
            """
            )

    def _on_visibility_toggled(self, target_name, is_visible):
        """Handle visibility toggle for a blendshape target.

        Args:
            target_name: Name of the target
            is_visible: New visibility state
        """
        self._target_visibility[target_name] = is_visible
        try:
            if hasattr(edit_mode_utils, "set_blendshape_weight"):
                weight = 1.0 if is_visible else 0.0
                edit_mode_utils.set_blendshape_weight(target_name, weight)
                self.logger.info(f"设置目标 {target_name} 的权重为 {weight}")
        except Exception as e:
            self.logger.exception(f"设置目标权重时发生错误: {e}")

    def _on_edit_toggled(self, target_name, is_editing):
        """Handle edit mode toggle for a blendshape target.

        Args:
            target_name: Name of the target
            is_editing: New editing state
        """
        if is_editing:
            for name, editing in self._target_editing.items():
                if editing and name != target_name:
                    for i in range(self.targets_list.count()):
                        item = self.targets_list.item(i)
                        widget = self.targets_list.itemWidget(item)
                        if isinstance(widget, BlendShapeItemWidget) and widget.target_name == name:
                            widget.set_editing(False)
                            self._target_editing[name] = False

        self._target_editing[target_name] = is_editing
        self.selected_target = target_name
        self.blendshape_target_set_enabled(self)

    def _on_delete_requested(self, target_name):
        """Handle delete request for a blendshape target.

        Args:
            target_name: Name of the target to delete
        """
        try:
            if self._target_editing.get(target_name, False):
                self._target_editing[target_name] = False
                if self.selected_target == target_name:
                    self.selected_target = None

            if hasattr(edit_mode_utils, "delete_blendshape_target"):
                success = edit_mode_utils.delete_blendshape_target(target_name)
                if success:
                    self.logger.info(f"成功删除目标: {target_name}")
                    if target_name in self._target_visibility:
                        del self._target_visibility[target_name]
                    if target_name in self._target_editing:
                        del self._target_editing[target_name]
                    self._refresh_targets_list()
                else:
                    self.logger.error(f"删除目标失败: {target_name}")
                    QtWidgets.QMessageBox.warning(self, "删除失败", f"无法删除目标 '{target_name}'。")
            else:
                self.logger.error("删除函数不可用")
                QtWidgets.QMessageBox.warning(self, "功能不可用", "删除功能当前不可用。")
        except Exception as e:
            self.logger.exception(f"删除目标时发生错误: {e}")
            QtWidgets.QMessageBox.critical(self, "错误", f"删除目标时发生错误: {e}")

    @staticmethod
    def remove_selected_target(class_instance):
        """Remove the currently selected target.

        Args:
            class_instance: Instance of ContentPanel
        """
        if class_instance.selected_target:
            class_instance.selected_target = None

    @staticmethod
    def is_edit_mode_enabled(class_instance):
        """Check if edit mode is enabled for any target.

        Args:
            class_instance: Instance of ContentPanel

        Returns:
            bool: True if any target is in edit mode
        """
        if class_instance.selected_target:
            for target_name, is_editing in class_instance._target_editing.items():
                if is_editing:
                    return True
            return False

    @staticmethod
    def reset_target_editing(class_instance):
        """Reset editing state for all targets.

        Args:
            class_instance: Instance of ContentPanel
        """
        for i in range(class_instance.targets_list.count()):
            item = class_instance.targets_list.item(i)
            widget = class_instance.targets_list.itemWidget(item)
            if hasattr(widget, "is_editing") and widget.is_editing:
                widget.set_editing(False)
                class_instance._target_editing[widget.target_name] = False
                class_instance.logger.info(f"重置目标编辑状态: {widget.target_name}")

    @staticmethod
    def blendshape_target_set_enabled(class_instance, in_or_out="in"):
        """Enable the selected blendshape target.

        Args:
            class_instance: Instance of ContentPanel
            in_or_out: Direction of the operation, "in" or "out"
        """
        if class_instance.selected_target:
            if not class_instance.is_edit_mode_enabled(class_instance) and in_or_out == "out":
                return
            else:
                try:
                    if hasattr(edit_mode_utils, "select_blendshape_target"):
                        edit_mode_utils.select_blendshape_target(class_instance.selected_target)
                except Exception as e:
                    class_instance.logger.exception(f"选择目标时发生错误: {e}")

    def _refresh_targets_list(self):
        """Refresh the list of blendshape targets."""
        try:
            targets = edit_mode_utils.get_blendshape_targets()

            self.targets_list.clear()
            for target in targets:
                if target not in self._target_visibility:
                    self._target_visibility[target] = True

                if target not in self._target_editing:
                    self._target_editing[target] = False

                item = QtWidgets.QListWidgetItem()
                item.setSizeHint(QtCore.QSize(0, 50))
                self.targets_list.addItem(item)

                widget = BlendShapeItemWidget(
                    target_name=target,
                    is_visible=self._target_visibility[target],
                    is_editing=self._target_editing[target],
                )
                widget.visibility_toggled.connect(self._on_visibility_toggled)
                widget.edit_toggled.connect(self._on_edit_toggled)
                widget.delete_requested.connect(self._on_delete_requested)
                widget.weight_changed.connect(self._on_weight_slider_changed)

                self.targets_list.setItemWidget(item, widget)

            self.logger.info(f"刷新目标列表，共{len(targets)}个目标")
        except Exception as e:
            self.logger.exception(f"刷新目标列表时发生错误: {e}")

    def _on_weight_slider_changed(self, target_name, weight):
        """Handle weight slider value change event

        Unlike _on_visibility_toggled, this function is specifically for handling
        weight changes from the slider, allowing any weight value between 0-1.

        Args:
            target_name: Name of the target
            weight: Weight value, range 0.0-1.0
        """
        try:
            edit_mode_utils.set_blendshape_weight(target_name, weight)
            self._target_visibility[target_name] = weight > 0
            self.logger.info(f"滑条设置目标 {target_name} 的权重为 {weight}")
        except Exception as e:
            self.logger.exception(f"滑条设置目标权重时发生错误: {e}")

    def eventFilter(self, obj, event):
        """Global event filter to capture click events and hide sliders

        Args:
            obj: Object that triggered the event
            event: Event object

        Returns:
            bool: Whether the event was handled
        """
        try:
            self._handle_mouse_press_event(event)
        except Exception as e:
            if hasattr(self, "logger"):
                self.logger.exception(f"事件过滤器处理事件时发生错误: {e}")
        return super(ContentPanel, self).eventFilter(obj, event)

    def _handle_mouse_press_event(self, event):
        """Handle mouse press events

        Args:
            event: Event object
        """
        if not (hasattr(event, "type") and callable(event.type) and event.type() == QtCore.QEvent.MouseButtonPress):
            return

        if not BlendShapeItemWidget.current_slider_widget:
            return

        if not (hasattr(event, "globalPos") and callable(event.globalPos)):
            return

        pos = event.globalPos()
        slider_widget = BlendShapeItemWidget.current_slider_widget.slider_widget
        slider_rect = QtCore.QRect(
            slider_widget.mapToGlobal(QtCore.QPoint(0, 0)),
            slider_widget.size(),
        )

        if not slider_rect.contains(pos):
            BlendShapeItemWidget.current_slider_widget.stacked_widget.setCurrentIndex(0)
            BlendShapeItemWidget.current_slider_widget = None
