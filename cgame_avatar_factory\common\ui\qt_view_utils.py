# Import third-party modules
from qtpy.QtWidgets import QApplication


def add_style_to_sheet(widget, new_style, modifier=""):
    """
    Add a new style to the given widget's existing style sheet.

    Args:
        widget (QWidget): The widget to which the new style should be added.
        new_style (str): The new style to be added to the widget's style sheet.
        modifier (str, optional): A string to be added to the beginning of the new style.

    Example:
        add_style_to_sheet(my_widget, "{border: none; color: blue;}")
        add_style_to_sheet(my_widget, "{border: none; color: blue;}", "::item")
    """
    if hasattr(widget, "styleSheet"):
        orig_style_sheet = widget.styleSheet()
        new_style_sheet = """
            {0}{1}{2}
        """.format(
            type(widget).__name__, modifier, new_style
        )
        widget.setStyleSheet(orig_style_sheet + new_style_sheet)


class EventProcessing:
    def __enter__(self):
        app = QApplication.instance()
        app.processEvents()

    def __exit__(self, exc_type, exc_value, traceback):
        app = QApplication.instance()
        app.processEvents()
