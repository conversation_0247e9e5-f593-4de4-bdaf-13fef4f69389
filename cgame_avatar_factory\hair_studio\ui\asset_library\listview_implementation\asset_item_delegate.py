"""Asset Item Delegate for QListView implementation.

This module provides a custom QStyledItemDelegate that renders hair assets
with the same visual appearance as the original AssetItem widget, including
thumbnails, text, selection states, and hover effects.
"""

# Import built-in modules
import logging
import os

# Import third-party modules
from qtpy import QtCore
from qtpy import QtGui
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.constants import ASSET_ITEM_HEIGHT
from cgame_avatar_factory.hair_studio.constants import ASSET_ITEM_WIDTH
from cgame_avatar_factory.hair_studio.constants import ASSET_THUMBNAIL_SIZE
from cgame_avatar_factory.hair_studio.constants import BORDER_RADIUS_LARGE
from cgame_avatar_factory.hair_studio.constants import BORDER_RADIUS_MEDIUM
from cgame_avatar_factory.hair_studio.constants import COLOR_BG_DARKER
from cgame_avatar_factory.hair_studio.constants import COLOR_BG_HOVER
from cgame_avatar_factory.hair_studio.constants import COLOR_BG_SELECTED
from cgame_avatar_factory.hair_studio.constants import COLOR_BG_SELECTED_HOVER
from cgame_avatar_factory.hair_studio.constants import COLOR_BORDER_DEFAULT
from cgame_avatar_factory.hair_studio.constants import COLOR_ITEM_SELECTED
from cgame_avatar_factory.hair_studio.constants import FONT_SIZE_NORMAL
from cgame_avatar_factory.hair_studio.constants import LAYOUT_MARGIN_SMALL
from cgame_avatar_factory.hair_studio.constants import LAYOUT_SPACING_MEDIUM


class AssetItemDelegate(QtWidgets.QStyledItemDelegate):
    """Custom delegate for rendering asset items in QListView.

    This delegate replicates the visual appearance of the original AssetItem widget,
    including:
    - Thumbnail rendering with proper scaling and centering
    - Asset name text with ellipsis handling
    - Selection highlighting (blue background and border)
    - Hover effects (light gray background)
    - Responsive sizing support
    """

    def __init__(self, parent=None):
        """Initialize the asset item delegate.

        Args:
            parent (QObject, optional): Parent object
        """
        super().__init__(parent)

        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Cache for loaded thumbnails to improve performance
        self._thumbnail_cache = {}

        # Current scale factor for responsive sizing
        self._scale_factor = 1.0

        # Cached dimensions
        self._item_size = QtCore.QSize(ASSET_ITEM_WIDTH, ASSET_ITEM_HEIGHT)
        self._thumbnail_size = ASSET_THUMBNAIL_SIZE

        # Mouse interaction state
        self._press_position = None

        self._logger.debug("AssetItemDelegate initialized")

    def paint(self, painter, option, index):
        """Paint the asset item.

        Args:
            painter (QPainter): Painter object
            option (QStyleOptionViewItem): Style options
            index (QModelIndex): Model index
        """
        painter.save()
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # Get asset data from model
        from .asset_list_model import AssetListModel

        asset_data = index.data(AssetListModel.AssetDataRole)
        if not asset_data:
            painter.restore()
            return

        # Get item rectangle
        rect = option.rect

        # Determine item state
        is_selected = option.state & QtWidgets.QStyle.State_Selected
        is_hovered = option.state & QtWidgets.QStyle.State_MouseOver

        # Draw background and border
        self._draw_background(painter, rect, is_selected, is_hovered)

        # Calculate layout areas
        content_rect = rect.adjusted(
            LAYOUT_MARGIN_SMALL,
            LAYOUT_MARGIN_SMALL * 4,
            -LAYOUT_MARGIN_SMALL,
            -LAYOUT_MARGIN_SMALL,
        )

        # Calculate thumbnail area (top part)
        thumbnail_rect = self._calculate_thumbnail_rect(content_rect)

        # Calculate text area (bottom part)
        text_rect = self._calculate_text_rect(content_rect, thumbnail_rect)

        # Draw thumbnail
        self._draw_thumbnail(painter, thumbnail_rect, asset_data, is_selected)

        # Draw asset name
        self._draw_text(painter, text_rect, asset_data, is_selected)

        painter.restore()

    def sizeHint(self, option, index):
        """Return the size hint for an item.

        Args:
            option (QStyleOptionViewItem): Style options
            index (QModelIndex): Model index

        Returns:
            QSize: Size hint for the item
        """
        return self._item_size

    def setScale(self, scale_factor):
        """Set the scale factor for responsive sizing.

        Args:
            scale_factor (float): Scale factor (0.5 to 2.0)
        """
        # Clamp scale factor
        self._scale_factor = max(0.5, min(2.0, scale_factor))

        # Update cached dimensions
        base_width = ASSET_ITEM_WIDTH
        base_height = ASSET_ITEM_HEIGHT
        base_thumbnail = ASSET_THUMBNAIL_SIZE

        self._item_size = QtCore.QSize(
            int(base_width * self._scale_factor),
            int(base_height * self._scale_factor),
        )
        self._thumbnail_size = int(base_thumbnail * self._scale_factor)

        self._logger.debug(f"Scale set to {self._scale_factor}x")

    def getScale(self):
        """Get the current scale factor.

        Returns:
            float: Current scale factor
        """
        return self._scale_factor

    def _draw_background(self, painter, rect, is_selected, is_hovered):
        """Draw the background and border for the item.

        Args:
            painter (QPainter): Painter object
            rect (QRect): Item rectangle
            is_selected (bool): Whether item is selected
            is_hovered (bool): Whether item is hovered
        """
        # Determine colors based on state
        if is_selected:
            if is_hovered:
                bg_color = QtGui.QColor(COLOR_BG_SELECTED_HOVER)
            else:
                bg_color = QtGui.QColor(COLOR_BG_SELECTED)
            border_color = QtGui.QColor(COLOR_ITEM_SELECTED)
            border_width = 5
        else:
            if is_hovered:
                bg_color = QtGui.QColor(COLOR_BG_HOVER)
                border_color = QtGui.QColor(COLOR_BORDER_DEFAULT)
                border_width = 2
            else:
                bg_color = QtGui.QColor(COLOR_BG_DARKER)
                border_color = QtGui.QColor(COLOR_BG_DARKER)
                border_width = 2

        # Draw background
        painter.setBrush(QtGui.QBrush(bg_color))
        painter.setPen(QtGui.QPen(border_color, border_width))

        # Draw rounded rectangle
        painter.drawRoundedRect(
            rect.adjusted(
                border_width // 2,
                border_width // 2,
                -border_width // 2,
                -border_width // 2,
            ),
            BORDER_RADIUS_LARGE,
            BORDER_RADIUS_LARGE,
        )

    def _calculate_thumbnail_rect(self, content_rect):
        """Calculate the rectangle for the thumbnail.

        Args:
            content_rect (QRect): Content area rectangle

        Returns:
            QRect: Thumbnail rectangle
        """
        # Center thumbnail horizontally, place at top
        thumb_x = content_rect.x() + (content_rect.width() - self._thumbnail_size) // 2
        thumb_y = content_rect.y()

        return QtCore.QRect(
            thumb_x,
            thumb_y,
            self._thumbnail_size,
            self._thumbnail_size,
        )

    def _calculate_text_rect(self, content_rect, thumbnail_rect):
        """Calculate the rectangle for the text.

        Args:
            content_rect (QRect): Content area rectangle
            thumbnail_rect (QRect): Thumbnail rectangle

        Returns:
            QRect: Text rectangle
        """
        text_y = thumbnail_rect.bottom() + LAYOUT_SPACING_MEDIUM
        text_height = content_rect.bottom() - text_y

        return QtCore.QRect(content_rect.x(), text_y, content_rect.width(), text_height)

    def _draw_thumbnail(self, painter, rect, asset_data, is_selected):
        """Draw the thumbnail image.

        Args:
            painter (QPainter): Painter object
            rect (QRect): Thumbnail rectangle
            asset_data (dict): Asset data
            is_selected (bool): Whether item is selected
        """
        # Get thumbnail path
        thumbnail_path = asset_data.get("thumbnail")

        # Load thumbnail pixmap
        pixmap = self._load_thumbnail(thumbnail_path)

        if pixmap and not pixmap.isNull():
            # Scale pixmap to fit rectangle while maintaining aspect ratio
            scaled_pixmap = pixmap.scaled(
                rect.size(),
                QtCore.Qt.KeepAspectRatio,
                QtCore.Qt.SmoothTransformation,
            )

            # Center the scaled pixmap in the rectangle
            x = rect.x() + (rect.width() - scaled_pixmap.width()) // 2
            y = rect.y() + (rect.height() - scaled_pixmap.height()) // 2

            painter.drawPixmap(x, y, scaled_pixmap)
        else:
            # Draw placeholder if no thumbnail
            self._draw_thumbnail_placeholder(painter, rect, is_selected)

    def _draw_thumbnail_placeholder(self, painter, rect, is_selected):
        """Draw a placeholder when thumbnail is not available.

        Args:
            painter (QPainter): Painter object
            rect (QRect): Thumbnail rectangle
            is_selected (bool): Whether item is selected
        """
        # Determine colors based on selection state
        if is_selected:
            bg_color = QtGui.QColor(COLOR_ITEM_SELECTED)
            border_color = QtGui.QColor(COLOR_ITEM_SELECTED)
        else:
            bg_color = QtGui.QColor(COLOR_BG_DARKER)
            border_color = QtGui.QColor(COLOR_BG_DARKER)

        # Draw placeholder background
        painter.setBrush(QtGui.QBrush(bg_color))
        painter.setPen(QtGui.QPen(border_color, 1))
        painter.drawRoundedRect(rect, BORDER_RADIUS_MEDIUM, BORDER_RADIUS_MEDIUM)

        # Draw placeholder text
        painter.setPen(QtGui.QColor("#FFFFFF"))
        font = painter.font()
        font.setPointSize(8)
        painter.setFont(font)
        painter.drawText(rect, QtCore.Qt.AlignCenter, "Thumbnail")

    def _draw_text(self, painter, rect, asset_data, is_selected):
        """Draw the asset name text.

        Args:
            painter (QPainter): Painter object
            rect (QRect): Text rectangle
            asset_data (dict): Asset data
            is_selected (bool): Whether item is selected
        """
        # Get asset name
        asset_name = asset_data.get("name", "Unnamed Asset")

        # Set text color based on selection state
        if is_selected:
            text_color = QtGui.QColor(COLOR_ITEM_SELECTED)
        else:
            text_color = QtGui.QColor("#FFFFFF")

        painter.setPen(text_color)

        # Set font
        font = painter.font()
        # Parse font size - handle both "px" and "pt" units
        font_size_str = FONT_SIZE_NORMAL
        if font_size_str.endswith("pt"):
            font_size = int(font_size_str.replace("pt", ""))
        elif font_size_str.endswith("px"):
            font_size = int(font_size_str.replace("px", ""))
        else:
            # Fallback to default size if no unit specified
            try:
                font_size = int(font_size_str)
            except ValueError:
                font_size = 9  # Default fallback size

        font.setPointSize(font_size)
        if is_selected:
            font.setBold(True)
        painter.setFont(font)

        # Draw text with ellipsis if needed
        metrics = QtGui.QFontMetrics(font)
        elided_text = metrics.elidedText(asset_name, QtCore.Qt.ElideRight, rect.width())

        painter.drawText(rect, QtCore.Qt.AlignCenter | QtCore.Qt.AlignTop, elided_text)

    def _load_thumbnail(self, thumbnail_path):
        """Load thumbnail from path with caching.

        Args:
            thumbnail_path (str): Path to thumbnail image

        Returns:
            QPixmap: Loaded pixmap or None if failed
        """
        if not thumbnail_path:
            return None

        # Check cache first
        cache_key = f"{thumbnail_path}_{self._thumbnail_size}"
        if cache_key in self._thumbnail_cache:
            return self._thumbnail_cache[cache_key]

        # Load from file
        if os.path.exists(thumbnail_path):
            pixmap = QtGui.QPixmap(thumbnail_path)
            if not pixmap.isNull():
                # Cache the loaded pixmap
                self._thumbnail_cache[cache_key] = pixmap
                return pixmap

        return None

    def clearThumbnailCache(self):
        """Clear the thumbnail cache to free memory."""
        self._thumbnail_cache.clear()
        self._logger.debug("Thumbnail cache cleared")

    def editorEvent(self, event, model, option, index):
        """Handle editor events for interaction.

        Args:
            event (QEvent): The event to handle
            model (QAbstractItemModel): The model
            option (QStyleOptionViewItem): Style options
            index (QModelIndex): Model index

        Returns:
            bool: True if event was handled
        """
        # Handle mouse events for hover effects and interactions
        if event.type() == QtCore.QEvent.MouseButtonPress:
            return self._handle_mouse_press(event, model, option, index)
        elif event.type() == QtCore.QEvent.MouseButtonRelease:
            return self._handle_mouse_release(event, model, option, index)
        elif event.type() == QtCore.QEvent.MouseMove:
            return self._handle_mouse_move(event, model, option, index)
        elif event.type() == QtCore.QEvent.MouseButtonDblClick:
            return self._handle_mouse_double_click(event, model, option, index)

        # Let the base class handle other events
        return super().editorEvent(event, model, option, index)

    def _handle_mouse_press(self, event, model, option, index):
        """Handle mouse press events.

        Args:
            event (QMouseEvent): Mouse event
            model (QAbstractItemModel): The model
            option (QStyleOptionViewItem): Style options
            index (QModelIndex): Model index

        Returns:
            bool: True if event was handled
        """
        if event.button() == QtCore.Qt.LeftButton:
            # Store press position for potential drag operation
            self._press_position = event.pos()
            self._logger.debug(f"Mouse press at {event.pos()} on item {index.row()}")
            return True

        return False

    def _handle_mouse_release(self, event, model, option, index):
        """Handle mouse release events.

        Args:
            event (QMouseEvent): Mouse event
            model (QAbstractItemModel): The model
            option (QStyleOptionViewItem): Style options
            index (QModelIndex): Model index

        Returns:
            bool: True if event was handled
        """
        if event.button() == QtCore.Qt.LeftButton:
            # Clear press position
            self._press_position = None
            self._logger.debug(f"Mouse release at {event.pos()} on item {index.row()}")
            return True

        return False

    def _handle_mouse_move(self, event, model, option, index):
        """Handle mouse move events for hover effects.

        Args:
            event (QMouseEvent): Mouse event
            model (QAbstractItemModel): The model
            option (QStyleOptionViewItem): Style options
            index (QModelIndex): Model index

        Returns:
            bool: True if event was handled
        """
        # Update hover state - this will trigger a repaint
        # The actual hover state is handled by the view's mouse tracking
        return False  # Let the view handle hover state

    def _handle_mouse_double_click(self, event, model, option, index):
        """Handle mouse double-click events.

        Args:
            event (QMouseEvent): Mouse event
            model (QAbstractItemModel): The model
            option (QStyleOptionViewItem): Style options
            index (QModelIndex): Model index

        Returns:
            bool: True if event was handled
        """
        if event.button() == QtCore.Qt.LeftButton:
            # Get asset data and emit signal through the view
            from .asset_list_model import AssetListModel

            asset_data = index.data(AssetListModel.AssetDataRole)
            if asset_data:
                # Find the view and emit double-click signal
                view = self.parent()
                if hasattr(view, "assetDoubleClicked"):
                    view.assetDoubleClicked.emit(asset_data)
                    self._logger.debug(
                        f"Double-click on asset: {asset_data.get('name', 'Unknown')}",
                    )
            return True

        return False
