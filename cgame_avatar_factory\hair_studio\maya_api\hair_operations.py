# cgame_avatar_factory\hair_studio\maya_api\hair_operations.py

# Import built-in modules
import logging

# Import Maya commands using unified import system
from .utils import get_maya_cmds

# Get Maya commands (real or mock)
cmds = get_maya_cmds(__name__)

# Import local modules
from cgame_avatar_factory.hair_studio.constants import HAIR_MATCH_HEAD_NAME
from cgame_avatar_factory.hair_studio.maya_api.asset_path_resolver import get_texture_path_for_hair_asset
from cgame_avatar_factory.hair_studio.maya_api.load_maya_asset import WrapHairMesh
from cgame_avatar_factory.hair_studio.maya_api.maya_node_util import delete_maya_node, check_node_exists, select_maya_node


def create_hair_node(asset_data):
    """
    Creates a hair node in Maya based on asset data.

    For development/testing: Only logs the operation, always returns success.

    Args:
        asset_data (dict): A dictionary containing information about the asset.
                           e.g., {'name': 'Hair Card 1', 'asset_type': 'card'}

    Returns:
        dict: A result dictionary, e.g., {'success': True, 'node_names': {'hair_asset': 'hair_card_1_grp', 'ref_head': 'head_hair_card_1_grp'}}
    """
    logger = logging.getLogger(__name__)
    node_name = "{}_grp".format(asset_data.get("name", "new_hair").replace(" ", "_"))
    node_name = node_name.replace("-", "_")
    failed_info = {"success": False, "node_names": {"hair_asset": node_name, "ref_head": None}}

    # Log the operation for development/testing
    logger.info("Maya API: create_hair_node called with asset_data: %s", asset_data)

    # logger.info("Maya API: Would create node: %s", node_name)
    meta_data = asset_data.get("metadata", None)
    if not meta_data:
        logger.warning("Asset metadata is empty: %s", asset_data)
        return failed_info

    # NOTE: find reference head mesh, mesh path
    head_path = meta_data.get(
        "reference",
        None,
    )
    fbx_path = meta_data.get("file_path", None)
    # Read sub_asset_type from asset_data top level, not from metadata
    sub_asset_type = asset_data.get("sub_asset_type", None)
    logger.info("sub_asset_type=%s", sub_asset_type)

    logger.info("head_path=%s, fbx_path=%s", head_path, fbx_path)

    if not head_path or not fbx_path:
        logger.warning(
            "Asset path is empty: head_path=%s, fbx_path=%s",
            head_path,
            fbx_path,
        )
        return failed_info

    # get texture path
    texture_path = get_texture_path_for_hair_asset(fbx_path)
    _hair_data = None

    _hair_data = WrapHairMesh(
        HAIR_MATCH_HEAD_NAME,
        fbx_path,
        node_name,
        head_path,
        texture_path,
        sub_asset_type,
    )

    if not _hair_data:
        logger.error(f"Failed to load hair asset: {node_name}")
        return failed_info

    logger.info(
        f"Loaded hair asset '{node_name}': {fbx_path}, imported_objects: {_hair_data}",
    )

    # Create node_names dictionary with both hair_asset and ref_head
    node_names = {
        "hair_asset": _hair_data.hair_target,
        "ref_head": _hair_data.hair_head_mesh,
    }

    logger.info(f"Created hair nodes: {node_names}")
    sussceed_info = {"success": True, "node_names": node_names}
    return sussceed_info


def delete_node(node_names_dict):
    """
    Deletes nodes from the Maya scene.

    For development/testing: Only logs the operation, always returns success.

    Args:
        node_names_dict (dict): Dictionary containing node names (e.g., {'hair_asset': 'name1', 'ref_head': 'name2'}).

    Returns:
        dict: A result dictionary.
    """
    logger = logging.getLogger(__name__)

    # Validate input type
    if not isinstance(node_names_dict, dict):
        error_msg = f"Expected dict input, got {type(node_names_dict)}"
        logger.error("Maya API: %s", error_msg)
        return {"success": False, "error": error_msg}

    # Extract all node names from dictionary values
    node_names = [name for name in node_names_dict.values() if name is not None]
    logger.info("Maya API: delete_node called with node_names dict: %s", node_names_dict)

    success_count = 0
    total_count = len(node_names)

    for node_name in node_names:
        # For development: Try to delete if Maya is available, but don't fail if not
        if cmds.objExists(node_name):
            is_success = delete_maya_node(node_name)
            if not is_success:
                logger.error("Maya API: Failed to delete node: %s", node_name)
                continue
            success_count += 1
        else:
            logger.warning("Maya API: Node not found for deletion: %s", node_name)
        
    # Return success if at least one node was deleted successfully
    success = success_count > 0
    logger.info("Maya API: Deleted %d out of %d nodes", success_count, total_count)
    return {"success": success}


def set_node_visibility(node_names_dict, is_visible):
    """
    Sets the visibility of a node in the Maya scene.

    For development/testing: Only logs the operation, always returns success.

    Args:
        node_names_dict (dict): Dictionary containing node names. Will operate on the 'hair_asset' key.
        is_visible (bool): The visibility state to set.

    Returns:
        dict: A result dictionary.
    """
    logger = logging.getLogger(__name__)

    # Validate input type
    if not isinstance(node_names_dict, dict):
        error_msg = f"Expected dict input, got {type(node_names_dict)}"
        logger.error("Maya API: %s", error_msg)
        return {"success": False, "error": error_msg}

    # Use hair_asset node for visibility operations
    node_name = node_names_dict.get("hair_asset")
    if not node_name:
        error_msg = "No 'hair_asset' key found in node_names dict"
        logger.error("Maya API: %s: %s", error_msg, node_names_dict)
        return {"success": False, "error": error_msg}

    logger.debug(
        "Maya API: set_node_visibility called with hair_asset: %s, is_visible: %s",
        node_name,
        is_visible,
    )

    try:
        # For development: Try to set visibility if Maya is available, but don't fail if not
        if cmds.objExists(node_name):
            cmds.setAttr("{}.visibility".format(node_name), is_visible)
            logger.debug(
                "Maya API: Successfully set visibility for %s to %s",
                node_name,
                is_visible,
            )
        else:
            logger.info("Maya API: Node not found for visibility change: %s", node_name)
        return {"success": True}
    except Exception as e:
        # For development: Log the attempt but still return success
        logger.warning(
            "Maya API: Maya not available or failed, but continuing: %s",
            str(e),
        )
        return {"success": True}


def check_component_nodes_exist(node_names_dict):
    """
    Check if all nodes in a component exist in the Maya scene.

    Args:
        node_names_dict (dict): Dictionary containing node names (e.g., {'hair_asset': 'name1', 'ref_head': 'name2'}).

    Returns:
        dict: A result dictionary with 'success' and 'missing_nodes' keys.
    """
    logger = logging.getLogger(__name__)

    # Validate input type
    if not isinstance(node_names_dict, dict):
        error_msg = f"Expected dict input, got {type(node_names_dict)}"
        logger.error("Maya API: %s", error_msg)
        return {"success": False, "error": error_msg}

    # Extract all node names from dictionary values
    node_names = [name for name in node_names_dict.values() if name is not None]
    logger.debug("Maya API: check_component_nodes_exist called with node_names dict: %s", node_names_dict)

    missing_nodes = []
    for node_name in node_names:
        if not check_node_exists(node_name):
            missing_nodes.append(node_name)
            logger.warning("Maya API: Node not found: %s", node_name)

    if missing_nodes:
        logger.warning("Maya API: Missing nodes: %s", missing_nodes)
        return {"success": False, "missing_nodes": missing_nodes}
    else:
        logger.debug("Maya API: All nodes exist")
        return {"success": True}


def select_component_nodes(node_names_dict):
    """
    Select all nodes in a component in the Maya scene.

    Args:
        node_names_dict (dict): Dictionary containing node names (e.g., {'hair_asset': 'name1', 'ref_head': 'name2'}).

    Returns:
        dict: A result dictionary.
    """
    logger = logging.getLogger(__name__)

    # Validate input type
    if not isinstance(node_names_dict, dict):
        error_msg = f"Expected dict input, got {type(node_names_dict)}"
        logger.error("Maya API: %s", error_msg)
        return {"success": False, "error": error_msg}

    # Extract all node names from dictionary values
    node_names = [name for name in node_names_dict.values() if name is not None]
    logger.debug("Maya API: select_component_nodes called with node_names dict: %s", node_names_dict)

    # First check if all nodes exist
    check_result = check_component_nodes_exist(node_names_dict)
    if not check_result.get("success"):
        return check_result

    # Select the primary node (hair_asset if available, otherwise first node)
    primary_node = node_names_dict.get("hair_asset") or node_names[0] if node_names else None

    if primary_node:
        success = select_maya_node(primary_node)
        if success:
            logger.info("Maya API: Successfully selected component node: %s", primary_node)
            return {"success": True, "selected_node": primary_node}
        else:
            error_msg = f"Failed to select node: {primary_node}"
            logger.error("Maya API: %s", error_msg)
            return {"success": False, "error": error_msg}
    else:
        error_msg = "No valid nodes to select"
        logger.error("Maya API: %s", error_msg)
        return {"success": False, "error": error_msg}
