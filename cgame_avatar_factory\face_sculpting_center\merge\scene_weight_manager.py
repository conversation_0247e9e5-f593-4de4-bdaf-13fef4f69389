# Import built-in modules
import logging
import random

# Import third-party modules
import maya.cmds as cmds

# Import local modules
import cgame_avatar_factory.common.constants as const


class SceneWeightManager:
    """Manages scene data and weight updates for merge operations

    Handles updating and tracking weights, ranges, and merge values
    for different areas in the scene. Controls attribute values and
    provides random merge functionality.

    Implemented as a singleton to ensure consistent state across the application.
    """

    _instance = None

    def __new__(cls):
        """Ensure only one instance exists (singleton pattern)"""
        if cls._instance is None:
            cls._instance = super(SceneWeightManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self) -> None:
        """Initialize scene data manager (only once)"""
        if self._initialized:
            return
        self.mix_type = const.MIX_TYPE[0]
        self.range = []
        self.random_merge_values = 0.0
        self._initialized = True

    def return_range(self):
        """Get current range

        Returns:
            list: Current range list
        """
        return self.range

    def update_weights(self, weights):
        """Update weights and apply to scene

        Args:
            weights: New weight values to apply
        """
        self.call_scene(weights)

    def update_mix_type(self, mix_type):
        """Update current mix type

        Args:
            max_type: New mix type value
        """
        self.mix_type = mix_type

    def update_range(self, range):
        """Update current range

        Args:
            range: New range value
        """
        self.range = range

    def random_set_merge(self):
        """Apply random merge values to all ranges except 'all'"""
        for range in self.return_range():
            p1 = 1 - self.random_merge_values
            p2 = random.uniform(0, self.random_merge_values)
            p3 = self.random_merge_values - p2

            random_values = [p2, p3, 0.0]
            random.shuffle(random_values)
            weight = [p1] + random_values

            self.set_attr_values(range, weight)

            self._apply_mirror_weight(range, weight)

    def _apply_mirror_weight(self, range_name, weight):
        """应用镜像权重到对称区域

        Args:
            range_name: 当前区域名称
            weight: 要应用的权重值
        """
        mirror_range = self._get_mirror_range(range_name)
        if mirror_range:
            self.set_attr_values(mirror_range, weight)

    def _get_mirror_range(self, range_name):
        """获取镜像区域名称

        Args:
            range_name: 当前区域名称

        Returns:
            str or None: 镜像区域名称，如果不是对称区域则返回None
        """
        if "_l" in range_name:
            return range_name.replace("_l", "_r")
        elif "_r" in range_name:
            return range_name.replace("_r", "_l")
        return None

    def set_random_merge_values(self, values):
        """Set random merge value

        Args:
            values: New random merge value
        """
        self.random_merge_values = values

    def return_random_merge_values(self):
        """Get current random merge value

        Returns:
            float: Current random merge value
        """
        return self.random_merge_values

    def call_scene(self, weights):
        """Apply current weights to scene attributes"""
        if self.range:
            if "all" in self.range:
                for _, range_index in const.PINCH_CTRL_ATTR.items():
                    self.set_attr_values(range_index, weights)
            else:
                for range in self.range:
                    if range in const.MOCK_MIRROR_AREA_CONFIG:
                        for _range in const.MOCK_MIRROR_AREA_CONFIG[range]:
                            self.set_attr_values(_range, weights)
                    else:
                        self.set_attr_values(range, weights)

    def reset_merge(self, target_range=None):
        """Reset merge values to defaults for specified range

        Args:
            target_range: Range to reset, if None uses self.range
        """
        if target_range is None:
            target_range = self.range
        default_weights = [1.0, 0.0, 0.0, 0.0]
        for range in target_range:
            if range in const.MOCK_MIRROR_AREA_CONFIG:
                for _range in const.MOCK_MIRROR_AREA_CONFIG[range]:
                    self.set_attr_values(_range, default_weights)
            self.set_attr_values(range, default_weights)

    def set_attr_values(self, range, weights):
        """Set attribute values for a range

        Args:
            range: Range to set values for
            weights: Weight values to apply
        """
        if self.mix_type == const.MIX_TYPE[0]:
            self.set_attribute_values(range, weights, const.FACE_MIX_SUFFIX)
            self.set_attribute_values(range, weights, const.FACE_ALIGN_SUFFIX)
        elif self.mix_type == const.MIX_TYPE[1]:
            self.set_attribute_values(range, weights, const.FACE_MIX_SUFFIX)
        elif self.mix_type == const.MIX_TYPE[2]:
            self.set_attribute_values(range, weights, const.FACE_ALIGN_SUFFIX)

    def set_attribute_values(self, range, weights, suffix):
        """Set attribute values for a range

        Args:
            range: Range to set values for
            weights: Weight values to apply
        """
        attr_name = f"{const.MIX_CTRL_NAME}.{range}"
        cmds.setAttr(f"{attr_name}_{suffix[0]}", weights[1])
        cmds.setAttr(f"{attr_name}_{suffix[1]}", weights[2])
        cmds.setAttr(f"{attr_name}_{suffix[2]}", weights[3])

    def get_weights_from_scene(self):
        """
        Get all area weights from Maya scene attributes and return a dic.

        For each area in const.MOCK_AREAS_NAME, this method reads the corresponding
        attributes from the Maya scene, reconstructs the weights list, and return.
        The first weight is calculated as 1 - w1 - w2 - w3,
        assuming the total sum is 1.

        Returns:
            dict: The updated weights for each area.
        """
        result = {}
        for range in const.MOCK_AREAS_NAME:
            attr_name = f"{const.MIX_CTRL_NAME}.{range}"

            if self.mix_type == const.MIX_TYPE[2]:
                suffix = const.FACE_ALIGN_SUFFIX
            else:
                suffix = const.FACE_MIX_SUFFIX

            try:
                w1 = cmds.getAttr(f"{attr_name}_{suffix[0]}")
                w2 = cmds.getAttr(f"{attr_name}_{suffix[1]}")
                w3 = cmds.getAttr(f"{attr_name}_{suffix[2]}")
            except Exception as e:
                logging.info(f"Failed to restore weights for {range}: {e}")
                continue

            w0 = 1.0 - w1 - w2 - w3
            weights = [w0, w1, w2, w3]
            result[range] = weights

        return result
