#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""Test for select_component Maya node checking functionality."""

import unittest
from unittest.mock import Mock, patch, MagicMock
import logging

# Import the modules we want to test
from cgame_avatar_factory.hair_studio.manager.hair_manager import Hair<PERSON>anager
from cgame_avatar_factory.hair_studio.data.models import HairComponent


class TestSelectComponentMayaCheck(unittest.TestCase):
    """Test cases for select_component Maya node checking."""

    def setUp(self):
        """Set up test fixtures."""
        # Mock dependencies
        self.mock_data_manager = Mock()
        self.mock_maya_api = Mock()
        
        # Create hair manager with mocked dependencies
        with patch('cgame_avatar_factory.hair_studio.manager.hair_manager.DataManager'):
            with patch('cgame_avatar_factory.hair_studio.manager.hair_manager.maya_api_instance'):
                self.hair_manager = HairManager()
                self.hair_manager._data_manager = self.mock_data_manager
                self.hair_manager._maya_api = self.mock_maya_api
                
        # Create a test component
        self.test_component = HairComponent(
            id="test_component_id",
            name="Test Hair Component",
            asset_id="test_asset_id",
            component_type="card",
            node_names={"hair_asset": "test_hair_node", "ref_head": "test_head_node"}
        )
        
        # Add component to manager's internal storage
        self.hair_manager._components[self.test_component.id] = self.test_component

    def test_select_component_nodes_exist_and_select_success(self):
        """Test selecting component when Maya nodes exist and selection succeeds."""
        # Mock Maya API responses
        self.mock_maya_api.check_component_exists.return_value = {"success": True}
        self.mock_maya_api.select_component.return_value = {
            "success": True, 
            "selected_node": "test_hair_node"
        }
        
        # Mock the signal
        self.hair_manager.component_selected = Mock()
        
        # Call select_component
        self.hair_manager.select_component(self.test_component.id)
        
        # Verify Maya API calls
        self.mock_maya_api.check_component_exists.assert_called_once_with({
            "node_names": {"hair_asset": "test_hair_node", "ref_head": "test_head_node"}
        })
        self.mock_maya_api.select_component.assert_called_once_with({
            "node_names": {"hair_asset": "test_hair_node", "ref_head": "test_head_node"}
        })
        
        # Verify component is selected
        self.assertEqual(self.hair_manager._selected_component_id, self.test_component.id)
        self.hair_manager.component_selected.emit.assert_called_once()

    def test_select_component_nodes_exist_but_select_fails(self):
        """Test selecting component when Maya nodes exist but selection fails."""
        # Mock Maya API responses
        self.mock_maya_api.check_component_exists.return_value = {"success": True}
        self.mock_maya_api.select_component.return_value = {
            "success": False, 
            "error": "Selection failed"
        }
        
        # Mock the signal
        self.hair_manager.component_selected = Mock()
        
        # Call select_component
        self.hair_manager.select_component(self.test_component.id)
        
        # Verify Maya API calls
        self.mock_maya_api.check_component_exists.assert_called_once()
        self.mock_maya_api.select_component.assert_called_once()
        
        # Verify component is still selected (selection failure doesn't prevent component selection)
        self.assertEqual(self.hair_manager._selected_component_id, self.test_component.id)
        self.hair_manager.component_selected.emit.assert_called_once()

    @patch('qtpy.QtWidgets.QMessageBox.information')
    def test_select_component_nodes_missing(self, mock_message_box):
        """Test selecting component when Maya nodes are missing."""
        # Mock Maya API responses
        self.mock_maya_api.check_component_exists.return_value = {
            "success": False,
            "missing_nodes": ["test_hair_node"]
        }

        # Mock the signals
        self.hair_manager.components_updated = Mock()
        self.hair_manager.component_selected = Mock()

        # Set the component as selected initially (to test selection clearing)
        self.hair_manager._selected_component_id = self.test_component.id

        # Call select_component
        self.hair_manager.select_component(self.test_component.id)

        # Verify Maya API calls
        self.mock_maya_api.check_component_exists.assert_called_once()
        self.mock_maya_api.select_component.assert_not_called()

        # Verify dialog was shown
        mock_message_box.assert_called_once()

        # Verify component was removed from internal storage
        self.assertNotIn(self.test_component.id, self.hair_manager._components)

        # Verify signals were emitted
        self.hair_manager.components_updated.emit.assert_called_once()
        self.hair_manager.component_selected.emit.assert_called_once_with(None)

        # Verify selection was cleared
        self.assertIsNone(self.hair_manager._selected_component_id)

    def test_safe_remove_component_success(self):
        """Test safe component removal when component exists."""
        # Mock the signals
        self.hair_manager.components_updated = Mock()
        self.hair_manager.component_selected = Mock()

        # Set the component as selected
        self.hair_manager._selected_component_id = self.test_component.id

        # Call safe remove
        result = self.hair_manager._safe_remove_component(self.test_component.id, "Test removal")

        # Verify removal was successful
        self.assertTrue(result)
        self.assertNotIn(self.test_component.id, self.hair_manager._components)

        # Verify signals were emitted
        self.hair_manager.components_updated.emit.assert_called_once()
        self.hair_manager.component_selected.emit.assert_called_once_with(None)

        # Verify selection was cleared
        self.assertIsNone(self.hair_manager._selected_component_id)

    def test_safe_remove_component_not_found(self):
        """Test safe component removal when component doesn't exist."""
        # Mock the signals
        self.hair_manager.components_updated = Mock()
        self.hair_manager.component_selected = Mock()

        # Call safe remove with non-existent ID
        result = self.hair_manager._safe_remove_component("non_existent_id", "Test removal")

        # Verify removal failed gracefully
        self.assertFalse(result)

        # Verify no signals were emitted
        self.hair_manager.components_updated.emit.assert_not_called()
        self.hair_manager.component_selected.emit.assert_not_called()

    def test_safe_remove_component_not_selected(self):
        """Test safe component removal when component is not selected."""
        # Mock the signals
        self.hair_manager.components_updated = Mock()
        self.hair_manager.component_selected = Mock()

        # Ensure no component is selected
        self.hair_manager._selected_component_id = None

        # Call safe remove
        result = self.hair_manager._safe_remove_component(self.test_component.id, "Test removal")

        # Verify removal was successful
        self.assertTrue(result)
        self.assertNotIn(self.test_component.id, self.hair_manager._components)

        # Verify components_updated was emitted but not component_selected
        self.hair_manager.components_updated.emit.assert_called_once()
        self.hair_manager.component_selected.emit.assert_not_called()

        # Verify selection remains None
        self.assertIsNone(self.hair_manager._selected_component_id)

    def test_select_component_no_node_names(self):
        """Test selecting component with no node_names."""
        # Create component without node_names (use empty dict instead of None)
        component_no_nodes = HairComponent(
            id="test_no_nodes_id",
            name="Test No Nodes Component",
            asset_id="test_asset_id",
            component_type="card",
            node_names={}
        )
        
        self.hair_manager._components[component_no_nodes.id] = component_no_nodes
        
        # Mock the signal
        self.hair_manager.component_selected = Mock()
        
        # Call select_component
        self.hair_manager.select_component(component_no_nodes.id)
        
        # Verify Maya API was not called
        self.mock_maya_api.check_component_exists.assert_not_called()
        self.mock_maya_api.select_component.assert_not_called()
        
        # Verify component is selected normally
        self.assertEqual(self.hair_manager._selected_component_id, component_no_nodes.id)
        self.hair_manager.component_selected.emit.assert_called_once()

    def test_select_component_clear_selection(self):
        """Test clearing component selection."""
        # Mock the signal
        self.hair_manager.component_selected = Mock()
        
        # Call select_component with None
        self.hair_manager.select_component(None)
        
        # Verify Maya API was not called
        self.mock_maya_api.check_component_exists.assert_not_called()
        self.mock_maya_api.select_component.assert_not_called()
        
        # Verify selection was cleared
        self.assertIsNone(self.hair_manager._selected_component_id)
        self.hair_manager.component_selected.emit.assert_called_once_with(None)


if __name__ == '__main__':
    # Set up logging for tests
    logging.basicConfig(level=logging.DEBUG)
    unittest.main()
