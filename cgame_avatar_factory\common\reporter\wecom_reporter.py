# Import built-in modules
import logging
import os
from typing import Any
from typing import Dict
from typing import Optional
from typing import Union

# Import third-party modules
from lightbox_paths.paths import get_user_name
import requests
from requests.exceptions import RequestException

# Import local modules
from cgame_avatar_factory.common import constants as const
from cgame_avatar_factory.common.utils.utils import read_json


class WecomReporter(object):
    """A singleton reporter for sending messages to WeChat Work (WeCom).

    This class provides a unified interface for sending notifications to WeChat Work
    using webhook API. It implements singleton pattern to ensure consistent configuration
    across the application.

    Example:
        >>> reporter = WecomReporter.instance()
        >>> reporter.send_message("Hello World!")
        >>> reporter.report_task("Build Complete", {"status": "success"})
    """

    # API Configuration
    WECOM_URL_TEMPLATE = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={bot_id}"
    DEFAULT_BOT_ID = "90883b48-5522-4746-9c25-c99aed658452"

    # Message Configuration
    MESSAGE_HEADER = "**Avatar Factory** 🎉"
    MESSAGE_FOOTER = "\n😊"
    REQUEST_TIMEOUT = 30

    # Singleton implementation
    _instance: Optional["WecomReporter"] = None
    _initialized: bool = False

    def __new__(cls) -> "WecomReporter":
        """Ensure only one instance exists (Singleton pattern).

        Returns:
            WecomReporter: The singleton instance.
        """
        if cls._instance is None:
            cls._instance = super(WecomReporter, cls).__new__(cls)
        return cls._instance

    def __init__(self) -> None:
        """Initialize WecomReporter object.

        Note:
            Due to singleton pattern, initialization only occurs once.
        """
        # Only initialize once
        if not self._initialized:
            self.logger = logging.getLogger(__name__)
            self.wecom_url: str = self.WECOM_URL_TEMPLATE
            self.bot_id: str = self._get_bot_id()
            self.user_name: str = get_user_name()
            self.project_abbr = os.getenv("THM_PROJECT_ABBR", "dev")
            self.task_name: str = ""
            self.task_info: str = ""
            WecomReporter._initialized = True

    @classmethod
    def instance(cls) -> "WecomReporter":
        """Get the singleton instance of WecomReporter.

        Returns:
            WecomReporter: The singleton instance.
        """
        return cls()

    def _get_bot_id(self) -> str:
        """Get bot ID from JSON config file with fallback to default.

        Returns:
            str: Bot ID from JSON config file or default value.
        """
        try:
            config_data = read_json(const.REPORTER_CONFIG_JSON_PATH)
            return config_data.get("wecom_bot_id") or self.DEFAULT_BOT_ID
        except Exception:
            return self.DEFAULT_BOT_ID

    @property
    def wecom_api_url(self) -> str:
        """Returns the wecom API URL with the bot ID.

        Returns:
            str: The complete WeChat Work API URL.
        """
        return self.wecom_url.format(bot_id=self.bot_id)

    def set_task_name(self, task_name: str) -> None:
        """Set the task name.

        Args:
            task_name: The name of the task.
        """
        self.task_name = task_name

    def set_task_info(self, task_info: Union[str, Dict]) -> None:
        """Set the task info.

        Args:
            task_info: Additional information about the task.
        """
        self.task_info = task_info

    def _format_dict_message(self, message: Dict) -> str:
        """Format dictionary message to markdown.

        Args:
            message: Dictionary containing message data.

        Returns:
            str: Formatted markdown string.
        """
        content = f"{self.MESSAGE_HEADER}\n\n"
        for key, value in message.items():
            content += self._format_key_value(key, value, indent_level=0)
        content += self.MESSAGE_FOOTER
        return content

    def _format_key_value(self, key: str, value, indent_level: int = 0) -> str:
        """Format a key-value pair with proper indentation.

        Args:
            key: The key to format
            value: The value to format (can be dict, list, or any other type)
            indent_level: Current indentation level

        Returns:
            str: Formatted key-value string
        """
        indent = "  " * indent_level

        if isinstance(value, dict):
            result = f"{indent}- {key}:\n"
            for sub_key, sub_value in value.items():
                result += self._format_key_value(sub_key, sub_value, indent_level + 1)
            return result
        elif isinstance(value, list):
            result = f"{indent}- {key}:\n"
            for item in value:
                result += f"{indent}  - {item}\n"
            return result
        else:
            return f"{indent}- {key}: {value}\n"

    def send_message(self, message: Union[str, Dict]) -> str:
        """Send a message using Wecom API.

        Args:
            message: Message to be sent. Can be string or dictionary.

        Returns:
            Response text from Wecom API.

        Raises:
            RequestException: If the HTTP request fails.
        """
        # Check if running in pytest test environment
        if os.getenv("PYTEST_CURRENT_TEST"):
            self.logger.debug("WeChat Work reporting is disabled in test environment")
            return "WeChat Work reporting disabled"

        # Format message based on type
        if isinstance(message, dict):
            content = self._format_dict_message(message)
        else:
            content = str(message)

        payload = {
            "msgtype": "markdown",
            "markdown": {"content": content},
        }

        try:
            response = requests.post(
                self.wecom_api_url,
                json=payload,
                timeout=self.REQUEST_TIMEOUT,
            )
            response.raise_for_status()
            self.logger.debug(f"Message sent successfully: {response.status_code}")
            return response.text
        except RequestException as e:
            self.logger.error(f"Failed to send WeChat Work message: {e}")
            raise

    def report_task(self, task_name: str = "", task_info: Union[str, Dict, Any] = None) -> None:
        """Report task completion to WeChat Work.

        Args:
            task_name: Name of the completed task
            task_info: Additional task information
        """
        # Use provided values or fall back to instance attributes
        final_task_name = task_name or self.task_name or "Unknown Task"
        final_task_info = task_info if task_info is not None else self.task_info

        message = {
            "用户名": self.user_name,
            "项目名": self.project_abbr,
            final_task_name: final_task_info,
        }
        self.send_message(message)
