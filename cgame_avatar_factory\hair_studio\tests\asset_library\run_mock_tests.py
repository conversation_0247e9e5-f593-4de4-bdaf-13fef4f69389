#!/usr/bin/env python
"""Test Runner for Mock-based Tests.

Runs the asset library tests that use mock data instead of real data.
"""

# Import built-in modules
import os
import sys
import unittest

# Add the project root to Python path for testing
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def run_mock_tests():
    """Run all mock-based tests."""
    print("=" * 80)
    print("HAIR STUDIO MOCK-BASED TESTS")
    print("=" * 80)
    print()

    # Test modules to run
    test_modules = [
        "test_asset_lib_config_core",
        "test_sub_type_logic",
        "test_sub_tab_switching",
    ]

    all_success = True

    for module_name in test_modules:
        print(f"Running {module_name}...")
        print("-" * 60)

        try:
            # Import the test module
            module = __import__(module_name, fromlist=[module_name])

            # Create test suite
            loader = unittest.TestLoader()
            suite = loader.loadTestsFromModule(module)

            # Run tests
            runner = unittest.TextTestRunner(verbosity=2)
            result = runner.run(suite)

            if not result.wasSuccessful():
                all_success = False
                print(f"❌ {module_name} had failures or errors")
            else:
                print(f"✅ {module_name} passed all tests")

        except Exception as e:
            print(f"❌ Error running {module_name}: {e}")
            all_success = False

        print()

    # Print summary
    print("=" * 80)
    if all_success:
        print("🎉 ALL MOCK-BASED TESTS PASSED!")
        print("✅ The tests are working correctly with mock data.")
    else:
        print("⚠️  SOME TESTS FAILED!")
        print("❌ Please check the failed tests and fix any issues.")

    return all_success


if __name__ == "__main__":
    success = run_mock_tests()
    sys.exit(0 if success else 1)
