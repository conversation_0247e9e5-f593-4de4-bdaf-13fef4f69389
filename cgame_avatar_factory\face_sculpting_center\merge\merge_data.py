# Import built-in modules
from functools import lru_cache
import json
import logging

# Import local modules
from cgame_avatar_factory.common.utils import utils


class MergeDataContext:
    """合并数据上下文，替代使用pydantic的版本"""

    def __init__(self, dna_infos=None):
        self._dna_infos = []

        if dna_infos is not None:
            self.dna_infos = dna_infos

    @property
    def dna_infos(self) -> list:
        """获取DNA信息"""
        return self._dna_infos

    @dna_infos.setter
    def dna_infos(self, value: list):
        """设置DNA信息并验证"""
        if not isinstance(value, list):
            raise TypeError(f"dna_infos must be a list, got {type(value)}")

        self._dna_infos = value

    def dict(self):
        """返回字典表示，模拟pydantic的dict方法"""
        return {
            "dna_infos": self.dna_infos,
        }

    def json(self):
        """返回JSON字符串表示，模拟pydantic的json方法"""
        return json.dumps(self.dict())

    @dna_infos.setter
    def dna_infos(self, value: list):
        """设置DNA信息并验证"""
        if not isinstance(value, list):
            raise TypeError(f"dna_infos must be a list, got {type(value)}")

        for dna_info in value:
            if dna_info.keys() != {"dna_file_path", "thumbnail_file_path", "dna_order"}:
                raise ValueError(f"invalid dna_info keys: {dna_info.keys()}")

            dna_file_path = dna_info["dna_file_path"]
            if not utils.path_available(dna_file_path):
                raise ValueError(f"invalid dna_file_path: {dna_file_path}")

            dna_thumbnail_path = dna_info["thumbnail_file_path"]
            if not utils.path_available(dna_thumbnail_path):
                raise ValueError(f"invalid dna_thumbnail_path: {dna_thumbnail_path}")

            dna_order = dna_info["dna_order"]
            if not isinstance(dna_order, int):
                raise ValueError(f"invalid dna_order: {dna_order}")

        self._dna_infos = value

    @property
    def dna_file_paths(self):
        """获取DNA文件路径列表"""
        return [dna_info["dna_file_path"] for dna_info in self.dna_infos]

    def update_dna_infos(self, dna_infos):
        """更新DNA信息"""
        self.dna_infos = dna_infos

    def save_to_json(self, file_path: str):
        """保存到JSON文件"""
        with open(file_path, "w") as file:
            data = self.dict()
            logging.getLogger(__name__).info(f"save merge data to {file_path}, data: {data}")
            json.dump(data, file)

    def load_from_json(self, file_path: str):
        """从JSON文件加载"""
        with open(file_path, "r") as f:
            data = json.load(f)
            logging.getLogger(__name__).info(f"load merge data from {file_path}, data: {data}")

            if "dna_infos" in data:
                self.update_dna_infos(data["dna_infos"])


@lru_cache(maxsize=None)
def get_merge_data_context() -> MergeDataContext:
    return MergeDataContext()
