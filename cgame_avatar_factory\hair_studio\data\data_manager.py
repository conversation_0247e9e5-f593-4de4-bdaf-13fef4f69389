"""Data Manager for Hair Studio.

This module provides data management for Hair Studio, loading assets from
specified paths and managing hair components and assets.
"""

# Import built-in modules
import hashlib
import logging
import os
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
import uuid

# Define constants locally to avoid circular imports
HAIR_TYPE_CARD = "card"
HAIR_TYPE_XGEN = "xgen"
HAIR_TYPE_CURVE = "curve"

DEFAULT_COMPONENT_WIDTH = 1.0
DEFAULT_COMPONENT_HEIGHT = 2.0
DEFAULT_XGEN_DENSITY = 1000
DEFAULT_XGEN_LENGTH = 5.0
DEFAULT_CURVE_THICKNESS = 0.1
DEFAULT_CURVE_SUBDIVISIONS = 8

PROPERTY_NAME = "name"
PROPERTY_VISIBLE = "visible"
PROPERTY_WIDTH = "width"
PROPERTY_HEIGHT = "height"
PROPERTY_DENSITY = "density"
PROPERTY_LENGTH = "length"
PROPERTY_THICKNESS = "thickness"
PROPERTY_SUBDIVISIONS = "subdivisions"

# Import local modules
# Import asset configuration constants from hair_studio constants
from cgame_avatar_factory.hair_studio.constants import SUPPORTED_MODEL_EXTENSIONS
from cgame_avatar_factory.hair_studio.constants import SUPPORTED_THUMBNAIL_EXTENSIONS

# Import asset configuration functions


def _find_reference_asset_in_directory(directory_path: str, logger) -> Optional[str]:
    """Find reference asset (head model) in a specific directory.

    Args:
        directory_path (str): Directory path to search in
        logger: Logger instance for logging

    Returns:
        Optional[str]: Path to the reference asset, or None if not found
    """
    # Supported reference model extensions
    reference_extensions = {".obj", ".fbx"}

    if not os.path.exists(directory_path):
        return None

    try:
        # List all files in the directory
        for filename in os.listdir(directory_path):
            file_path = os.path.join(directory_path, filename)

            # Skip if not a file
            if not os.path.isfile(file_path):
                continue

            # Check file extension
            _, ext = os.path.splitext(filename)
            if ext.lower() not in reference_extensions:
                continue

            # Check if filename contains "head" (case-insensitive)
            if "head" in filename.lower():
                logger.debug(f"Found reference asset in {directory_path}: {file_path}")
                return file_path

    except Exception as e:
        logger.warning(f"Error searching for reference asset in {directory_path}: {e}")

    return None


def _find_asset_in_directory(directory_path: str, directory_name: str) -> tuple:
    """Find model and thumbnail files in a directory.

    Args:
        directory_path (str): Path to the directory to search
        directory_name (str): Name of the directory (used for matching files)

    Returns:
        tuple: (model_path, thumbnail_path) or (None, None)
    """
    logger = logging.getLogger(__name__)

    # Look for 3D model file inside the directory with the same name
    model_path = None
    for ext in SUPPORTED_MODEL_EXTENSIONS:
        potential_model = os.path.join(directory_path, f"{directory_name}{ext}")
        if os.path.isfile(potential_model):
            model_path = potential_model
            break

    # Look for thumbnail file - first try inside the directory (new structure)
    thumbnail_path = None
    for ext in SUPPORTED_THUMBNAIL_EXTENSIONS:
        # New structure: thumbnail inside the directory
        potential_thumbnail = os.path.join(directory_path, f"{directory_name}{ext}")
        if os.path.isfile(potential_thumbnail):
            thumbnail_path = potential_thumbnail
            break

    # If no thumbnail found inside, try parent directory (old structure compatibility)
    if not thumbnail_path:
        parent_dir = os.path.dirname(directory_path)
        for ext in SUPPORTED_THUMBNAIL_EXTENSIONS:
            potential_thumbnail = os.path.join(parent_dir, f"{directory_name}{ext}")
            if os.path.isfile(potential_thumbnail):
                thumbnail_path = potential_thumbnail
                logger.debug(f"Found thumbnail in parent directory: {potential_thumbnail}")
                break

    return model_path, thumbnail_path


def _scan_assets_in_directory_with_subtype(
    directory_path: str,
    asset_type: str,
    sub_asset_type: str,
) -> List[Dict[str, Any]]:
    """Scan a directory for hair assets with known sub-type information.

    This is an optimized version that doesn't need to determine sub-type from path
    since the sub-type is already known from the configuration system.

    Args:
        directory_path (str): Path to the directory to scan
        asset_type (str): Type of assets (card, xgen, curve)
        sub_asset_type (str): Known sub-type (eyebrow, hair, beard)

    Returns:
        List[Dict[str, Any]]: List of asset dictionaries with file_path and thumbnail
    """
    # Always use this module's own logger
    logger = logging.getLogger(__name__)

    logger.info(f"Scanning directory: {directory_path} for {asset_type}/{sub_asset_type} assets (2 levels deep)")
    assets = []

    if not os.path.exists(directory_path):
        logger.warning(f"Directory does not exist: {directory_path}")
        return assets

    try:
        # First, find base reference in current directory
        base_reference = _find_reference_asset_in_directory(directory_path, logger)
        if not base_reference:
            logger.error(f"No reference asset found in base directory: {directory_path}")
            return assets  # Skip entire directory if no base reference

        logger.info(f"Found base reference: {base_reference}")

        # Collect all potential asset directories from both level 1 and level 2
        potential_asset_dirs = []

        # Level 1: Direct subdirectories
        for item in os.listdir(directory_path):
            item_path = os.path.join(directory_path, item)
            if os.path.isdir(item_path):
                potential_asset_dirs.append((item_path, item, base_reference))  # Add base reference

                # Level 2: Subdirectories within subdirectories
                try:
                    # Check if this level has its own reference (can override base)
                    level_reference = _find_reference_asset_in_directory(item_path, logger)
                    current_reference = level_reference if level_reference else base_reference

                    for sub_item in os.listdir(item_path):
                        sub_item_path = os.path.join(item_path, sub_item)
                        if os.path.isdir(sub_item_path):
                            potential_asset_dirs.append((sub_item_path, sub_item, current_reference))
                except (PermissionError, OSError) as e:
                    logger.warning(f"Cannot access subdirectory {item_path}: {e}")
                    continue

        # Check each potential directory for valid assets
        for dir_path, dir_name, reference_path in potential_asset_dirs:
            model_path, thumbnail_path = _find_asset_in_directory(dir_path, dir_name)

            # Only create asset if both thumbnail and model exist
            if thumbnail_path and model_path:
                # Generate unique ID using hash of model file path
                asset_id = hashlib.md5(model_path.encode()).hexdigest()[:8]

                # Create display name from directory name
                display_name = dir_name.replace("_", " ").title()

                # Create metadata with reference path
                metadata = {
                    "file_path": model_path,
                    "reference": reference_path,
                }

                asset = {
                    "id": f"{asset_type}_{asset_id}",
                    "name": display_name,
                    "asset_type": asset_type,
                    "sub_asset_type": sub_asset_type,  # Use provided sub-type (no path parsing needed)
                    "file_path": model_path,
                    "thumbnail": thumbnail_path,
                    "metadata": metadata,
                }
                assets.append(asset)

                logger.debug(
                    f"Found valid {sub_asset_type} asset: {display_name} "
                    f"(model: {model_path}, thumbnail: {thumbnail_path}, "
                    f"reference: {reference_path})",
                )

    except (PermissionError, OSError) as e:
        logger.error(f"Error scanning directory {directory_path}: {e}")

    logger.info(f"Found {len(assets)} {sub_asset_type} assets in {directory_path}")
    return assets


class MockHairComponent:
    """Mock hair component for testing and demonstration."""

    def __init__(
        self,
        component_id: str,
        name: str,
        hair_type: str,
        asset_id: str = None,
    ):
        """Initialize a mock hair component.

        Args:
            component_id (str): Unique identifier for the component
            name (str): Display name of the component
            hair_type (str): Type of hair (card, xgen, curve)
            asset_id (str, optional): ID of the source asset
        """
        self.id = component_id
        self.name = name
        self.type = hair_type
        self.asset_id = asset_id
        self.visible = True
        self.is_viewed = True  # Add is_viewed property for UI display

        # Type-specific properties
        if hair_type == HAIR_TYPE_CARD:
            self.width = DEFAULT_COMPONENT_WIDTH
            self.height = DEFAULT_COMPONENT_HEIGHT
        elif hair_type == HAIR_TYPE_XGEN:
            self.density = DEFAULT_XGEN_DENSITY
            self.length = DEFAULT_XGEN_LENGTH
        elif hair_type == HAIR_TYPE_CURVE:
            self.thickness = DEFAULT_CURVE_THICKNESS
            self.subdivisions = DEFAULT_CURVE_SUBDIVISIONS

    def to_dict(self) -> Dict[str, Any]:
        """Convert component to dictionary representation.

        Returns:
            Dict[str, Any]: Component data as dictionary
        """
        data = {
            "id": self.id,
            PROPERTY_NAME: self.name,
            "type": self.type,
            "asset_id": self.asset_id,
            PROPERTY_VISIBLE: self.visible,
            "is_viewed": self.is_viewed,
        }

        # Add type-specific properties
        if self.type == HAIR_TYPE_CARD:
            data[PROPERTY_WIDTH] = self.width
            data[PROPERTY_HEIGHT] = self.height
        elif self.type == HAIR_TYPE_XGEN:
            data[PROPERTY_DENSITY] = self.density
            data[PROPERTY_LENGTH] = self.length
        elif self.type == HAIR_TYPE_CURVE:
            data[PROPERTY_THICKNESS] = self.thickness
            data[PROPERTY_SUBDIVISIONS] = self.subdivisions

        return data


class DataManager:
    """Data manager for Hair Studio asset and component management."""

    def __init__(self):
        """Initialize the data manager."""
        self._components: List[MockHairComponent] = []
        self._assets: List[Dict[str, Any]] = []
        self._selected_component_id: Optional[str] = None

        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        self._logger.info("DataManager initialized")

        # Initialize with empty component list as per PRD requirements
        # Components should only be created when user drags assets from library

        # Load assets from configured paths
        self._load_assets_from_paths()

    def _load_assets_from_paths(self):
        """Load assets from configured directory paths with sub-type information."""
        self._assets.clear()

        # Use new asset library configuration to get paths with sub-type information
        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import get_asset_lib_config

        config = get_asset_lib_config()

        # For card type, get paths organized by sub-type
        if config.get_tab_count() > 0:
            # Load card assets with sub-type information
            for tab_key in config.get_tab_keys():
                tab_paths = config.get_tab_lib_paths(tab_key)
                for directory_path in tab_paths:
                    # Pass sub-type information to scanning function
                    assets = _scan_assets_in_directory_with_subtype(directory_path, "card", tab_key)
                    self._assets.extend(assets)
                    self._logger.debug(f"Loaded {len(assets)} {tab_key} assets from {directory_path}")

        # not Load other asset types (xgen, curve)

        self._logger.info(f"Loaded {len(self._assets)} total assets from all configured paths")

    def reload_assets(self):
        """Reload assets from configured paths."""
        self._load_assets_from_paths()

    def get_components(self, hair_type: str = None) -> List[Dict[str, Any]]:
        """Get all components, optionally filtered by hair type.

        Args:
            hair_type (str, optional): Filter by hair type

        Returns:
            List[Dict[str, Any]]: List of component dictionaries
        """
        components = self._components
        if hair_type:
            components = [c for c in components if c.type == hair_type]

        return [c.to_dict() for c in components]

    def get_component(self, component_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific component by ID.

        Args:
            component_id (str): ID of the component to retrieve

        Returns:
            Optional[Dict[str, Any]]: Component data or None if not found
        """
        for component in self._components:
            if component.id == component_id:
                return component.to_dict()
        return None

    def create_component(self, asset_id: str) -> Optional[Dict[str, Any]]:
        """Create a new component from an asset.

        Args:
            asset_id (str): ID of the asset to create component from

        Returns:
            Optional[Dict[str, Any]]: Created component data or None if failed
        """
        # Find the asset
        asset = None
        for a in self._assets:
            if a["id"] == asset_id:
                asset = a
                break

        if not asset:
            return None

        # Create new component
        component_id = str(uuid.uuid4())
        component_name = f"New {asset['name']}"

        component = MockHairComponent(
            component_id=component_id,
            name=component_name,
            hair_type=asset["asset_type"],
            asset_id=asset_id,
        )

        self._components.append(component)
        return component.to_dict()

    def update_component(self, component_id: str, **kwargs) -> bool:
        """Update a component's properties.

        Args:
            component_id (str): ID of the component to update
            **kwargs: Properties to update

        Returns:
            bool: True if update successful, False otherwise
        """
        for component in self._components:
            if component.id == component_id:
                # Update basic properties
                if PROPERTY_NAME in kwargs:
                    component.name = kwargs[PROPERTY_NAME]
                if PROPERTY_VISIBLE in kwargs:
                    component.visible = kwargs[PROPERTY_VISIBLE]
                if "is_viewed" in kwargs:
                    component.is_viewed = kwargs["is_viewed"]

                # Update type-specific properties
                if component.type == HAIR_TYPE_CARD:
                    if PROPERTY_WIDTH in kwargs:
                        component.width = kwargs[PROPERTY_WIDTH]
                    if PROPERTY_HEIGHT in kwargs:
                        component.height = kwargs[PROPERTY_HEIGHT]
                elif component.type == HAIR_TYPE_XGEN:
                    if PROPERTY_DENSITY in kwargs:
                        component.density = kwargs[PROPERTY_DENSITY]
                    if PROPERTY_LENGTH in kwargs:
                        component.length = kwargs[PROPERTY_LENGTH]
                elif component.type == HAIR_TYPE_CURVE:
                    if PROPERTY_THICKNESS in kwargs:
                        component.thickness = kwargs[PROPERTY_THICKNESS]
                    if PROPERTY_SUBDIVISIONS in kwargs:
                        component.subdivisions = kwargs[PROPERTY_SUBDIVISIONS]

                return True
        return False

    def delete_component(self, component_id: str) -> bool:
        """Delete a component.

        Args:
            component_id (str): ID of the component to delete

        Returns:
            bool: True if deletion successful, False otherwise
        """
        for i, component in enumerate(self._components):
            if component.id == component_id:
                del self._components[i]
                if self._selected_component_id == component_id:
                    self._selected_component_id = None
                return True
        return False

    def select_component(self, component_id: Optional[str]):
        """Select a component.

        Args:
            component_id (Optional[str]): ID of component to select, or None to clear selection
        """
        self._selected_component_id = component_id

    def get_selected_component_id(self) -> Optional[str]:
        """Get the currently selected component ID.

        Returns:
            Optional[str]: Selected component ID or None
        """
        return self._selected_component_id

    def get_assets(self, asset_type: str = None) -> List[Dict[str, Any]]:
        """Get all assets, optionally filtered by type.

        Args:
            asset_type (str, optional): Filter by asset type

        Returns:
            List[Dict[str, Any]]: List of asset dictionaries
        """
        assets = self._assets
        if asset_type:
            assets = [a for a in assets if a["asset_type"] == asset_type]

        return assets.copy()


# Backward compatibility alias
MockDataManager = DataManager
