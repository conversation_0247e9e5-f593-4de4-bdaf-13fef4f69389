# Import third-party modules
from qtpy import QtCore


def geo_animated_mixin(cls):
    """Amend widget's setGeometry() to apply smooth interpolation."""
    old_init = cls.__init__
    old_set_geometry = cls.setGeometry

    def _new_init(self, *args, **kwargs):
        old_init(self, *args, **kwargs)
        self.animation_enabled = True
        self.animation_duration = 500
        self.animation_curve = QtCore.QEasingCurve.InOutQuad
        self.animation_ignore_init = True

        self._animation_init_finished = False
        self._orig_set_geometry = self.setGeometry

    def _animated_set_geometry(self, rect):
        if self.animation_ignore_init and not self._animation_init_finished:
            old_set_geometry(self, rect)
            self._animation_init_finished = True
        elif self.animation_enabled:
            animation = QtCore.QPropertyAnimation(self, b"geometry")
            animation.setDuration(self.animation_duration)
            animation.setStartValue(self.geometry())
            animation.setEndValue(rect)
            animation.setEasingCurve(self.animation_curve)
            animation.start()
            self.animation = animation
        else:
            old_set_geometry(self, rect)

    setattr(cls, "__init__", _new_init)
    setattr(cls, "setGeometry", _animated_set_geometry)
    return cls
