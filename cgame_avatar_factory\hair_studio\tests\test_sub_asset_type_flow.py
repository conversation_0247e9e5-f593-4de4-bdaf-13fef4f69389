"""Test sub_asset_type data flow from data manager to Maya API."""

# Import built-in modules
import unittest
from unittest.mock import MagicMock
from unittest.mock import patch

# Import local modules
# Import the modules we need to test
from cgame_avatar_factory.hair_studio.data.models import HairAsset
from cgame_avatar_factory.hair_studio.maya_api import hair_operations


class TestSubAssetTypeFlow(unittest.TestCase):
    """Test that sub_asset_type flows correctly through the system."""

    def test_hair_asset_sub_asset_type_support(self):
        """Test that HairAsset properly handles sub_asset_type."""
        # Test creating HairAsset with sub_asset_type
        asset = HairAsset(
            id="test_001",
            name="Test Hair",
            asset_type="card",
            sub_asset_type="scalp",
            thumbnail="/path/to/thumb.jpg",
            metadata={"file_path": "/path/to/asset.fbx", "reference": "/path/to/ref.obj"},
        )

        # Verify the attribute is set
        self.assertEqual(asset.sub_asset_type, "scalp")

        # Test to_dict includes sub_asset_type
        asset_dict = asset.to_dict()
        self.assertIn("sub_asset_type", asset_dict)
        self.assertEqual(asset_dict["sub_asset_type"], "scalp")

        # Test from_dict preserves sub_asset_type
        recreated_asset = HairAsset.from_dict(asset_dict)
        self.assertEqual(recreated_asset.sub_asset_type, "scalp")

    def test_hair_asset_none_sub_asset_type(self):
        """Test that HairAsset handles None sub_asset_type correctly."""
        asset = HairAsset(
            id="test_002",
            name="Test Hair",
            asset_type="card",
            sub_asset_type=None,
        )

        self.assertIsNone(asset.sub_asset_type)

        asset_dict = asset.to_dict()
        self.assertIn("sub_asset_type", asset_dict)
        self.assertIsNone(asset_dict["sub_asset_type"])

    @patch("cgame_avatar_factory.hair_studio.maya_api.hair_operations.WrapHairMesh")
    @patch("cgame_avatar_factory.hair_studio.maya_api.hair_operations.get_texture_path_for_hair_asset")
    def test_create_hair_node_reads_sub_asset_type_correctly(self, mock_get_texture, mock_wrap_hair):
        """Test that create_hair_node reads sub_asset_type from correct location."""
        # Mock the dependencies
        mock_get_texture.return_value = "/path/to/texture.jpg"
        mock_hair_mesh = MagicMock()
        mock_hair_mesh.hair_target = "test_hair_node"
        mock_hair_mesh.hair_head_mesh = "test_head_node"
        mock_wrap_hair.return_value = mock_hair_mesh

        # Create test asset data with sub_asset_type at top level
        asset_data = {
            "name": "Test Hair Asset",
            "sub_asset_type": "scalp",  # This should be read correctly
            "metadata": {
                "file_path": "/path/to/hair.fbx",
                "reference": "/path/to/head.obj",
            },
        }

        # Call the function
        result = hair_operations.create_hair_node(asset_data)

        # Verify the function succeeded
        self.assertTrue(result.get("success"))

        # Verify WrapHairMesh was called with the correct sub_asset_type
        mock_wrap_hair.assert_called_once()
        call_args = mock_wrap_hair.call_args[0]  # Get positional arguments
        # The sub_asset_type should be the 6th argument (index 5)
        self.assertEqual(call_args[5], "scalp")

    @patch("cgame_avatar_factory.hair_studio.maya_api.hair_operations.WrapHairMesh")
    @patch("cgame_avatar_factory.hair_studio.maya_api.hair_operations.get_texture_path_for_hair_asset")
    def test_create_hair_node_handles_none_sub_asset_type(self, mock_get_texture, mock_wrap_hair):
        """Test that create_hair_node handles None sub_asset_type correctly."""
        # Mock the dependencies
        mock_get_texture.return_value = "/path/to/texture.jpg"
        mock_hair_mesh = MagicMock()
        mock_hair_mesh.hair_target = "test_hair_node"
        mock_hair_mesh.hair_head_mesh = "test_head_node"
        mock_wrap_hair.return_value = mock_hair_mesh

        # Create test asset data without sub_asset_type
        asset_data = {
            "name": "Test Hair Asset",
            # No sub_asset_type field
            "metadata": {
                "file_path": "/path/to/hair.fbx",
                "reference": "/path/to/head.obj",
            },
        }

        # Call the function
        result = hair_operations.create_hair_node(asset_data)

        # Verify the function succeeded
        self.assertTrue(result.get("success"))

        # Verify WrapHairMesh was called with None for sub_asset_type
        mock_wrap_hair.assert_called_once()
        call_args = mock_wrap_hair.call_args[0]  # Get positional arguments
        # The sub_asset_type should be None (6th argument, index 5)
        self.assertIsNone(call_args[5])


if __name__ == "__main__":
    unittest.main()
