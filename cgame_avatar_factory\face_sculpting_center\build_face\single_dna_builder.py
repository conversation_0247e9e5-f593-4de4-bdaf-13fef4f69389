# Import standard modules
# Import built-in modules
import os

# Import third-party modules
from dna_viewer import DNA
from dna_viewer import RigConfig

# Import local modules
from cgame_avatar_factory.common import constants as const
import cgame_avatar_factory.common.state.scene_state_manager as scene_state_mgr
import cgame_avatar_factory.common.utils.axis_align_utils as axis_align_utils
import cgame_avatar_factory.face_sculpting_center.accessibility.ref_image_plane_utils as ref_image_plane_utils
import cgame_avatar_factory.face_sculpting_center.build_face.material_create as mat
import cgame_avatar_factory.face_sculpting_center.utils.dna_utils as dna
import cgame_avatar_factory.face_sculpting_center.utils.mesh_util as mesh


class SingleDNABuilder:
    """Builder for handling single DNA file operations"""

    def __init__(self, progress_callback=None):
        """Initialize builder with optional progress callback

        Args:
            progress_callback: Optional callback function for progress updates
        """
        self.progress_callback = progress_callback

    def build(self, dna_file_path: str):
        """Build single DNA file

        Args:
            dna_file_path: Path to DNA file to process
        """
        if not os.path.exists(dna_file_path):
            raise FileNotFoundError(f"DNA file not found: {dna_file_path}")

        self._update_progress(10, "Preparing DNA file processing...")

        self._update_progress(30, "Loading DNA file...")

        source_dna_path = os.path.join(const.CONFIG_PATH, const.BASE_DNA_PATH)
        mesh_index_list = dna.get_mesh_index(source_dna_path)
        dna_obj = DNA(dna_file_path)

        self._update_progress(50, "Building model...")
        dna.build_base_mesh(dna_obj, config=RigConfig(meshes=mesh_index_list))

        self._update_progress(65, "Aligning axis...")
        imported_up_axis = "y" if dna_obj.coordinate_system == (0, 2, 4) else "z"
        axis_align_utils.align_object_to_scene_up_axis(
            const.HEAD_GRP,
            imported_up_axis,
        )

        self._update_progress(80, "Loading LODs...")
        mesh.load_lods_model()

        self._update_progress(90, "Applying materials...")
        mat.MaterialCreate(dna_path=dna_file_path)
        mesh.focus_camera_on_head()

        self._update_progress(100, "DNA processing completed")
        scene_state_mgr.save()
        mesh.set_layer_display_type(const.HEAD_LAYER_NAME, 2)
        ref_image_plane_utils.reset_view_access_history()

    def _update_progress(self, value: int, message: str):
        """Update progress if callback is available

        Args:
            value: Progress value (0-100)
            message: Progress message
        """
        if self.progress_callback:
            self.progress_callback(value, message)
