# Import built-in modules
import os

# Import third-party modules
import dayu_widgets
from dayu_widgets.divider import MDivider
from dayu_widgets.label import <PERSON><PERSON><PERSON><PERSON>
from dayu_widgets.line_edit import MLineEdit
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
import cgame_avatar_factory.common.constants as const
from cgame_avatar_factory.common.utils import utils
from cgame_avatar_factory.face_sculpting_center.ui.components.info_section import FileMultSelectSectionWidget
from cgame_avatar_factory.face_sculpting_center.ui.dna_lib import lib_util
from cgame_avatar_factory.face_sculpting_center.ui.dna_lib.lib_filter_widgets import TagSelectSectionWidget
from cgame_avatar_factory.face_sculpting_center.utils import export_utils
from cgame_avatar_factory.face_sculpting_center.utils import tag_util


class CharacterExportDialog(QtWidgets.QDialog):
    """Dialog for exporting character models to the library."""

    def __init__(self, parent=None):
        """Initialize the dialog.

        Args:
            parent (QWidget, optional): Parent widget. Defaults to None.
        """
        super().__init__(parent=parent)
        self.character_data = None
        self.setup_ui()
        self.setWindowTitle("导出模型至库中")

    def setup_ui(self):
        """Set up the user interface."""
        self.setup_layout()
        self.setup_widgets()
        self.setup_style()
        self.setup_signals()

    def setup_layout(self):
        """Set up the main vertical layout."""
        self.main_layout = QtWidgets.QVBoxLayout()
        self.main_layout.setContentsMargins(16, 16, 16, 16)
        self.main_layout.setSpacing(16)
        self.setLayout(self.main_layout)

    def setup_widgets(self):
        """Create and add widgets to the layout."""

        # Section: 角色规范
        role_spec_divider = MDivider("角色规范")
        role_spec_divider.setStyleSheet("font-size: 10pt;")
        self.main_layout.addWidget(role_spec_divider)
        mesh_name_list, mesh_topology_spec = export_utils.get_base_dna_info()
        spec_check_results = export_utils.check_topology_compliance(mesh_name_list, mesh_topology_spec)

        # 初始化表格，设置宽度、左对齐和默认字体
        mesh_info = "<table style='width:100%; font-size: 10pt;'>"
        mesh_info += (
            "<tr><td style='padding: 2px 5px; text-align: left;'>名称</td><td style='padding: 5px 30px; text-align:"
            " left;'>顶点数</td><td style='padding: 5px 30px; text-align: left;'>是否满足</td></tr>"
        )

        for model_name, info in mesh_topology_spec.items():
            vertex_count = info["vertex_count"]
            is_compliant = spec_check_results[model_name]
            color = "#90EE90" if is_compliant else "#CE565E"
            compliance_text = "满足" if is_compliant else "不满足"
            # 设置单元格左对齐和增加列间距
            line = (
                f"<tr><td style='padding: 5px 5px; text-align: left;'>{model_name}</td><td style='padding: 5px 30px;"
                f" text-align: left;'>{vertex_count}</td><td style='padding: 5px 30px; text-align: left; color:"
                f" {color};'>{compliance_text}</td></tr>"
            )
            mesh_info += line
        mesh_info += "</table>"
        tip_label = MLabel(
            f"{mesh_info}",
        )
        self.main_layout.addWidget(tip_label)

        # Section: 角色信息
        divider_character_info = MDivider("角色信息")
        divider_character_info.setStyleSheet("font-size: 10pt;")
        self.main_layout.addWidget(divider_character_info)
        # 模型贴图选择
        texture_options = [
            {"text": "跟随模型", "callback": lambda: export_utils.ExportType.FROM_MODEL},
            {"text": "指定路径", "path_selector": True, "hint_text": "请选择贴图文件"},
        ]
        self.tex_select_section = FileMultSelectSectionWidget(
            section_name="选择模型贴图获取方式",
            options=texture_options,
            default_index=0,
        )
        self.tex_select_section.setStyleSheet("font-size: 10pt;")
        self.main_layout.addWidget(self.tex_select_section)

        # 缩略图选择
        thumbnail_options = [
            {"text": "自动截取", "callback": lambda: export_utils.ExportType.FROM_MODEL},
            {"text": "指定路径", "path_selector": True, "hint_text": "请选择缩略图文件"},
        ]
        self.thumbnail_select_section = FileMultSelectSectionWidget(
            section_name="选择角色缩略图获取方式",
            options=thumbnail_options,
            default_index=0,
        )
        self.thumbnail_select_section.setStyleSheet("font-size: 10pt;")
        self.main_layout.addWidget(self.thumbnail_select_section)

        # 导出路径选择
        export_path_options = [
            {
                "text": "项目库",
                "callback": lambda: os.path.join(const.PROJECT_AVATAR_FACTORY_PATH, "lib"),
            },
            {
                "text": "公共库",
                "callback": lambda: os.path.join(const.PUBLIC_AVATAR_FACTORY_PATH, "lib"),
            },
            {
                "text": "个人库",
                "callback": lambda: lib_util.get_local_lib_path(),
            },
        ]
        self.export_path_select_section = FileMultSelectSectionWidget(
            section_name="导出路径",
            options=export_path_options,
            default_index=2,
        )
        self.export_path_select_section.setStyleSheet("font-size: 10pt;")
        self.main_layout.addWidget(self.export_path_select_section)

        character_name_layout = QtWidgets.QVBoxLayout()
        character_name_layout.setContentsMargins(9, 9, 9, 9)
        character_name_layout.setSpacing(10)

        tool_label = MLabel(text="角色名:")
        tool_label.setAlignment(QtCore.Qt.AlignLeft)
        tool_label.setFixedWidth(80)
        tool_label.setStyleSheet("font-size: 10pt;")

        self.character_name_edit = MLineEdit(text="")
        self.character_name_edit.setPlaceholderText("请填写角色名")
        self.character_name_edit.setMinimumWidth(280)
        self.character_name_edit.setStyleSheet("font-size: 10pt; height: 28px;")
        character_name_layout.addWidget(tool_label)
        character_name_layout.addWidget(self.character_name_edit)
        character_name_layout.addStretch(1)

        self.main_layout.addLayout(character_name_layout)

        all_tags = tag_util.get_all_tags()
        self.tag_section = TagSelectSectionWidget(all_tags, section_name="角色标签")
        self.main_layout.addWidget(self.tag_section)

        self.setup_confirm_cancel_section()

    def setup_style(self):
        """Set up the dialog style."""
        self.setMinimumSize(600, 650)
        self.setMaximumWidth(700)

    def setup_confirm_cancel_section(self):
        """Create confirm and cancel buttons section."""
        self.confirm_cancel_container = QtWidgets.QFrame()
        self.confirm_cancel_layout = QtWidgets.QHBoxLayout()
        self.confirm_cancel_container.setLayout(self.confirm_cancel_layout)

        self.confirm_button = dayu_widgets.MPushButton(parent=self)
        self.confirm_button.setText("确认")
        self.confirm_button.clicked.connect(self.accept)
        self.confirm_button.setMinimumWidth(100)
        self.cancel_button = dayu_widgets.MPushButton(parent=self)
        self.cancel_button.setText("取消")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setMinimumWidth(100)

        self.confirm_cancel_layout.addStretch(1)
        self.confirm_cancel_layout.addWidget(self.cancel_button)
        self.confirm_cancel_layout.addStretch(1)
        self.confirm_cancel_layout.addWidget(self.confirm_button)
        self.confirm_cancel_layout.addStretch(1)
        self.main_layout.addWidget(self.confirm_cancel_container)

    def setup_signals(self):
        """Connect signals to their respective slots."""
        self.tex_select_section.sig_file_select_button_clicked.connect(
            self.slot_select_texture_file,
        )
        self.thumbnail_select_section.sig_file_select_button_clicked.connect(
            self.slot_select_thumbnail_file,
        )
        self.export_path_select_section.sig_file_select_button_clicked.connect(
            self.slot_select_folder,
        )

    def select_file(self, filter="*.*"):
        """Open a file dialog to select a file.

        Args:
            filter (str, optional): File filter string. Defaults to "*.*".

        Returns:
            str: Selected file path.
        """
        file_path, _ = QtWidgets.QFileDialog.getOpenFileName(
            self,
            "选择文件",
            "",
            filter,
        )
        return file_path

    @QtCore.Slot()
    def slot_select_texture_file(self):
        """Slot to handle texture file selection."""
        filter_str = utils.generate_file_filter(
            const.SUPPORTED_THUMBNAIL_FILE_EXTENSIONS,
            filter_name="贴图文件",
        )
        file_path = self.select_file(filter=filter_str)
        if not file_path:
            return
        self.tex_select_section.set_file_path(file_path)

    @QtCore.Slot()
    def slot_select_thumbnail_file(self):
        """Slot to handle thumbnail file selection."""
        filter_str = utils.generate_file_filter(
            const.SUPPORTED_THUMBNAIL_FILE_EXTENSIONS,
            filter_name="缩略图",
        )
        file_path = self.select_file(filter=filter_str)
        if not file_path:
            return
        self.thumbnail_select_section.set_file_path(file_path)

    @QtCore.Slot()
    def slot_select_folder(self):
        """Slot to handle folder selection."""
        file_path = self.select_folder()
        if not file_path:
            return
        self.export_path_select_section.set_file_path(file_path)

    @property
    def dna_data(self):
        """Get the DNA data collected from the dialog.

        Returns:
            dict: DNA data dictionary.
        """
        return self.character_data

    def accept(self):
        """Validate inputs and accept the dialog if valid."""
        self.character_data = {}

        mesh_name_list, mesh_topology_spec = export_utils.get_base_dna_info()
        # check topology
        check_results = export_utils.check_topology_compliance(mesh_name_list, mesh_topology_spec)
        for obj_name, is_valid in check_results.items():
            if not is_valid:
                dayu_widgets.MToast.error("拓扑不符合规范！", parent=self)
                return
        texture_path = self.tex_select_section.get_file_path()
        if not texture_path:
            dayu_widgets.MToast.error("贴图文件未选择", parent=self)
            return
        self.character_data["texture_file_path"] = texture_path

        thumbnail_file_path = self.thumbnail_select_section.get_file_path()
        if not thumbnail_file_path:
            dayu_widgets.MToast.error("角色缩略图未选择", parent=self)
            return
        self.character_data["thumbnail_file_path"] = thumbnail_file_path

        export_root = self.export_path_select_section.get_file_path()
        if not export_root or export_root == "":
            export_type_name = self.export_path_select_section.get_mode_content()
            dayu_widgets.MToast.error(f"请检查{export_type_name}路径", parent=self)
            return
        self.character_data["export_root"] = export_root

        character_name = self.character_name_edit.text()
        if not character_name:
            dayu_widgets.MToast.error("角色名未选择", parent=self)
            return
        self.character_data["character_name"] = character_name

        self.character_data["tags"] = self.tag_section.selected_tags

        super().accept()
